-- سكريبت مبسط لإدراج بيانات عربية تجريبية
-- Simple Arabic Sample Data Script

USE AwqafManagement;
GO

-- حذف البيانات الموجودة
DELETE FROM ChartOfAccounts;
DBCC CHECKIDENT ('ChartOfAccounts', RESEED, 0);
GO

-- إدراج الحسابات مع الأعمدة الموجودة فقط
SET IDENTITY_INSERT ChartOfAccounts ON;

-- الحسابات الرئيسية والفرعية
INSERT INTO ChartOfAccounts (AccountId, AccountCode, AccountNameAr, AccountTypeId, AccountGroupId, ParentAccountId, AccountLevel, Nature, IsParent, IsActive, AllowPosting, OpeningBalance, Description, CreatedDate, ModifiedDate)
VALUES 
-- 1. الأصول (Assets)
(1, N'1.00.000', N'الأصول', 1, 1, NULL, 0, 0, 1, 1, 0, 0.00, N'مجموعة الأصول الرئيسية', GETDATE(), GETDATE()),
(2, N'1.01.000', N'الأصول المتداولة', 1, 1, 1, 1, 0, 1, 1, 0, 0.00, N'الأصول قصيرة الأجل', GETDATE(), GETDATE()),
(3, N'1.01.001', N'الصندوق الرئيسي', 1, 1, 2, 2, 0, 0, 1, 1, 50000.00, N'صندوق النقدية الرئيسي', GETDATE(), GETDATE()),
(4, N'1.01.002', N'البنك الأهلي التجاري', 1, 1, 2, 2, 0, 0, 1, 1, 250000.00, N'حساب البنك الأهلي التجاري', GETDATE(), GETDATE()),
(5, N'1.01.003', N'بنك الراجحي', 1, 1, 2, 2, 0, 0, 1, 1, 180000.00, N'حساب بنك الراجحي', GETDATE(), GETDATE()),
(6, N'1.01.004', N'بنك الرياض', 1, 1, 2, 2, 0, 0, 1, 1, 120000.00, N'حساب بنك الرياض', GETDATE(), GETDATE()),
(7, N'1.01.005', N'العملاء والذمم المدينة', 1, 1, 2, 2, 0, 1, 1, 0, 0.00, N'مجموعة العملاء والذمم المدينة', GETDATE(), GETDATE()),
(8, N'1.01.006', N'عميل - شركة الأوقاف العقارية', 1, 1, 7, 2, 0, 0, 1, 1, 75000.00, N'شركة الأوقاف العقارية', GETDATE(), GETDATE()),
(9, N'1.01.007', N'عميل - مؤسسة الخير الإسلامية', 1, 1, 7, 2, 0, 0, 1, 1, 45000.00, N'مؤسسة الخير الإسلامية', GETDATE(), GETDATE()),

-- الأصول الثابتة
(10, N'1.02.000', N'الأصول الثابتة', 1, 1, 1, 1, 0, 1, 1, 0, 0.00, N'الأصول طويلة الأجل', GETDATE(), GETDATE()),
(11, N'1.02.001', N'الأراضي والعقارات', 1, 1, 10, 2, 0, 0, 1, 1, 2500000.00, N'الأراضي والعقارات الوقفية', GETDATE(), GETDATE()),
(12, N'1.02.002', N'المباني والإنشاءات', 1, 1, 10, 2, 0, 0, 1, 1, 1800000.00, N'المباني والإنشاءات الوقفية', GETDATE(), GETDATE()),
(13, N'1.02.003', N'الأثاث والمعدات', 1, 1, 10, 2, 0, 0, 1, 1, 150000.00, N'الأثاث والمعدات المكتبية', GETDATE(), GETDATE()),

-- 2. الخصوم (Liabilities)
(14, N'2.00.000', N'الخصوم', 2, 2, NULL, 0, 1, 1, 1, 0, 0.00, N'مجموعة الخصوم الرئيسية', GETDATE(), GETDATE()),
(15, N'2.01.000', N'الخصوم المتداولة', 2, 2, 14, 1, 1, 1, 1, 0, 0.00, N'الخصوم قصيرة الأجل', GETDATE(), GETDATE()),
(16, N'2.01.001', N'الموردون والذمم الدائنة', 2, 2, 15, 2, 1, 0, 1, 1, 25000.00, N'الموردون والذمم الدائنة', GETDATE(), GETDATE()),
(17, N'2.01.002', N'المصروفات المستحقة', 2, 2, 15, 2, 1, 0, 1, 1, 15000.00, N'المصروفات المستحقة الدفع', GETDATE(), GETDATE()),

-- 3. حقوق الملكية (Equity)
(18, N'3.00.000', N'حقوق الملكية', 3, 3, NULL, 0, 1, 1, 1, 0, 0.00, N'مجموعة حقوق الملكية', GETDATE(), GETDATE()),
(19, N'3.01.001', N'رأس المال الوقفي', 3, 3, 18, 2, 1, 0, 1, 1, 5000000.00, N'رأس المال الوقفي الأساسي', GETDATE(), GETDATE()),
(20, N'3.01.002', N'الأرباح المحتجزة', 3, 3, 18, 2, 1, 0, 1, 1, 500000.00, N'الأرباح المحتجزة من السنوات السابقة', GETDATE(), GETDATE()),

-- 4. الإيرادات (Revenues)
(21, N'4.00.000', N'الإيرادات', 4, 4, NULL, 0, 1, 1, 1, 0, 0.00, N'مجموعة الإيرادات الرئيسية', GETDATE(), GETDATE()),
(22, N'4.01.001', N'إيرادات الإيجارات', 4, 4, 21, 2, 1, 0, 1, 1, 0.00, N'إيرادات إيجار العقارات الوقفية', GETDATE(), GETDATE()),
(23, N'4.01.002', N'إيرادات الاستثمارات', 4, 4, 21, 2, 1, 0, 1, 1, 0.00, N'إيرادات الاستثمارات المالية', GETDATE(), GETDATE()),
(24, N'4.01.003', N'التبرعات والهبات', 4, 4, 21, 2, 1, 0, 1, 1, 0.00, N'التبرعات والهبات الواردة', GETDATE(), GETDATE()),

-- 5. المصروفات (Expenses)
(25, N'5.00.000', N'المصروفات', 5, 5, NULL, 0, 0, 1, 1, 0, 0.00, N'مجموعة المصروفات الرئيسية', GETDATE(), GETDATE()),
(26, N'5.01.001', N'مصروفات الصيانة', 5, 5, 25, 2, 0, 0, 1, 1, 0.00, N'مصروفات صيانة العقارات', GETDATE(), GETDATE()),
(27, N'5.01.002', N'الرواتب والأجور', 5, 5, 25, 2, 0, 0, 1, 1, 0.00, N'رواتب وأجور الموظفين', GETDATE(), GETDATE()),
(28, N'5.01.003', N'المصروفات الإدارية', 5, 5, 25, 2, 0, 0, 1, 1, 0.00, N'المصروفات الإدارية العامة', GETDATE(), GETDATE()),
(29, N'5.01.004', N'مصروفات الكهرباء والماء', 5, 5, 25, 2, 0, 0, 1, 1, 0.00, N'مصروفات الكهرباء والماء والهاتف', GETDATE(), GETDATE()),
(30, N'5.01.005', N'مصروفات التأمين', 5, 5, 25, 2, 0, 0, 1, 1, 0.00, N'مصروفات التأمين على العقارات', GETDATE(), GETDATE());

SET IDENTITY_INSERT ChartOfAccounts OFF;
GO

-- عرض النتائج
SELECT 
    AccountCode as 'رمز الحساب',
    AccountNameAr as 'اسم الحساب بالعربية',
    CASE AccountLevel 
        WHEN 0 THEN N'رئيسي'
        WHEN 1 THEN N'فرعي'
        WHEN 2 THEN N'تفصيلي'
    END as 'مستوى الحساب',
    CASE Nature 
        WHEN 0 THEN N'مدين'
        WHEN 1 THEN N'دائن'
    END as 'طبيعة الحساب',
    CASE IsParent 
        WHEN 1 THEN N'نعم'
        WHEN 0 THEN N'لا'
    END as 'حساب أب',
    OpeningBalance as 'الرصيد الافتتاحي'
FROM ChartOfAccounts 
ORDER BY AccountCode;

PRINT N'تم إدراج 30 حساب محاسبي بنجاح مع ترميز عربي محسن';
