-- ===================================================================
-- إنشاء جداول النظام المحاسبي - الدليل المحاسبي
-- تاريخ الإنشاء: 2025-01-07
-- الوصف: جداول الحسابات الرئيسية والفرعية مع الهيكل الشجري
-- ===================================================================

USE AwqafManagement;
GO

-- ===================================================================
-- جدول أنواع الحسابات الرئيسية (الأصول، الخصوم، حقوق الملكية، الإيرادات، المصروفات)
-- ===================================================================
CREATE TABLE AccountTypes (
    AccountTypeId INT IDENTITY(1,1) PRIMARY KEY,
    AccountTypeCode NVARCHAR(10) NOT NULL UNIQUE,
    AccountTypeName NVARCHAR(100) NOT NULL,
    AccountTypeNameAr NVARCHAR(100) NOT NULL,
    Description NVARCHAR(500),
    DisplayOrder INT NOT NULL DEFAULT 0,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    CreatedBy INT,
    ModifiedDate DATETIME2,
    ModifiedBy INT,
    
    CONSTRAINT FK_AccountTypes_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(UserId),
    CONSTRAINT FK_AccountTypes_ModifiedBy FOREIGN KEY (ModifiedBy) REFERENCES Users(UserId)
);

-- ===================================================================
-- جدول المجموعات الرئيسية للحسابات
-- ===================================================================
CREATE TABLE AccountGroups (
    AccountGroupId INT IDENTITY(1,1) PRIMARY KEY,
    AccountTypeId INT NOT NULL,
    GroupCode NVARCHAR(20) NOT NULL UNIQUE,
    GroupName NVARCHAR(150) NOT NULL,
    GroupNameAr NVARCHAR(150) NOT NULL,
    Description NVARCHAR(500),
    DisplayOrder INT NOT NULL DEFAULT 0,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    CreatedBy INT,
    ModifiedDate DATETIME2,
    ModifiedBy INT,
    
    CONSTRAINT FK_AccountGroups_AccountType FOREIGN KEY (AccountTypeId) REFERENCES AccountTypes(AccountTypeId),
    CONSTRAINT FK_AccountGroups_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(UserId),
    CONSTRAINT FK_AccountGroups_ModifiedBy FOREIGN KEY (ModifiedBy) REFERENCES Users(UserId)
);

-- ===================================================================
-- جدول الحسابات الرئيسية والفرعية (هيكل شجري)
-- ===================================================================
CREATE TABLE ChartOfAccounts (
    AccountId INT IDENTITY(1,1) PRIMARY KEY,
    AccountCode NVARCHAR(50) NOT NULL UNIQUE,
    AccountName NVARCHAR(200) NOT NULL,
    AccountNameAr NVARCHAR(200) NOT NULL,
    AccountTypeId INT NOT NULL,
    AccountGroupId INT,
    ParentAccountId INT NULL, -- للحسابات الفرعية
    AccountLevel INT NOT NULL DEFAULT 1, -- مستوى الحساب في الشجرة
    IsParent BIT NOT NULL DEFAULT 0, -- هل الحساب أب لحسابات أخرى
    IsActive BIT NOT NULL DEFAULT 1,
    AllowPosting BIT NOT NULL DEFAULT 1, -- هل يسمح بالترحيل المباشر
    Description NVARCHAR(500),
    
    -- معلومات إضافية للحساب
    CurrencyCode NVARCHAR(10) DEFAULT 'SAR',
    OpeningBalance DECIMAL(18,4) DEFAULT 0,
    CurrentBalance DECIMAL(18,4) DEFAULT 0,
    
    -- معلومات التدقيق
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    CreatedBy INT,
    ModifiedDate DATETIME2,
    ModifiedBy INT,
    
    CONSTRAINT FK_ChartOfAccounts_AccountType FOREIGN KEY (AccountTypeId) REFERENCES AccountTypes(AccountTypeId),
    CONSTRAINT FK_ChartOfAccounts_AccountGroup FOREIGN KEY (AccountGroupId) REFERENCES AccountGroups(AccountGroupId),
    CONSTRAINT FK_ChartOfAccounts_Parent FOREIGN KEY (ParentAccountId) REFERENCES ChartOfAccounts(AccountId),
    CONSTRAINT FK_ChartOfAccounts_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(UserId),
    CONSTRAINT FK_ChartOfAccounts_ModifiedBy FOREIGN KEY (ModifiedBy) REFERENCES Users(UserId)
);

-- ===================================================================
-- جدول مراكز التكلفة
-- ===================================================================
CREATE TABLE CostCenters (
    CostCenterId INT IDENTITY(1,1) PRIMARY KEY,
    CostCenterCode NVARCHAR(20) NOT NULL UNIQUE,
    CostCenterName NVARCHAR(150) NOT NULL,
    CostCenterNameAr NVARCHAR(150) NOT NULL,
    ParentCostCenterId INT NULL,
    Description NVARCHAR(500),
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    CreatedBy INT,
    ModifiedDate DATETIME2,
    ModifiedBy INT,
    
    CONSTRAINT FK_CostCenters_Parent FOREIGN KEY (ParentCostCenterId) REFERENCES CostCenters(CostCenterId),
    CONSTRAINT FK_CostCenters_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(UserId),
    CONSTRAINT FK_CostCenters_ModifiedBy FOREIGN KEY (ModifiedBy) REFERENCES Users(UserId)
);

-- ===================================================================
-- إنشاء الفهارس لتحسين الأداء
-- ===================================================================

-- فهارس جدول أنواع الحسابات
CREATE INDEX IX_AccountTypes_Code ON AccountTypes(AccountTypeCode);
CREATE INDEX IX_AccountTypes_Active ON AccountTypes(IsActive);

-- فهارس جدول مجموعات الحسابات
CREATE INDEX IX_AccountGroups_Type ON AccountGroups(AccountTypeId);
CREATE INDEX IX_AccountGroups_Code ON AccountGroups(GroupCode);
CREATE INDEX IX_AccountGroups_Active ON AccountGroups(IsActive);

-- فهارس جدول دليل الحسابات
CREATE INDEX IX_ChartOfAccounts_Code ON ChartOfAccounts(AccountCode);
CREATE INDEX IX_ChartOfAccounts_Type ON ChartOfAccounts(AccountTypeId);
CREATE INDEX IX_ChartOfAccounts_Group ON ChartOfAccounts(AccountGroupId);
CREATE INDEX IX_ChartOfAccounts_Parent ON ChartOfAccounts(ParentAccountId);
CREATE INDEX IX_ChartOfAccounts_Level ON ChartOfAccounts(AccountLevel);
CREATE INDEX IX_ChartOfAccounts_Active ON ChartOfAccounts(IsActive);

-- فهارس جدول مراكز التكلفة
CREATE INDEX IX_CostCenters_Code ON CostCenters(CostCenterCode);
CREATE INDEX IX_CostCenters_Parent ON CostCenters(ParentCostCenterId);
CREATE INDEX IX_CostCenters_Active ON CostCenters(IsActive);

PRINT 'تم إنشاء جداول النظام المحاسبي بنجاح';
