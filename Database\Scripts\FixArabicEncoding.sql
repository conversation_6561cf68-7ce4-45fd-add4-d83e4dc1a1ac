-- ===================================================================
-- إصلاح مشكلة ترميز اللغة العربية
-- Fix Arabic Encoding Issues
-- ===================================================================

USE AwqafManagement;
GO

PRINT N'=== إصلاح ترميز اللغة العربية ===';
PRINT '=== Fixing Arabic Encoding ===';
PRINT '';

-- ===================================================================
-- 1. حذف البيانات الموجودة مع الترميز الخاطئ
-- ===================================================================
PRINT N'1. حذف البيانات الموجودة...';

DELETE FROM ChartOfAccounts;
DELETE FROM AccountGroups WHERE AccountGroupId > 7; -- الاحتفاظ بالمجموعات الأساسية فقط
DELETE FROM AccountTypes WHERE AccountTypeId > 5; -- الاحتفاظ بالأنواع الأساسية فقط

PRINT N'✓ تم حذف البيانات القديمة';

-- ===================================================================
-- 2. إعادة إدراج البيانات بترميز صحيح
-- ===================================================================
PRINT N'2. إدراج البيانات بترميز صحيح...';

-- إدراج الحسابات مع ترميز عربي صحيح - المرحلة الأولى (الحسابات الرئيسية)
INSERT INTO ChartOfAccounts (
    AccountCode, AccountName, AccountNameAr, AccountTypeId, AccountGroupId,
    ParentAccountId, AccountLevel, IsParent, IsActive, AllowPosting,
    CurrencyCode, OpeningBalance, Description, CreatedDate
) VALUES
-- الحسابات الرئيسية (بدون parent)
(N'1.00.000', N'Assets', N'الأصول', 1, 1, NULL, 1, 1, 1, 0, N'SAR', 0, N'جميع أصول المؤسسة', GETDATE()),
(N'2.00.000', N'Liabilities', N'الخصوم', 2, 3, NULL, 1, 1, 1, 0, N'SAR', 0, N'جميع التزامات المؤسسة', GETDATE()),
(N'3.00.000', N'Equity', N'حقوق الملكية', 3, 5, NULL, 1, 1, 1, 0, N'SAR', 0, N'حقوق أصحاب المؤسسة', GETDATE()),
(N'4.00.000', N'Revenue', N'الإيرادات', 4, 6, NULL, 1, 1, 1, 0, N'SAR', 0, N'جميع إيرادات المؤسسة', GETDATE()),
(N'5.00.000', N'Expenses', N'المصروفات', 5, 7, NULL, 1, 1, 1, 0, N'SAR', 0, N'جميع مصروفات المؤسسة', GETDATE());

-- المرحلة الثانية (الحسابات الفرعية)
INSERT INTO ChartOfAccounts (
    AccountCode, AccountName, AccountNameAr, AccountTypeId, AccountGroupId,
    ParentAccountId, AccountLevel, IsParent, IsActive, AllowPosting,
    CurrencyCode, OpeningBalance, Description, CreatedDate
) VALUES
(N'1.01.000', N'Current Assets', N'الأصول المتداولة', 1, 1,
    (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = N'1.00.000'), 2, 1, 1, 0, N'SAR', 0, N'الأصول قصيرة الأجل', GETDATE()),
(N'1.02.000', N'Fixed Assets', N'الأصول الثابتة', 1, 2,
    (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = N'1.00.000'), 2, 1, 1, 0, N'SAR', 0, N'الأصول طويلة الأجل', GETDATE()),
(N'2.01.000', N'Current Liabilities', N'الخصوم المتداولة', 2, 3,
    (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = N'2.00.000'), 2, 1, 1, 0, N'SAR', 0, N'الالتزامات قصيرة الأجل', GETDATE());

-- المرحلة الثالثة (الحسابات التفصيلية)
INSERT INTO ChartOfAccounts (
    AccountCode, AccountName, AccountNameAr, AccountTypeId, AccountGroupId,
    ParentAccountId, AccountLevel, IsParent, IsActive, AllowPosting,
    CurrencyCode, OpeningBalance, Description, CreatedDate
) VALUES
(N'1.01.001', N'Cash in Hand', N'النقدية في الصندوق', 1, 1,
    (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = N'1.01.000'), 3, 0, 1, 1, N'SAR', 50000, N'النقد المتوفر في الصندوق', GETDATE()),
(N'1.01.002', N'Bank - Current Account', N'البنك - الحساب الجاري', 1, 1,
    (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = N'1.01.000'), 3, 0, 1, 1, N'SAR', 250000, N'الحساب الجاري في البنك', GETDATE()),
(N'1.01.003', N'Accounts Receivable', N'العملاء والمدينون', 1, 1,
    (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = N'1.01.000'), 3, 0, 1, 1, N'SAR', 75000, N'المبالغ المستحقة من العملاء', GETDATE()),
(N'1.02.001', N'Buildings', N'المباني', 1, 2,
    (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = N'1.02.000'), 3, 0, 1, 1, N'SAR', 2000000, N'المباني والعقارات', GETDATE()),
(N'1.02.002', N'Equipment', N'المعدات', 1, 2,
    (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = N'1.02.000'), 3, 0, 1, 1, N'SAR', 150000, N'المعدات والأجهزة', GETDATE()),
(N'2.01.001', N'Accounts Payable', N'الموردون والدائنون', 2, 3,
    (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = N'2.01.000'), 3, 0, 1, 1, N'SAR', 45000, N'المبالغ المستحقة للموردين', GETDATE()),
(N'2.01.002', N'Accrued Expenses', N'المصروفات المستحقة', 2, 3,
    (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = N'2.01.000'), 3, 0, 1, 1, N'SAR', 15000, N'المصروفات المستحقة الدفع', GETDATE()),
(N'3.01.001', N'Paid-up Capital', N'رأس المال المدفوع', 3, 5,
    (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = N'3.00.000'), 2, 0, 1, 1, N'SAR', 1000000, N'رأس المال المدفوع فعلياً', GETDATE()),
(N'3.02.001', N'Retained Earnings', N'الأرباح المحتجزة', 3, 5,
    (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = N'3.00.000'), 2, 0, 1, 1, N'SAR', 500000, N'الأرباح المحتجزة من السنوات السابقة', GETDATE()),
(N'4.01.001', N'Sales Revenue', N'إيرادات المبيعات', 4, 6,
    (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = N'4.00.000'), 2, 0, 1, 1, N'SAR', 0, N'إيرادات من بيع البضائع والخدمات', GETDATE()),
(N'4.02.001', N'Other Income', N'إيرادات أخرى', 4, 6,
    (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = N'4.00.000'), 2, 0, 1, 1, N'SAR', 0, N'الإيرادات الأخرى المتنوعة', GETDATE()),
(N'5.01.001', N'Salaries and Wages', N'رواتب وأجور', 5, 7,
    (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = N'5.00.000'), 2, 0, 1, 1, N'SAR', 0, N'رواتب وأجور الموظفين', GETDATE()),
(N'5.02.001', N'Office Expenses', N'مصروفات إدارية', 5, 7,
    (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = N'5.00.000'), 2, 0, 1, 1, N'SAR', 0, N'المصروفات الإدارية والعامة', GETDATE()),
(N'5.03.001', N'Utilities', N'المرافق العامة', 5, 7,
    (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = N'5.00.000'), 2, 0, 1, 1, N'SAR', 0, N'فواتير الكهرباء والماء والهاتف', GETDATE());

PRINT N'✓ تم إدراج الحسابات بترميز صحيح';

-- ===================================================================
-- 3. التحقق من النتائج
-- ===================================================================
PRINT N'3. التحقق من النتائج...';

DECLARE @AccountsCount INT = (SELECT COUNT(*) FROM ChartOfAccounts WHERE IsActive = 1);
PRINT N'عدد الحسابات الجديدة: ' + CAST(@AccountsCount AS NVARCHAR(10));

-- عرض عينة من الحسابات
PRINT N'';
PRINT N'=== عينة من الحسابات الجديدة ===';

SELECT TOP 10
    AccountCode as N'رمز الحساب',
    AccountNameAr as N'اسم الحساب بالعربية',
    AccountName as N'اسم الحساب بالإنجليزية',
    AccountLevel as N'المستوى',
    CASE WHEN IsParent = 1 THEN N'نعم' ELSE N'لا' END as N'حساب أب',
    OpeningBalance as N'الرصيد الافتتاحي'
FROM ChartOfAccounts 
WHERE IsActive = 1 
ORDER BY AccountCode;

PRINT N'';
PRINT N'=== تم إصلاح مشكلة الترميز بنجاح ===';
PRINT '=== Arabic encoding fixed successfully ===';
