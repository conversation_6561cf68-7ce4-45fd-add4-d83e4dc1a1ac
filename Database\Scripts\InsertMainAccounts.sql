-- إدراج الحسابات الرئيسية والفرعية
-- Insert Main and Sub Accounts

USE AwqafManagement;
GO

-- حذف البيانات الموجودة وإعادة تعيين الهوية
DELETE FROM ChartOfAccounts;
DBCC CHECKIDENT ('ChartOfAccounts', RESEED, 0);
GO

-- إدراج الحسابات الرئيسية والفرعية
SET IDENTITY_INSERT ChartOfAccounts ON;

-- ===== 1. الأصول (Assets) =====
INSERT INTO ChartOfAccounts (AccountId, AccountCode, AccountNameAr, AccountNameEn, ParentAccountId, LevelType, Nature, AccountGroupId, Status, Currency, Description, AllowPosting, AllowDirectEntry, OpeningBalance, OpeningBalanceNature, AccountLevel, IsParent, CreatedDate)
VALUES 
-- 1.0 الأصول - الحساب الرئيسي
(1, N'1.00.000', N'الأصول', N'Assets', NULL, 1, 1, 1, 1, 1, N'مجموعة الأصول الرئيسية', 0, 0, 0.00, 1, 1, 1, GETDATE()),

-- 1.1 الأصول المتداولة
(2, N'1.01.000', N'الأصول المتداولة', N'Current Assets', 1, 2, 1, 1, 1, 1, N'الأصول قصيرة الأجل', 0, 0, 0.00, 1, 2, 1, GETDATE()),

-- 1.1.1 النقدية والبنوك
(3, N'1.01.001', N'الصندوق الرئيسي', N'Main Cash Box', 2, 3, 1, 1, 1, 1, N'صندوق النقدية الرئيسي', 1, 1, 50000.00, 1, 3, 0, GETDATE()),
(4, N'1.01.002', N'صندوق فرعي', N'Sub Cash Box', 2, 3, 1, 1, 1, 1, N'صندوق نقدية فرعي', 1, 1, 15000.00, 1, 3, 0, GETDATE()),
(5, N'1.01.003', N'البنك الأهلي التجاري', N'National Commercial Bank', 2, 3, 1, 1, 1, 1, N'حساب البنك الأهلي التجاري', 1, 1, 250000.00, 1, 3, 0, GETDATE()),
(6, N'1.01.004', N'بنك الراجحي', N'Al Rajhi Bank', 2, 3, 1, 1, 1, 1, N'حساب بنك الراجحي', 1, 1, 180000.00, 1, 3, 0, GETDATE()),
(7, N'1.01.005', N'بنك الرياض', N'Riyad Bank', 2, 3, 1, 1, 1, 1, N'حساب بنك الرياض', 1, 1, 120000.00, 1, 3, 0, GETDATE()),
(8, N'1.01.006', N'بنك سامبا', N'Samba Bank', 2, 3, 1, 1, 1, 1, N'حساب بنك سامبا', 1, 1, 95000.00, 1, 3, 0, GETDATE()),

-- 1.1.2 العملاء والذمم المدينة
(9, N'1.01.010', N'العملاء والذمم المدينة', N'Accounts Receivable', 2, 3, 1, 1, 1, 1, N'مجموعة العملاء والذمم المدينة', 0, 0, 0.00, 1, 3, 1, GETDATE()),
(10, N'1.01.011', N'عميل - شركة الأوقاف العقارية', N'Client - Real Estate Endowments Co.', 9, 3, 1, 1, 1, 1, N'شركة الأوقاف العقارية', 1, 1, 75000.00, 1, 3, 0, GETDATE()),
(11, N'1.01.012', N'عميل - مؤسسة الخير الإسلامية', N'Client - Islamic Charity Foundation', 9, 3, 1, 1, 1, 1, N'مؤسسة الخير الإسلامية', 1, 1, 45000.00, 1, 3, 0, GETDATE()),
(12, N'1.01.013', N'عميل - جمعية البر الخيرية', N'Client - Charity Association', 9, 3, 1, 1, 1, 1, N'جمعية البر الخيرية', 1, 1, 32000.00, 1, 3, 0, GETDATE()),

-- 1.1.3 المخزون
(13, N'1.01.020', N'المخزون', N'Inventory', 2, 3, 1, 1, 1, 1, N'مجموعة المخزون', 0, 0, 0.00, 1, 3, 1, GETDATE()),
(14, N'1.01.021', N'مخزون المواد الغذائية', N'Food Inventory', 13, 3, 1, 1, 1, 1, N'مخزون المواد الغذائية', 1, 1, 25000.00, 1, 3, 0, GETDATE()),
(15, N'1.01.022', N'مخزون المواد التنظيفية', N'Cleaning Supplies Inventory', 13, 3, 1, 1, 1, 1, N'مخزون المواد التنظيفية', 1, 1, 8000.00, 1, 3, 0, GETDATE()),

-- 1.2 الأصول الثابتة
(16, N'1.02.000', N'الأصول الثابتة', N'Fixed Assets', 1, 2, 1, 1, 1, 1, N'الأصول طويلة الأجل', 0, 0, 0.00, 1, 2, 1, GETDATE()),

-- 1.2.1 العقارات والأراضي
(17, N'1.02.001', N'الأراضي والعقارات', N'Land and Real Estate', 16, 3, 1, 1, 1, 1, N'الأراضي والعقارات الوقفية', 1, 1, 2500000.00, 1, 3, 0, GETDATE()),
(18, N'1.02.002', N'المباني والإنشاءات', N'Buildings and Constructions', 16, 3, 1, 1, 1, 1, N'المباني والإنشاءات الوقفية', 1, 1, 1800000.00, 1, 3, 0, GETDATE()),
(19, N'1.02.003', N'المباني السكنية', N'Residential Buildings', 16, 3, 1, 1, 1, 1, N'المباني السكنية الوقفية', 1, 1, 1200000.00, 1, 3, 0, GETDATE()),
(20, N'1.02.004', N'المباني التجارية', N'Commercial Buildings', 16, 3, 1, 1, 1, 1, N'المباني التجارية الوقفية', 1, 1, 800000.00, 1, 3, 0, GETDATE()),

-- 1.2.2 المعدات والأثاث
(21, N'1.02.010', N'الأثاث والمعدات', N'Furniture and Equipment', 16, 3, 1, 1, 1, 1, N'الأثاث والمعدات المكتبية', 1, 1, 150000.00, 1, 3, 0, GETDATE()),
(22, N'1.02.011', N'أجهزة الحاسوب', N'Computer Equipment', 16, 3, 1, 1, 1, 1, N'أجهزة الحاسوب والتقنية', 1, 1, 85000.00, 1, 3, 0, GETDATE()),
(23, N'1.02.012', N'السيارات والمركبات', N'Vehicles', 16, 3, 1, 1, 1, 1, N'السيارات والمركبات', 1, 1, 120000.00, 1, 3, 0, GETDATE()),

-- ===== 2. الخصوم (Liabilities) =====
(24, N'2.00.000', N'الخصوم', N'Liabilities', NULL, 1, 2, 2, 1, 1, N'مجموعة الخصوم الرئيسية', 0, 0, 0.00, 2, 1, 1, GETDATE()),

-- 2.1 الخصوم المتداولة
(25, N'2.01.000', N'الخصوم المتداولة', N'Current Liabilities', 24, 2, 2, 2, 1, 1, N'الخصوم قصيرة الأجل', 0, 0, 0.00, 2, 2, 1, GETDATE()),

-- 2.1.1 الموردون والذمم الدائنة
(26, N'2.01.001', N'الموردون والذمم الدائنة', N'Accounts Payable', 25, 3, 2, 2, 1, 1, N'الموردون والذمم الدائنة', 1, 1, 25000.00, 2, 3, 0, GETDATE()),
(27, N'2.01.002', N'مورد - شركة الإنشاءات المتقدمة', N'Supplier - Advanced Construction Co.', 25, 3, 2, 2, 1, 1, N'شركة الإنشاءات المتقدمة', 1, 1, 15000.00, 2, 3, 0, GETDATE()),
(28, N'2.01.003', N'مورد - شركة الخدمات التقنية', N'Supplier - Technical Services Co.', 25, 3, 2, 2, 1, 1, N'شركة الخدمات التقنية', 1, 1, 8000.00, 2, 3, 0, GETDATE()),

-- 2.1.2 المصروفات المستحقة
(29, N'2.01.010', N'المصروفات المستحقة', N'Accrued Expenses', 25, 3, 2, 2, 1, 1, N'المصروفات المستحقة الدفع', 1, 1, 15000.00, 2, 3, 0, GETDATE()),
(30, N'2.01.011', N'رواتب مستحقة', N'Accrued Salaries', 25, 3, 2, 2, 1, 1, N'رواتب الموظفين المستحقة', 1, 1, 12000.00, 2, 3, 0, GETDATE()),

-- ===== 3. حقوق الملكية (Equity) =====
(31, N'3.00.000', N'حقوق الملكية', N'Equity', NULL, 1, 2, 3, 1, 1, N'مجموعة حقوق الملكية', 0, 0, 0.00, 2, 1, 1, GETDATE()),

-- 3.1 رأس المال
(32, N'3.01.000', N'رأس المال', N'Capital', 31, 2, 2, 3, 1, 1, N'مجموعة رأس المال', 0, 0, 0.00, 2, 2, 1, GETDATE()),
(33, N'3.01.001', N'رأس المال الوقفي', N'Endowment Capital', 32, 3, 2, 3, 1, 1, N'رأس المال الوقفي الأساسي', 1, 1, 5000000.00, 2, 3, 0, GETDATE()),
(34, N'3.01.002', N'الأرباح المحتجزة', N'Retained Earnings', 32, 3, 2, 3, 1, 1, N'الأرباح المحتجزة من السنوات السابقة', 1, 1, 500000.00, 2, 3, 0, GETDATE()),
(35, N'3.01.003', N'احتياطي عام', N'General Reserve', 32, 3, 2, 3, 1, 1, N'الاحتياطي العام', 1, 1, 200000.00, 2, 3, 0, GETDATE());

-- ===== 4. الإيرادات (Revenues) =====
(36, N'4.00.000', N'الإيرادات', N'Revenues', NULL, 1, 2, 4, 1, 1, N'مجموعة الإيرادات الرئيسية', 0, 0, 0.00, 2, 1, 1, GETDATE()),

-- 4.1 إيرادات التشغيل
(37, N'4.01.000', N'إيرادات التشغيل', N'Operating Revenues', 36, 2, 2, 4, 1, 1, N'إيرادات التشغيل الأساسية', 0, 0, 0.00, 2, 2, 1, GETDATE()),

-- 4.1.1 إيرادات الإيجارات
(38, N'4.01.001', N'إيرادات الإيجارات السكنية', N'Residential Rental Income', 37, 3, 2, 4, 1, 1, N'إيرادات إيجار العقارات السكنية', 1, 1, 0.00, 2, 3, 0, GETDATE()),
(39, N'4.01.002', N'إيرادات الإيجارات التجارية', N'Commercial Rental Income', 37, 3, 2, 4, 1, 1, N'إيرادات إيجار العقارات التجارية', 1, 1, 0.00, 2, 3, 0, GETDATE()),
(40, N'4.01.003', N'إيرادات الإيجارات الإدارية', N'Administrative Rental Income', 37, 3, 2, 4, 1, 1, N'إيرادات إيجار المكاتب الإدارية', 1, 1, 0.00, 2, 3, 0, GETDATE()),

-- 4.1.2 إيرادات الاستثمارات
(41, N'4.01.010', N'إيرادات الاستثمارات', N'Investment Income', 37, 3, 2, 4, 1, 1, N'إيرادات الاستثمارات المالية', 1, 1, 0.00, 2, 3, 0, GETDATE()),
(42, N'4.01.011', N'أرباح الأسهم', N'Dividend Income', 37, 3, 2, 4, 1, 1, N'أرباح الأسهم والاستثمارات', 1, 1, 0.00, 2, 3, 0, GETDATE()),
(43, N'4.01.012', N'فوائد الودائع البنكية', N'Bank Deposit Interest', 37, 3, 2, 4, 1, 1, N'فوائد الودائع البنكية', 1, 1, 0.00, 2, 3, 0, GETDATE()),

-- 4.2 إيرادات أخرى
(44, N'4.02.000', N'إيرادات أخرى', N'Other Revenues', 36, 2, 2, 4, 1, 1, N'الإيرادات الأخرى', 0, 0, 0.00, 2, 2, 1, GETDATE()),
(45, N'4.02.001', N'التبرعات والهبات', N'Donations and Gifts', 44, 3, 2, 4, 1, 1, N'التبرعات والهبات الواردة', 1, 1, 0.00, 2, 3, 0, GETDATE()),
(46, N'4.02.002', N'إيرادات متنوعة', N'Miscellaneous Income', 44, 3, 2, 4, 1, 1, N'إيرادات متنوعة أخرى', 1, 1, 0.00, 2, 3, 0, GETDATE()),

-- ===== 5. المصروفات (Expenses) =====
(47, N'5.00.000', N'المصروفات', N'Expenses', NULL, 1, 1, 5, 1, 1, N'مجموعة المصروفات الرئيسية', 0, 0, 0.00, 1, 1, 1, GETDATE()),

-- 5.1 مصروفات التشغيل
(48, N'5.01.000', N'مصروفات التشغيل', N'Operating Expenses', 47, 2, 1, 5, 1, 1, N'مصروفات التشغيل الأساسية', 0, 0, 0.00, 1, 2, 1, GETDATE()),

-- 5.1.1 الرواتب والأجور
(49, N'5.01.001', N'الرواتب والأجور', N'Salaries and Wages', 48, 3, 1, 5, 1, 1, N'رواتب وأجور الموظفين', 1, 1, 0.00, 1, 3, 0, GETDATE()),
(50, N'5.01.002', N'بدلات الموظفين', N'Employee Allowances', 48, 3, 1, 5, 1, 1, N'بدلات ومكافآت الموظفين', 1, 1, 0.00, 1, 3, 0, GETDATE()),
(51, N'5.01.003', N'التأمينات الاجتماعية', N'Social Insurance', 48, 3, 1, 5, 1, 1, N'التأمينات الاجتماعية للموظفين', 1, 1, 0.00, 1, 3, 0, GETDATE()),

-- 5.1.2 مصروفات الصيانة
(52, N'5.01.010', N'مصروفات الصيانة', N'Maintenance Expenses', 48, 3, 1, 5, 1, 1, N'مصروفات صيانة العقارات', 1, 1, 0.00, 1, 3, 0, GETDATE()),
(53, N'5.01.011', N'صيانة المباني', N'Building Maintenance', 48, 3, 1, 5, 1, 1, N'صيانة المباني والإنشاءات', 1, 1, 0.00, 1, 3, 0, GETDATE()),
(54, N'5.01.012', N'صيانة المعدات', N'Equipment Maintenance', 48, 3, 1, 5, 1, 1, N'صيانة المعدات والأجهزة', 1, 1, 0.00, 1, 3, 0, GETDATE()),

-- 5.1.3 المصروفات الإدارية
(55, N'5.01.020', N'المصروفات الإدارية', N'Administrative Expenses', 48, 3, 1, 5, 1, 1, N'المصروفات الإدارية العامة', 1, 1, 0.00, 1, 3, 0, GETDATE()),
(56, N'5.01.021', N'مصروفات الكهرباء والماء', N'Utilities Expenses', 48, 3, 1, 5, 1, 1, N'مصروفات الكهرباء والماء والهاتف', 1, 1, 0.00, 1, 3, 0, GETDATE()),
(57, N'5.01.022', N'مصروفات التأمين', N'Insurance Expenses', 48, 3, 1, 5, 1, 1, N'مصروفات التأمين على العقارات', 1, 1, 0.00, 1, 3, 0, GETDATE()),
(58, N'5.01.023', N'مصروفات القرطاسية', N'Stationery Expenses', 48, 3, 1, 5, 1, 1, N'مصروفات القرطاسية والمكتبية', 1, 1, 0.00, 1, 3, 0, GETDATE()),
(59, N'5.01.024', N'مصروفات الاتصالات', N'Communication Expenses', 48, 3, 1, 5, 1, 1, N'مصروفات الهاتف والإنترنت', 1, 1, 0.00, 1, 3, 0, GETDATE()),
(60, N'5.01.025', N'مصروفات السفر والانتقال', N'Travel Expenses', 48, 3, 1, 5, 1, 1, N'مصروفات السفر والانتقال', 1, 1, 0.00, 1, 3, 0, GETDATE());

SET IDENTITY_INSERT ChartOfAccounts OFF;
GO

-- عرض ملخص الحسابات المدرجة
SELECT
    CASE LevelType
        WHEN 1 THEN N'رئيسي'
        WHEN 2 THEN N'فرعي'
        WHEN 3 THEN N'تفصيلي'
    END as 'نوع الحساب',
    COUNT(*) as 'عدد الحسابات'
FROM ChartOfAccounts
GROUP BY LevelType
ORDER BY LevelType;

-- عرض الحسابات الرئيسية
SELECT
    AccountCode as 'رمز الحساب',
    AccountNameAr as 'اسم الحساب',
    CASE Nature
        WHEN 1 THEN N'مدين'
        WHEN 2 THEN N'دائن'
    END as 'طبيعة الحساب'
FROM ChartOfAccounts
WHERE LevelType = 1
ORDER BY AccountCode;

PRINT N'تم إدراج 60 حساب محاسبي شامل (رئيسي وفرعي وتفصيلي) بنجاح';
