@echo off
echo ========================================
echo    نظام إدارة الأوقاف - وحدة المحاسبة
echo    Awqaf Management System - Accounting
echo ========================================
echo.

REM Check if executable exists
if exist "bin\Debug\Awqaf_Managment.exe" (
    echo ✓ التطبيق موجود ومتاح للتشغيل
    echo ✓ Application found and ready to run
    echo.
    echo تشغيل التطبيق...
    echo Starting application...
    start "" "bin\Debug\Awqaf_Managment.exe"
    echo.
    echo ========================================
    echo بيانات تسجيل الدخول:
    echo Login Credentials:
    echo Username: admin
    echo Password: admin123
    echo ========================================
    goto :end
)

echo البحث عن أدوات البناء...
echo Looking for build tools...

REM Try to find Visual Studio Build Tools
set "VSWHERE=%ProgramFiles(x86)%\Microsoft Visual Studio\Installer\vswhere.exe"
if exist "%VSWHERE%" (
    for /f "usebackq tokens=*" %%i in (`"%VSWHERE%" -latest -products * -requires Microsoft.Component.MSBuild -property installationPath`) do (
        set "VSINSTALLDIR=%%i"
    )
)

REM Try MSBuild from Visual Studio
if defined VSINSTALLDIR (
    set "MSBUILD=%VSINSTALLDIR%\MSBuild\Current\Bin\MSBuild.exe"
    if exist "%MSBUILD%" (
        echo استخدام Visual Studio MSBuild...
        echo Using Visual Studio MSBuild...
        "%MSBUILD%" Awqaf_Managment.csproj /p:Configuration=Debug /p:Platform="Any CPU"
        goto :success
    )
)

REM Try .NET Framework MSBuild
set "MSBUILD=%ProgramFiles(x86)%\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe"
if exist "%MSBUILD%" (
    echo استخدام .NET Framework MSBuild 2019...
    echo Using .NET Framework MSBuild 2019...
    "%MSBUILD%" Awqaf_Managment.csproj /p:Configuration=Debug /p:Platform="Any CPU"
    goto :success
)

REM Try .NET Framework MSBuild 2017
set "MSBUILD=%ProgramFiles(x86)%\Microsoft Visual Studio\2017\BuildTools\MSBuild\15.0\Bin\MSBuild.exe"
if exist "%MSBUILD%" (
    echo استخدام .NET Framework MSBuild 2017...
    echo Using .NET Framework MSBuild 2017...
    "%MSBUILD%" Awqaf_Managment.csproj /p:Configuration=Debug /p:Platform="Any CPU"
    goto :success
)

REM Try Windows SDK
set "MSBUILD=%ProgramFiles(x86)%\MSBuild\14.0\Bin\MSBuild.exe"
if exist "%MSBUILD%" (
    echo استخدام Windows SDK MSBuild...
    echo Using Windows SDK MSBuild...
    "%MSBUILD%" Awqaf_Managment.csproj /p:Configuration=Debug /p:Platform="Any CPU"
    goto :success
)

echo ❌ لم يتم العثور على أدوات البناء
echo ❌ MSBuild not found. Please install Visual Studio Build Tools.
echo يرجى تثبيت Visual Studio Build Tools أو Visual Studio
echo Please install Visual Studio Build Tools or Visual Studio
goto :end

:success
echo ✓ تم البناء بنجاح!
echo ✓ Build completed successfully!
echo تشغيل التطبيق...
echo Running application...
start "" "bin\Debug\Awqaf_Managment.exe"
echo.
echo ========================================
echo بيانات تسجيل الدخول:
echo Login Credentials:
echo Username: admin
echo Password: admin123
echo ========================================

:end
echo.
echo اضغط أي مفتاح للإغلاق...
echo Press any key to close...
pause >nul
