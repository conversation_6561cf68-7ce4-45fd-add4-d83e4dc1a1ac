-- إدراج الحسابات الرئيسية والفرعية - النسخة النهائية
-- Insert Main and Sub Accounts - Final Version

USE AwqafManagement;
GO

-- حذف البيانات الموجودة وإعادة تعيين الهوية
DELETE FROM ChartOfAccounts;
DBCC CHECKIDENT ('ChartOfAccounts', RESEED, 0);
GO

-- إدراج الحسابات الرئيسية والفرعية
SET IDENTITY_INSERT ChartOfAccounts ON;

-- ===== 1. الأصول (Assets) =====
INSERT INTO ChartOfAccounts (AccountId, AccountCode, AccountName, AccountNameAr, AccountTypeId, AccountGroupId, ParentAccountId, AccountLevel, IsParent, IsActive, AllowPosting, Description, CurrencyCode, OpeningBalance, CurrentBalance, CreatedDate, CreatedBy)
VALUES 
-- 1.0 الأصول - الحساب الرئيسي
(1, N'1.00.000', N'Assets', N'الأصول', 1003, 1008, NULL, 1, 1, 1, 0, N'مجموعة الأصول الرئيسية', N'SAR', 0.00, 0.00, GETDATE(), 1),

-- 1.1 الأصول المتداولة
(2, N'1.01.000', N'Current Assets', N'الأصول المتداولة', 1003, 1008, 1, 2, 1, 1, 0, N'الأصول قصيرة الأجل', N'SAR', 0.00, 0.00, GETDATE(), 1),

-- 1.1.1 النقدية والبنوك
(3, N'1.01.001', N'Main Cash Box', N'الصندوق الرئيسي', 1003, 1008, 2, 3, 0, 1, 1, N'صندوق النقدية الرئيسي', N'SAR', 50000.00, 50000.00, GETDATE(), 1),
(4, N'1.01.002', N'Sub Cash Box', N'صندوق فرعي', 1003, 1008, 2, 3, 0, 1, 1, N'صندوق نقدية فرعي', N'SAR', 15000.00, 15000.00, GETDATE(), 1),
(5, N'1.01.003', N'National Commercial Bank', N'البنك الأهلي التجاري', 1003, 1008, 2, 3, 0, 1, 1, N'حساب البنك الأهلي التجاري', N'SAR', 250000.00, 250000.00, GETDATE(), 1),
(6, N'1.01.004', N'Al Rajhi Bank', N'بنك الراجحي', 1003, 1008, 2, 3, 0, 1, 1, N'حساب بنك الراجحي', N'SAR', 180000.00, 180000.00, GETDATE(), 1),
(7, N'1.01.005', N'Riyad Bank', N'بنك الرياض', 1003, 1008, 2, 3, 0, 1, 1, N'حساب بنك الرياض', N'SAR', 120000.00, 120000.00, GETDATE(), 1),
(8, N'1.01.006', N'Samba Bank', N'بنك سامبا', 1003, 1008, 2, 3, 0, 1, 1, N'حساب بنك سامبا', N'SAR', 95000.00, 95000.00, GETDATE(), 1),

-- 1.1.2 العملاء والذمم المدينة
(9, N'1.01.010', N'Accounts Receivable', N'العملاء والذمم المدينة', 1003, 1008, 2, 3, 1, 1, 0, N'مجموعة العملاء والذمم المدينة', N'SAR', 0.00, 0.00, GETDATE(), 1),
(10, N'1.01.011', N'Client - Real Estate Endowments Co.', N'عميل - شركة الأوقاف العقارية', 1003, 1008, 9, 3, 0, 1, 1, N'شركة الأوقاف العقارية', N'SAR', 75000.00, 75000.00, GETDATE(), 1),
(11, N'1.01.012', N'Client - Islamic Charity Foundation', N'عميل - مؤسسة الخير الإسلامية', 1003, 1008, 9, 3, 0, 1, 1, N'مؤسسة الخير الإسلامية', N'SAR', 45000.00, 45000.00, GETDATE(), 1),
(12, N'1.01.013', N'Client - Charity Association', N'عميل - جمعية البر الخيرية', 1003, 1008, 9, 3, 0, 1, 1, N'جمعية البر الخيرية', N'SAR', 32000.00, 32000.00, GETDATE(), 1),

-- 1.2 الأصول الثابتة
(13, N'1.02.000', N'Fixed Assets', N'الأصول الثابتة', 1003, 1009, 1, 2, 1, 1, 0, N'الأصول طويلة الأجل', N'SAR', 0.00, 0.00, GETDATE(), 1),
(14, N'1.02.001', N'Land and Real Estate', N'الأراضي والعقارات', 1003, 1009, 13, 3, 0, 1, 1, N'الأراضي والعقارات الوقفية', N'SAR', 2500000.00, 2500000.00, GETDATE(), 1),
(15, N'1.02.002', N'Buildings and Constructions', N'المباني والإنشاءات', 1003, 1009, 13, 3, 0, 1, 1, N'المباني والإنشاءات الوقفية', N'SAR', 1800000.00, 1800000.00, GETDATE(), 1),
(16, N'1.02.003', N'Residential Buildings', N'المباني السكنية', 1003, 1009, 13, 3, 0, 1, 1, N'المباني السكنية الوقفية', N'SAR', 1200000.00, 1200000.00, GETDATE(), 1),
(17, N'1.02.004', N'Commercial Buildings', N'المباني التجارية', 1003, 1009, 13, 3, 0, 1, 1, N'المباني التجارية الوقفية', N'SAR', 800000.00, 800000.00, GETDATE(), 1),
(18, N'1.02.010', N'Furniture and Equipment', N'الأثاث والمعدات', 1003, 1009, 13, 3, 0, 1, 1, N'الأثاث والمعدات المكتبية', N'SAR', 150000.00, 150000.00, GETDATE(), 1),
(19, N'1.02.011', N'Computer Equipment', N'أجهزة الحاسوب', 1003, 1009, 13, 3, 0, 1, 1, N'أجهزة الحاسوب والتقنية', N'SAR', 85000.00, 85000.00, GETDATE(), 1),
(20, N'1.02.012', N'Vehicles', N'السيارات والمركبات', 1003, 1009, 13, 3, 0, 1, 1, N'السيارات والمركبات', N'SAR', 120000.00, 120000.00, GETDATE(), 1),

-- ===== 2. الخصوم (Liabilities) =====
(21, N'2.00.000', N'Liabilities', N'الخصوم', 1004, 1010, NULL, 1, 1, 1, 0, N'مجموعة الخصوم الرئيسية', N'SAR', 0.00, 0.00, GETDATE(), 1),
(22, N'2.01.000', N'Current Liabilities', N'الخصوم المتداولة', 1004, 1010, 21, 2, 1, 1, 0, N'الخصوم قصيرة الأجل', N'SAR', 0.00, 0.00, GETDATE(), 1),
(23, N'2.01.001', N'Accounts Payable', N'الموردون والذمم الدائنة', 1004, 1010, 22, 3, 0, 1, 1, N'الموردون والذمم الدائنة', N'SAR', 25000.00, 25000.00, GETDATE(), 1),
(24, N'2.01.002', N'Supplier - Advanced Construction Co.', N'مورد - شركة الإنشاءات المتقدمة', 1004, 1010, 22, 3, 0, 1, 1, N'شركة الإنشاءات المتقدمة', N'SAR', 15000.00, 15000.00, GETDATE(), 1),
(25, N'2.01.003', N'Supplier - Technical Services Co.', N'مورد - شركة الخدمات التقنية', 1004, 1010, 22, 3, 0, 1, 1, N'شركة الخدمات التقنية', N'SAR', 8000.00, 8000.00, GETDATE(), 1),
(26, N'2.01.010', N'Accrued Expenses', N'المصروفات المستحقة', 1004, 1010, 22, 3, 0, 1, 1, N'المصروفات المستحقة الدفع', N'SAR', 15000.00, 15000.00, GETDATE(), 1),
(27, N'2.01.011', N'Accrued Salaries', N'رواتب مستحقة', 1004, 1010, 22, 3, 0, 1, 1, N'رواتب الموظفين المستحقة', N'SAR', 12000.00, 12000.00, GETDATE(), 1),

-- ===== 3. حقوق الملكية (Equity) =====
(28, N'3.00.000', N'Equity', N'حقوق الملكية', 1005, 1011, NULL, 1, 1, 1, 0, N'مجموعة حقوق الملكية', N'SAR', 0.00, 0.00, GETDATE(), 1),
(29, N'3.01.001', N'Endowment Capital', N'رأس المال الوقفي', 1005, 1011, 28, 3, 0, 1, 1, N'رأس المال الوقفي الأساسي', N'SAR', 5000000.00, 5000000.00, GETDATE(), 1),
(30, N'3.01.002', N'Retained Earnings', N'الأرباح المحتجزة', 1005, 1011, 28, 3, 0, 1, 1, N'الأرباح المحتجزة من السنوات السابقة', N'SAR', 500000.00, 500000.00, GETDATE(), 1),
(31, N'3.01.003', N'General Reserve', N'احتياطي عام', 1005, 1011, 28, 3, 0, 1, 1, N'الاحتياطي العام', N'SAR', 200000.00, 200000.00, GETDATE(), 1);

SET IDENTITY_INSERT ChartOfAccounts OFF;
GO

PRINT N'تم إدراج الحسابات الرئيسية والفرعية بنجاح - المرحلة الأولى (31 حساب)';
