USE AwqafManagement;
GO

-- إنشاء إجراء مخزن للمصادقة
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_AuthenticateUser')
    DROP PROCEDURE sp_AuthenticateUser;
GO

CREATE PROCEDURE sp_AuthenticateUser
    @Username NVARCHAR(50),
    @PasswordHash NVARCHAR(255)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @UserId INT;
    DECLARE @IsActive BIT;
    DECLARE @IsLocked BIT;
    DECLARE @FailedAttempts INT;
    DECLARE @StoredHash NVARCHAR(255);
    DECLARE @Salt NVARCHAR(255);
    
    -- البحث عن المستخدم
    SELECT @UserId = UserId, @IsActive = IsActive, @IsLocked = IsLocked, 
           @FailedAttempts = FailedLoginAttempts, @StoredHash = PasswordHash, @Salt = Salt
    FROM Users 
    WHERE Username = @Username;
    
    -- التحقق من وجود المستخدم
    IF @UserId IS NULL
    BEGIN
        SELECT 0 AS IsAuthenticated, 'المستخدم غير موجود' AS Message, NULL AS UserId;
        RETURN;
    END
    
    -- التحقق من حالة المستخدم
    IF @IsActive = 0
    BEGIN
        SELECT 0 AS IsAuthenticated, 'الحساب غير نشط' AS Message, @UserId AS UserId;
        RETURN;
    END
    
    IF @IsLocked = 1
    BEGIN
        SELECT 0 AS IsAuthenticated, 'الحساب مقفل' AS Message, @UserId AS UserId;
        RETURN;
    END
    
    -- التحقق من كلمة المرور
    IF @StoredHash = @PasswordHash
    BEGIN
        -- تسجيل دخول ناجح
        UPDATE Users SET 
            LastLoginDate = GETDATE(),
            FailedLoginAttempts = 0
        WHERE UserId = @UserId;
        
        -- إدراج سجل تسجيل الدخول
        INSERT INTO LoginHistory (UserId, Username, IsSuccessful) 
        VALUES (@UserId, @Username, 1);
        
        SELECT 1 AS IsAuthenticated, 'تم تسجيل الدخول بنجاح' AS Message, @UserId AS UserId;
    END
    ELSE
    BEGIN
        -- تسجيل دخول فاشل
        UPDATE Users SET FailedLoginAttempts = FailedLoginAttempts + 1 WHERE UserId = @UserId;
        
        INSERT INTO LoginHistory (UserId, Username, IsSuccessful, FailureReason) 
        VALUES (@UserId, @Username, 0, 'كلمة مرور خاطئة');
        
        SELECT 0 AS IsAuthenticated, 'كلمة المرور غير صحيحة' AS Message, @UserId AS UserId;
    END
END
GO

-- إنشاء إجراء مخزن لجلب صلاحيات المستخدم
IF EXISTS (SELECT * FROM sys.objects WHERE type = 'P' AND name = 'sp_GetUserPermissions')
    DROP PROCEDURE sp_GetUserPermissions;
GO

CREATE PROCEDURE sp_GetUserPermissions
    @UserId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT DISTINCT 
        p.PermissionId,
        p.PermissionName,
        p.PermissionNameAr,
        p.ModuleName,
        p.ModuleNameAr,
        rp.CanView,
        rp.CanAdd,
        rp.CanEdit,
        rp.CanDelete,
        rp.CanPrint,
        rp.CanExport
    FROM Users u
    INNER JOIN UserRoles ur ON u.UserId = ur.UserId AND ur.IsActive = 1
    INNER JOIN Roles r ON ur.RoleId = r.RoleId AND r.IsActive = 1
    INNER JOIN RolePermissions rp ON r.RoleId = rp.RoleId
    INNER JOIN Permissions p ON rp.PermissionId = p.PermissionId AND p.IsActive = 1
    WHERE u.UserId = @UserId AND u.IsActive = 1;
END
GO

-- التحقق من وجود المستخدم admin
IF NOT EXISTS (SELECT * FROM Users WHERE Username = 'admin')
BEGIN
    -- إنشاء مستخدم admin مع كلمة مرور مشفرة
    INSERT INTO Users (Username, PasswordHash, Salt, FullName, Email, IsActive) VALUES 
    ('admin', 'A665A45920422F9D417E4867EFDC4FB8A04A1F3FFF1FA07E998E86F7F7A27AE3', 'RandomSalt123', 'مدير النظام', '<EMAIL>', 1);
    
    -- ربط المدير بدور مدير النظام
    DECLARE @AdminUserId INT = (SELECT UserId FROM Users WHERE Username = 'admin');
    DECLARE @AdminRoleId INT = (SELECT RoleId FROM Roles WHERE RoleName = 'Administrator');
    
    IF @AdminRoleId IS NOT NULL AND NOT EXISTS (SELECT * FROM UserRoles WHERE UserId = @AdminUserId AND RoleId = @AdminRoleId)
    BEGIN
        INSERT INTO UserRoles (UserId, RoleId) VALUES (@AdminUserId, @AdminRoleId);
    END
END
GO

PRINT 'تم إنشاء الإجراءات المخزنة بنجاح';
PRINT 'Stored procedures created successfully';
