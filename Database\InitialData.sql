-- إدراج البيانات الأولية لنظام إدارة الأوقاف
-- Initial Data for Awqaf Management System

USE AwqafManagement;
GO

-- إدراج الأدوار الأساسية
-- Insert Basic Roles
IF NOT EXISTS (SELECT 1 FROM Roles WHERE RoleName = 'SuperAdmin')
BEGIN
    INSERT INTO Roles (RoleName, RoleNameAr, Description, IsActive)
    VALUES 
    ('SuperAdmin', 'مدير النظام', 'مدير النظام الرئيسي - صلاحيات كاملة', 1),
    ('Admin', 'مدير', 'مدير عام - صلاحيات إدارية', 1),
    ('Manager', 'مدير قسم', 'مدير قسم - صلاحيات محدودة', 1),
    ('User', 'مستخدم', 'مستخدم عادي - صلاحيات أساسية', 1),
    ('Viewer', 'مشاهد', 'مشاهد فقط - صلاحيات قراءة', 1);
END

-- إدراج الصلاحيات الأساسية
-- Insert Basic Permissions
IF NOT EXISTS (SELECT 1 FROM Permissions WHERE ModuleName = 'Properties')
BEGIN
    INSERT INTO Permissions (PermissionName, PermissionNameAr, ModuleName, ModuleNameAr, Description, IsActive)
    VALUES
    ('Properties.View', 'عرض العقارات', 'Properties', 'العقارات', 'صلاحية عرض العقارات', 1),
    ('Properties.Add', 'إضافة العقارات', 'Properties', 'العقارات', 'صلاحية إضافة العقارات', 1),
    ('Properties.Edit', 'تعديل العقارات', 'Properties', 'العقارات', 'صلاحية تعديل العقارات', 1),
    ('Properties.Delete', 'حذف العقارات', 'Properties', 'العقارات', 'صلاحية حذف العقارات', 1),
    ('Customers.View', 'عرض العملاء', 'Customers', 'العملاء', 'صلاحية عرض العملاء', 1),
    ('Customers.Add', 'إضافة العملاء', 'Customers', 'العملاء', 'صلاحية إضافة العملاء', 1),
    ('Customers.Edit', 'تعديل العملاء', 'Customers', 'العملاء', 'صلاحية تعديل العملاء', 1),
    ('Customers.Delete', 'حذف العملاء', 'Customers', 'العملاء', 'صلاحية حذف العملاء', 1),
    ('Contracts.View', 'عرض العقود', 'Contracts', 'العقود', 'صلاحية عرض العقود', 1),
    ('Contracts.Add', 'إضافة العقود', 'Contracts', 'العقود', 'صلاحية إضافة العقود', 1),
    ('Contracts.Edit', 'تعديل العقود', 'Contracts', 'العقود', 'صلاحية تعديل العقود', 1),
    ('Contracts.Delete', 'حذف العقود', 'Contracts', 'العقود', 'صلاحية حذف العقود', 1),
    ('Settings.View', 'عرض الإعدادات', 'Settings', 'الإعدادات', 'صلاحية عرض الإعدادات', 1),
    ('Settings.Add', 'إضافة الإعدادات', 'Settings', 'الإعدادات', 'صلاحية إضافة الإعدادات', 1),
    ('Settings.Edit', 'تعديل الإعدادات', 'Settings', 'الإعدادات', 'صلاحية تعديل الإعدادات', 1),
    ('Settings.Delete', 'حذف الإعدادات', 'Settings', 'الإعدادات', 'صلاحية حذف الإعدادات', 1);
END

-- إدراج الصلاحيات للمدير الرئيسي على جميع الوحدات
-- Insert Permissions for SuperAdmin on all modules
DECLARE @SuperAdminRoleId INT = (SELECT RoleId FROM Roles WHERE RoleName = 'SuperAdmin');

IF @SuperAdminRoleId IS NOT NULL
BEGIN
    -- حذف الصلاحيات الموجودة للمدير الرئيسي
    DELETE FROM RolePermissions WHERE RoleId = @SuperAdminRoleId;

    -- إدراج صلاحيات كاملة للمدير الرئيسي على جميع الصلاحيات
    INSERT INTO RolePermissions (RoleId, PermissionId, CanView, CanAdd, CanEdit, CanDelete, CanPrint, CanExport)
    SELECT @SuperAdminRoleId, PermissionId, 1, 1, 1, 1, 1, 1
    FROM Permissions WHERE IsActive = 1;
END

-- إدراج الصلاحيات للمدير العام
DECLARE @AdminRoleId INT = (SELECT RoleId FROM Roles WHERE RoleName = 'Admin');

IF @AdminRoleId IS NOT NULL
BEGIN
    -- حذف الصلاحيات الموجودة للمدير العام
    DELETE FROM RolePermissions WHERE RoleId = @AdminRoleId;

    -- إدراج صلاحيات للمدير العام على الصلاحيات الأساسية
    INSERT INTO RolePermissions (RoleId, PermissionId, CanView, CanAdd, CanEdit, CanDelete, CanPrint, CanExport)
    SELECT @AdminRoleId, PermissionId, 1, 1, 1,
           CASE WHEN PermissionName LIKE '%.Delete' AND ModuleName IN ('Settings') THEN 0 ELSE 1 END,
           1, 1
    FROM Permissions
    WHERE IsActive = 1 AND ModuleName IN ('Properties', 'Customers', 'Contracts', 'Settings');
END

-- تحديث المستخدم الحالي ليكون مدير نظام
-- Update current user to be SuperAdmin
DECLARE @CurrentUserId INT = (SELECT TOP 1 UserId FROM Users WHERE Username = 'admin');
DECLARE @SuperAdminRoleIdForUser INT = (SELECT RoleId FROM Roles WHERE RoleName = 'SuperAdmin');

IF @CurrentUserId IS NOT NULL AND @SuperAdminRoleIdForUser IS NOT NULL
BEGIN
    -- حذف الأدوار الحالية للمستخدم
    DELETE FROM UserRoles WHERE UserId = @CurrentUserId;
    
    -- إضافة دور المدير الرئيسي
    INSERT INTO UserRoles (UserId, RoleId, AssignedDate, AssignedBy)
    VALUES (@CurrentUserId, @SuperAdminRoleIdForUser, GETDATE(), @CurrentUserId);
    
    -- تحديث بيانات المستخدم
    UPDATE Users 
    SET FullName = 'مدير النظام الرئيسي',
        Email = '<EMAIL>',
        IsActive = 1,
        IsLocked = 0
    WHERE UserId = @CurrentUserId;
END

-- إنشاء مستخدم تجريبي إضافي
IF NOT EXISTS (SELECT 1 FROM Users WHERE Username = 'manager')
BEGIN
    DECLARE @ManagerRoleId INT = (SELECT RoleId FROM Roles WHERE RoleName = 'Manager');
    
    INSERT INTO Users (Username, FullName, Email, PasswordHash, Salt, IsActive, IsLocked, CreatedDate)
    VALUES ('manager', 'مدير القسم', '<EMAIL>', 'manager', 'salt123', 1, 0, GETDATE());
    
    DECLARE @NewManagerId INT = SCOPE_IDENTITY();
    
    IF @ManagerRoleId IS NOT NULL
    BEGIN
        INSERT INTO UserRoles (UserId, RoleId, AssignedDate, AssignedBy)
        VALUES (@NewManagerId, @ManagerRoleId, GETDATE(), 1);
    END
END

-- البيانات الأولية تم إدراجها بنجاح
-- Initial data insertion completed

PRINT 'تم إدراج البيانات الأولية بنجاح';
PRINT 'Initial data inserted successfully';
