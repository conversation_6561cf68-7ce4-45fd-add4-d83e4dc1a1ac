using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using System.Threading.Tasks;

namespace Awqaf_Managment.UI.Helpers
{
    /// <summary>
    /// أنواع الإشعارات
    /// Notification Types
    /// </summary>
    public enum NotificationType
    {
        Success,    // نجاح
        <PERSON>r,      // خطأ
        Warning,    // تحذير
        Info        // معلومات
    }

    /// <summary>
    /// مساعد الإشعارات المرئية
    /// Visual Notification Helper
    /// </summary>
    public static class NotificationHelper
    {
        #region Constants
        private const int NOTIFICATION_WIDTH = 350;
        private const int NOTIFICATION_HEIGHT = 80;
        private const int NOTIFICATION_MARGIN = 10;
        private const int ANIMATION_DURATION = 300;
        private const int DISPLAY_DURATION = 3000;
        #endregion

        #region Public Methods
        /// <summary>
        /// عرض إشعار نجاح
        /// Show success notification
        /// </summary>
        /// <param name="message">الرسالة</param>
        /// <param name="title">العنوان</param>
        /// <param name="parent">النموذج الأب</param>
        public static void ShowSuccess(string message, string title = "نجح", Form parent = null)
        {
            ShowNotification(message, title, NotificationType.Success, parent);
        }

        /// <summary>
        /// عرض إشعار خطأ
        /// Show error notification
        /// </summary>
        /// <param name="message">الرسالة</param>
        /// <param name="title">العنوان</param>
        /// <param name="parent">النموذج الأب</param>
        public static void ShowError(string message, string title = "خطأ", Form parent = null)
        {
            ShowNotification(message, title, NotificationType.Error, parent);
        }

        /// <summary>
        /// عرض إشعار تحذير
        /// Show warning notification
        /// </summary>
        /// <param name="message">الرسالة</param>
        /// <param name="title">العنوان</param>
        /// <param name="parent">النموذج الأب</param>
        public static void ShowWarning(string message, string title = "تحذير", Form parent = null)
        {
            ShowNotification(message, title, NotificationType.Warning, parent);
        }

        /// <summary>
        /// عرض إشعار معلومات
        /// Show info notification
        /// </summary>
        /// <param name="message">الرسالة</param>
        /// <param name="title">العنوان</param>
        /// <param name="parent">النموذج الأب</param>
        public static void ShowInfo(string message, string title = "معلومات", Form parent = null)
        {
            ShowNotification(message, title, NotificationType.Info, parent);
        }

        /// <summary>
        /// عرض إشعار مخصص
        /// Show custom notification
        /// </summary>
        /// <param name="message">الرسالة</param>
        /// <param name="title">العنوان</param>
        /// <param name="type">نوع الإشعار</param>
        /// <param name="parent">النموذج الأب</param>
        public static void ShowNotification(string message, string title, NotificationType type, Form parent = null)
        {
            try
            {
                var notification = new NotificationForm(message, title, type);
                notification.ShowNotification(parent);
            }
            catch (Exception ex)
            {
                // في حالة فشل الإشعار المتقدم، استخدم MessageBox
                MessageBox.Show(message, title, MessageBoxButtons.OK, GetMessageBoxIcon(type));
                System.Diagnostics.Debug.WriteLine($"خطأ في عرض الإشعار: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على أيقونة MessageBox حسب النوع
        /// Get MessageBox icon by type
        /// </summary>
        /// <param name="type">نوع الإشعار</param>
        /// <returns>أيقونة MessageBox</returns>
        private static MessageBoxIcon GetMessageBoxIcon(NotificationType type)
        {
            switch (type)
            {
                case NotificationType.Success:
                    return MessageBoxIcon.Information;
                case NotificationType.Error:
                    return MessageBoxIcon.Error;
                case NotificationType.Warning:
                    return MessageBoxIcon.Warning;
                case NotificationType.Info:
                    return MessageBoxIcon.Information;
                default:
                    return MessageBoxIcon.Information;
            }
        }
        #endregion
    }

    /// <summary>
    /// نموذج الإشعار المخصص
    /// Custom Notification Form
    /// </summary>
    internal class NotificationForm : Form
    {
        #region Fields
        private readonly string _message;
        private readonly string _title;
        private readonly NotificationType _type;
        private readonly Timer _displayTimer;
        private readonly Timer _animationTimer;
        private int _animationStep = 0;
        private bool _isClosing = false;
        #endregion

        #region Constructor
        public NotificationForm(string message, string title, NotificationType type)
        {
            _message = message;
            _title = title;
            _type = type;

            InitializeForm();
            SetupTimers();
        }
        #endregion

        #region Initialization
        private void InitializeForm()
        {
            // إعدادات النموذج الأساسية
            this.FormBorderStyle = FormBorderStyle.None;
            this.ShowInTaskbar = false;
            this.TopMost = true;
            this.Size = new Size(NOTIFICATION_WIDTH, NOTIFICATION_HEIGHT);
            this.StartPosition = FormStartPosition.Manual;
            this.BackColor = GetBackgroundColor();
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;

            // إعداد الرسم المخصص
            this.SetStyle(ControlStyles.AllPaintingInWmPaint | 
                         ControlStyles.UserPaint | 
                         ControlStyles.DoubleBuffer | 
                         ControlStyles.ResizeRedraw, true);

            // إضافة أحداث
            this.Paint += NotificationForm_Paint;
            this.Click += NotificationForm_Click;
            this.MouseEnter += NotificationForm_MouseEnter;
            this.MouseLeave += NotificationForm_MouseLeave;
        }

        private void SetupTimers()
        {
            // مؤقت العرض
            _displayTimer = new Timer();
            _displayTimer.Interval = DISPLAY_DURATION;
            _displayTimer.Tick += DisplayTimer_Tick;

            // مؤقت الحركة
            _animationTimer = new Timer();
            _animationTimer.Interval = 16; // ~60 FPS
            _animationTimer.Tick += AnimationTimer_Tick;
        }
        #endregion

        #region Public Methods
        public void ShowNotification(Form parent = null)
        {
            try
            {
                // تحديد موقع الإشعار
                SetNotificationPosition(parent);

                // بدء الحركة
                StartShowAnimation();

                // عرض النموذج
                this.Show();

                // بدء مؤقت العرض
                _displayTimer.Start();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في عرض الإشعار: {ex.Message}");
            }
        }
        #endregion

        #region Private Methods
        private void SetNotificationPosition(Form parent)
        {
            Screen screen = parent != null ? Screen.FromControl(parent) : Screen.PrimaryScreen;
            
            int x = screen.WorkingArea.Right - NOTIFICATION_WIDTH - NOTIFICATION_MARGIN;
            int y = screen.WorkingArea.Bottom - NOTIFICATION_HEIGHT - NOTIFICATION_MARGIN;

            this.Location = new Point(x, y + NOTIFICATION_HEIGHT); // البداية من خارج الشاشة
        }

        private void StartShowAnimation()
        {
            _animationStep = 0;
            _isClosing = false;
            _animationTimer.Start();
        }

        private void StartHideAnimation()
        {
            _animationStep = 0;
            _isClosing = true;
            _displayTimer.Stop();
            _animationTimer.Start();
        }

        private Color GetBackgroundColor()
        {
            switch (_type)
            {
                case NotificationType.Success:
                    return Color.FromArgb(46, 204, 113);
                case NotificationType.Error:
                    return Color.FromArgb(231, 76, 60);
                case NotificationType.Warning:
                    return Color.FromArgb(241, 196, 15);
                case NotificationType.Info:
                    return Color.FromArgb(52, 152, 219);
                default:
                    return Color.FromArgb(52, 152, 219);
            }
        }

        private string GetIcon()
        {
            switch (_type)
            {
                case NotificationType.Success:
                    return "✓";
                case NotificationType.Error:
                    return "✗";
                case NotificationType.Warning:
                    return "⚠";
                case NotificationType.Info:
                    return "ℹ";
                default:
                    return "ℹ";
            }
        }
        #endregion

        #region Event Handlers
        private void NotificationForm_Paint(object sender, PaintEventArgs e)
        {
            try
            {
                Graphics g = e.Graphics;
                g.SmoothingMode = SmoothingMode.AntiAlias;
                g.TextRenderingHint = System.Drawing.Text.TextRenderingHint.ClearTypeGridFit;

                // رسم الخلفية مع الحواف المدورة
                using (var path = CreateRoundedRectangle(ClientRectangle, 8))
                {
                    using (var brush = new SolidBrush(GetBackgroundColor()))
                    {
                        g.FillPath(brush, path);
                    }
                }

                // رسم الأيقونة
                var iconFont = new Font("Segoe UI Symbol", 16, FontStyle.Bold);
                var iconBrush = new SolidBrush(Color.White);
                var iconRect = new Rectangle(10, 10, 30, 30);
                
                var iconFormat = new StringFormat
                {
                    Alignment = StringAlignment.Center,
                    LineAlignment = StringAlignment.Center
                };
                
                g.DrawString(GetIcon(), iconFont, iconBrush, iconRect, iconFormat);

                // رسم العنوان
                var titleFont = new Font("Tahoma", 10, FontStyle.Bold);
                var titleBrush = new SolidBrush(Color.White);
                var titleRect = new Rectangle(50, 10, Width - 70, 20);
                
                var titleFormat = new StringFormat
                {
                    Alignment = StringAlignment.Near,
                    LineAlignment = StringAlignment.Center,
                    FormatFlags = StringFormatFlags.DirectionRightToLeft
                };
                
                g.DrawString(_title, titleFont, titleBrush, titleRect, titleFormat);

                // رسم الرسالة
                var messageFont = new Font("Tahoma", 9);
                var messageBrush = new SolidBrush(Color.White);
                var messageRect = new Rectangle(50, 35, Width - 70, Height - 45);
                
                var messageFormat = new StringFormat
                {
                    Alignment = StringAlignment.Near,
                    LineAlignment = StringAlignment.Near,
                    FormatFlags = StringFormatFlags.DirectionRightToLeft,
                    Trimming = StringTrimming.EllipsisWord
                };
                
                g.DrawString(_message, messageFont, messageBrush, messageRect, messageFormat);

                // رسم زر الإغلاق
                var closeRect = new Rectangle(Width - 25, 5, 20, 20);
                var closeBrush = new SolidBrush(Color.FromArgb(100, Color.White));
                g.FillEllipse(closeBrush, closeRect);
                
                var closeFont = new Font("Segoe UI", 8, FontStyle.Bold);
                var closeTextBrush = new SolidBrush(Color.White);
                g.DrawString("×", closeFont, closeTextBrush, closeRect, iconFormat);

                // تنظيف الموارد
                iconFont.Dispose();
                iconBrush.Dispose();
                titleFont.Dispose();
                titleBrush.Dispose();
                messageFont.Dispose();
                messageBrush.Dispose();
                closeBrush.Dispose();
                closeFont.Dispose();
                closeTextBrush.Dispose();
                iconFormat.Dispose();
                titleFormat.Dispose();
                messageFormat.Dispose();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في رسم الإشعار: {ex.Message}");
            }
        }

        private GraphicsPath CreateRoundedRectangle(Rectangle rect, int radius)
        {
            var path = new GraphicsPath();
            
            path.AddArc(rect.X, rect.Y, radius * 2, radius * 2, 180, 90);
            path.AddArc(rect.Right - radius * 2, rect.Y, radius * 2, radius * 2, 270, 90);
            path.AddArc(rect.Right - radius * 2, rect.Bottom - radius * 2, radius * 2, radius * 2, 0, 90);
            path.AddArc(rect.X, rect.Bottom - radius * 2, radius * 2, radius * 2, 90, 90);
            path.CloseFigure();
            
            return path;
        }

        private void DisplayTimer_Tick(object sender, EventArgs e)
        {
            StartHideAnimation();
        }

        private void AnimationTimer_Tick(object sender, EventArgs e)
        {
            const int totalSteps = ANIMATION_DURATION / 16;
            _animationStep++;

            if (_isClosing)
            {
                // حركة الإخفاء
                float progress = (float)_animationStep / totalSteps;
                int targetY = Screen.PrimaryScreen.WorkingArea.Bottom;
                int startY = targetY - NOTIFICATION_HEIGHT - NOTIFICATION_MARGIN;
                int currentY = (int)(startY + (targetY - startY) * progress);
                
                this.Location = new Point(this.Location.X, currentY);

                if (_animationStep >= totalSteps)
                {
                    _animationTimer.Stop();
                    this.Close();
                }
            }
            else
            {
                // حركة الظهور
                float progress = (float)_animationStep / totalSteps;
                int targetY = Screen.PrimaryScreen.WorkingArea.Bottom - NOTIFICATION_HEIGHT - NOTIFICATION_MARGIN;
                int startY = targetY + NOTIFICATION_HEIGHT;
                int currentY = (int)(startY + (targetY - startY) * progress);
                
                this.Location = new Point(this.Location.X, currentY);

                if (_animationStep >= totalSteps)
                {
                    _animationTimer.Stop();
                }
            }
        }

        private void NotificationForm_Click(object sender, EventArgs e)
        {
            StartHideAnimation();
        }

        private void NotificationForm_MouseEnter(object sender, EventArgs e)
        {
            _displayTimer.Stop();
        }

        private void NotificationForm_MouseLeave(object sender, EventArgs e)
        {
            if (!_isClosing)
            {
                _displayTimer.Start();
            }
        }
        #endregion

        #region Cleanup
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _displayTimer?.Dispose();
                _animationTimer?.Dispose();
            }
            base.Dispose(disposing);
        }
        #endregion
    }
}
