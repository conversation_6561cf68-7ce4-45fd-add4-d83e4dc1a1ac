using System;
using System.Drawing;
using System.Windows.Forms;
using Awqaf_Managment.Common;
using Awqaf_Managment.Common.Helpers;
using Awqaf_Managment.Services.Security;

namespace Awqaf_Managment.UI.Forms
{
    public partial class LoginForm : Form
    {
        public LoginForm()
        {
            InitializeComponent();
            InitializeCustomComponents();
        }

        private void InitializeCustomComponents()
        {
            // تطبيق دعم RTL
            UIHelper.ApplyRTLSupport(this);
            
            // تطبيق الخط العربي
            UIHelper.ApplyArabicFont(this);
            
            // تخصيص الألوان
            this.BackColor = Constants.BackgroundColor;
            
            // إعداد أحداث الأزرار
            btnLogin.Click += BtnLogin_Click;
            btnCancel.Click += BtnCancel_Click;
            
            // إعداد Enter للدخول
            this.AcceptButton = btnLogin;
            this.CancelButton = btnCancel;
            
            // تركيز على حقل اسم المستخدم
            txtUsername.Focus();
        }

        private void BtnLogin_Click(object sender, EventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (ValidateInput())
                {
                    // إظهار مؤشر التحميل
                    btnLogin.Enabled = false;
                    btnLogin.Text = "جاري التحقق...";
                    this.Cursor = Cursors.WaitCursor;

                    // محاولة تسجيل الدخول
                    var loginResult = AuthenticationService.Login(txtUsername.Text.Trim(), txtPassword.Text);

                    if (loginResult.IsSuccess)
                    {
                        // تسجيل دخول ناجح
                        this.Hide();
                        var mainForm = new MainForm();
                        mainForm.ShowDialog();
                        this.Close();
                    }
                    else
                    {
                        // فشل في تسجيل الدخول
                        UIHelper.ShowError(loginResult.Message);
                        txtPassword.Clear();
                        txtUsername.Focus();
                    }
                }
            }
            catch (Exception ex)
            {
                UIHelper.ShowError($"حدث خطأ أثناء تسجيل الدخول: {ex.Message}");
            }
            finally
            {
                // إعادة تعيين حالة الزر
                btnLogin.Enabled = true;
                btnLogin.Text = "دخول";
                this.Cursor = Cursors.Default;
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            if (UIHelper.ShowConfirm("هل تريد إغلاق التطبيق؟") == DialogResult.Yes)
            {
                Application.Exit();
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                UIHelper.ShowError("يرجى إدخال اسم المستخدم");
                txtUsername.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                UIHelper.ShowError("يرجى إدخال كلمة المرور");
                txtPassword.Focus();
                return false;
            }

            return true;
        }

        private void LoginForm_Load(object sender, EventArgs e)
        {
            // تطبيق التنسيقات الإضافية عند تحميل النموذج
            this.CenterToScreen();
        }

        private void btnLogin_Click_1(object sender, EventArgs e)
        {

        }
    }
}
