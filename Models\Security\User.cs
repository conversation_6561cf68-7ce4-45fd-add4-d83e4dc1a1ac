using System;
using System.Collections.Generic;

namespace Awqaf_Managment.Models.Security
{
    /// <summary>
    /// نموذج بيانات المستخدم
    /// </summary>
    public class User
    {
        public int UserId { get; set; }
        public string Username { get; set; }
        public string PasswordHash { get; set; }
        public string Salt { get; set; }
        public string FullName { get; set; }
        public string Email { get; set; }
        public string Phone { get; set; }
        public bool IsActive { get; set; }
        public bool IsLocked { get; set; }
        public int FailedLoginAttempts { get; set; }
        public DateTime? LastLoginDate { get; set; }
        public DateTime LastPasswordChange { get; set; }
        public DateTime? PasswordExpiryDate { get; set; }
        public DateTime CreatedDate { get; set; }
        public int? CreatedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public int? ModifiedBy { get; set; }

        // خصائص إضافية للعرض
        public List<Role> Roles { get; set; } = new List<Role>();
        public List<Permission> Permissions { get; set; } = new List<Permission>();
        public string RolesDisplay => string.Join(", ", Roles?.ConvertAll(r => r.RoleNameAr) ?? new List<string>());

        public User()
        {
            IsActive = true;
            IsLocked = false;
            FailedLoginAttempts = 0;
            CreatedDate = DateTime.Now;
            LastPasswordChange = DateTime.Now;
        }
    }

    /// <summary>
    /// نموذج بيانات الدور
    /// </summary>
    public class Role
    {
        public int RoleId { get; set; }
        public string RoleName { get; set; }
        public string RoleNameAr { get; set; }
        public string Description { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }
        public int? CreatedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public int? ModifiedBy { get; set; }

        // خصائص إضافية
        public List<Permission> Permissions { get; set; } = new List<Permission>();
        public int UsersCount { get; set; }

        public Role()
        {
            IsActive = true;
            CreatedDate = DateTime.Now;
        }
    }

    /// <summary>
    /// نموذج بيانات الصلاحية
    /// </summary>
    public class Permission
    {
        public int PermissionId { get; set; }
        public string PermissionName { get; set; }
        public string PermissionNameAr { get; set; }
        public string ModuleName { get; set; }
        public string ModuleNameAr { get; set; }
        public string Description { get; set; }
        public bool IsActive { get; set; }
        public DateTime CreatedDate { get; set; }

        // صلاحيات التحكم
        public bool CanView { get; set; }
        public bool CanAdd { get; set; }
        public bool CanEdit { get; set; }
        public bool CanDelete { get; set; }
        public bool CanPrint { get; set; }
        public bool CanExport { get; set; }

        public Permission()
        {
            IsActive = true;
            CreatedDate = DateTime.Now;
        }
    }

    /// <summary>
    /// نموذج ربط المستخدم بالدور
    /// </summary>
    public class UserRole
    {
        public int UserRoleId { get; set; }
        public int UserId { get; set; }
        public int RoleId { get; set; }
        public DateTime AssignedDate { get; set; }
        public int? AssignedBy { get; set; }
        public bool IsActive { get; set; }

        // خصائص إضافية للعرض
        public string Username { get; set; }
        public string FullName { get; set; }
        public string RoleName { get; set; }
        public string RoleNameAr { get; set; }
        public string AssignedByName { get; set; }

        public UserRole()
        {
            AssignedDate = DateTime.Now;
            IsActive = true;
        }
    }

    /// <summary>
    /// نموذج ربط الدور بالصلاحية
    /// </summary>
    public class RolePermission
    {
        public int RolePermissionId { get; set; }
        public int RoleId { get; set; }
        public int PermissionId { get; set; }
        public bool CanView { get; set; }
        public bool CanAdd { get; set; }
        public bool CanEdit { get; set; }
        public bool CanDelete { get; set; }
        public bool CanPrint { get; set; }
        public bool CanExport { get; set; }
        public DateTime CreatedDate { get; set; }
        public int? CreatedBy { get; set; }

        // خصائص إضافية للعرض
        public string RoleName { get; set; }
        public string RoleNameAr { get; set; }
        public string PermissionName { get; set; }
        public string PermissionNameAr { get; set; }
        public string ModuleName { get; set; }
        public string ModuleNameAr { get; set; }

        public RolePermission()
        {
            CreatedDate = DateTime.Now;
        }
    }

    /// <summary>
    /// نموذج سجل تسجيل الدخول
    /// </summary>
    public class LoginHistory
    {
        public int LoginId { get; set; }
        public int? UserId { get; set; }
        public string Username { get; set; }
        public DateTime LoginDate { get; set; }
        public string IPAddress { get; set; }
        public string UserAgent { get; set; }
        public bool IsSuccessful { get; set; }
        public string FailureReason { get; set; }
        public DateTime? LogoutDate { get; set; }

        // خصائص إضافية للعرض
        public string FullName { get; set; }
        public string StatusText => IsSuccessful ? "نجح" : "فشل";
        public string DurationText
        {
            get
            {
                if (LogoutDate.HasValue && IsSuccessful)
                {
                    var duration = LogoutDate.Value - LoginDate;
                    return $"{duration.Hours:00}:{duration.Minutes:00}:{duration.Seconds:00}";
                }
                return IsSuccessful ? "متصل" : "-";
            }
        }

        public LoginHistory()
        {
            LoginDate = DateTime.Now;
        }
    }

    /// <summary>
    /// نتيجة تسجيل الدخول
    /// </summary>
    public class LoginResult
    {
        public bool IsSuccess { get; set; }
        public string Message { get; set; }
        public User User { get; set; }
        public string ErrorCode { get; set; }

        public LoginResult()
        {
        }

        public LoginResult(bool isSuccess, string message, User user = null, string errorCode = null)
        {
            IsSuccess = isSuccess;
            Message = message;
            User = user;
            ErrorCode = errorCode;
        }

        public static LoginResult Success(User user, string message = "تم تسجيل الدخول بنجاح")
        {
            return new LoginResult(true, message, user);
        }

        public static LoginResult Failure(string message, string errorCode = null)
        {
            return new LoginResult(false, message, null, errorCode);
        }

        public static LoginResult InvalidCredentials()
        {
            return Failure("اسم المستخدم أو كلمة المرور غير صحيحة", "INVALID_CREDENTIALS");
        }

        public static LoginResult AccountLocked()
        {
            return Failure("الحساب مقفل. يرجى الاتصال بالمدير", "ACCOUNT_LOCKED");
        }

        public static LoginResult AccountInactive()
        {
            return Failure("الحساب غير نشط. يرجى الاتصال بالمدير", "ACCOUNT_INACTIVE");
        }

        public static LoginResult TooManyAttempts()
        {
            return Failure("تم تجاوز عدد المحاولات المسموحة. يرجى المحاولة لاحقاً", "TOO_MANY_ATTEMPTS");
        }

        public static LoginResult SystemError(string details = null)
        {
            string message = "حدث خطأ في النظام";
            if (!string.IsNullOrEmpty(details))
                message += $": {details}";
            return Failure(message, "SYSTEM_ERROR");
        }
    }

    /// <summary>
    /// أنواع الصلاحيات
    /// </summary>
    public enum PermissionAction
    {
        View = 1,
        Add = 2,
        Edit = 3,
        Delete = 4,
        Print = 5,
        Export = 6
    }

    /// <summary>
    /// امتدادات أنواع الصلاحيات
    /// </summary>
    public static class PermissionActionExtensions
    {
        public static string ToArabicString(this PermissionAction action)
        {
            switch (action)
            {
                case PermissionAction.View:
                    return "عرض";
                case PermissionAction.Add:
                    return "إضافة";
                case PermissionAction.Edit:
                    return "تعديل";
                case PermissionAction.Delete:
                    return "حذف";
                case PermissionAction.Print:
                    return "طباعة";
                case PermissionAction.Export:
                    return "تصدير";
                default:
                    return action.ToString();
            }
        }

        public static string ToEnglishString(this PermissionAction action)
        {
            return action.ToString();
        }
    }
}
