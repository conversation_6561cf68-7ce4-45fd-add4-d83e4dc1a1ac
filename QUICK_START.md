# دليل البدء السريع - Quick Start Guide

## خطوات التشغيل السريع

### 1. تشغيل SQL Server
```cmd
# تشغيل كمدير (Run as Administrator)
net start "MSSQLSERVER"
```

### 2. إنشاء قاعدة البيانات
```cmd
# تشغيل سكريبت الإعداد
setup_database.bat
```

### 3. تشغيل التطبيق
```cmd
# تشغيل التطبيق
bin\Debug\Awqaf_Managment.exe
```

## بيانات الدخول الافتراضية

- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

## الوحدات المتاحة

✅ **إدارة المستخدمين** - مكتملة ومجربة
- إضافة وتعديل وحذف المستخدمين
- إدارة الأدوار والصلاحيات
- تسجيل الدخول والخروج

🚧 **الوحدات الأخرى** - في مرحلة التطوير
- إدارة العقارات
- إدارة المستأجرين  
- إدارة العقود
- إدارة المدفوعات
- إدارة الصيانة
- إدارة الموظفين
- إدارة الأصول
- التقارير
- الإعدادات

## حل المشاكل الشائعة

### خطأ في الاتصال بقاعدة البيانات
```
تأكد من:
1. تشغيل خدمة SQL Server
2. وجود قاعدة البيانات AwqafManagement
3. صحة سلسلة الاتصال في App.config
```

### خطأ في تسجيل الدخول
```
استخدم بيانات الدخول الافتراضية:
- اسم المستخدم: admin
- كلمة المرور: admin123
```

### خطأ في البناء
```
تأكد من:
1. تثبيت .NET Framework 4.8
2. تثبيت Visual Studio Build Tools
3. تشغيل build.bat
```

## الخطوات التالية

بعد تشغيل النظام بنجاح:

1. **تغيير كلمة مرور المدير**
   - اذهب إلى إدارة المستخدمين
   - اختر المستخدم admin
   - غير كلمة المرور

2. **إضافة مستخدمين جدد**
   - اذهب إلى إدارة المستخدمين
   - اضغط "إضافة مستخدم جديد"
   - املأ البيانات المطلوبة

3. **إعداد الأدوار والصلاحيات**
   - راجع الأدوار الموجودة
   - أضف أدوار جديدة حسب الحاجة
   - اربط المستخدمين بالأدوار المناسبة

## معلومات تقنية سريعة

- **قاعدة البيانات:** AwqafManagement
- **خادم قاعدة البيانات:** . (Local SQL Server)
- **ملف التكوين:** App.config
- **مجلد التطبيق:** bin\Debug\
- **ملفات قاعدة البيانات:** Database\

---
**ملاحظة:** هذا النظام في مرحلة التطوير. وحدة إدارة المستخدمين مكتملة والوحدات الأخرى قيد التطوير.
