-- إدراج الحسابات الرئيسية والفرعية - نسخة مبسطة
-- Insert Main and Sub Accounts - Simple Version

USE AwqafManagement;
GO

-- حذف البيانات الموجودة وإعادة تعيين الهوية
DELETE FROM ChartOfAccounts;
DBCC CHECKIDENT ('ChartOfAccounts', RESEED, 0);
GO

-- إدراج الحسابات الرئيسية والفرعية
SET IDENTITY_INSERT ChartOfAccounts ON;

-- ===== 1. الأصول (Assets) =====
INSERT INTO ChartOfAccounts (AccountId, AccountCode, AccountName, AccountNameAr, AccountTypeId, AccountGroupId, ParentAccountId, AccountLevel, IsParent, IsActive, AllowPosting, Description, CurrencyCode, OpeningBalance, CurrentBalance, CreatedDate, CreatedBy)
VALUES 
-- 1.0 الأصول - الحساب الرئيسي
(1, N'1.00.000', N'Assets', N'الأصول', 1, 1, NULL, 1, 1, 1, 0, N'مجموعة الأصول الرئيسية', N'SAR', 0.00, 0.00, GETDATE(), 1);

-- 1.1 الأصول المتداولة
INSERT INTO ChartOfAccounts (AccountId, AccountCode, AccountName, AccountNameAr, AccountTypeId, AccountGroupId, ParentAccountId, AccountLevel, IsParent, IsActive, AllowPosting, Description, CurrencyCode, OpeningBalance, CurrentBalance, CreatedDate, CreatedBy)
VALUES 
(2, N'1.01.000', N'Current Assets', N'الأصول المتداولة', 1, 1, 1, 2, 1, 1, 0, N'الأصول قصيرة الأجل', N'SAR', 0.00, 0.00, GETDATE(), 1);

-- 1.1.1 النقدية والبنوك
INSERT INTO ChartOfAccounts (AccountId, AccountCode, AccountName, AccountNameAr, AccountTypeId, AccountGroupId, ParentAccountId, AccountLevel, IsParent, IsActive, AllowPosting, Description, CurrencyCode, OpeningBalance, CurrentBalance, CreatedDate, CreatedBy)
VALUES 
(3, N'1.01.001', N'Main Cash Box', N'الصندوق الرئيسي', 1, 1, 2, 3, 0, 1, 1, N'صندوق النقدية الرئيسي', N'SAR', 50000.00, 50000.00, GETDATE(), 1),
(4, N'1.01.002', N'Sub Cash Box', N'صندوق فرعي', 1, 1, 2, 3, 0, 1, 1, N'صندوق نقدية فرعي', N'SAR', 15000.00, 15000.00, GETDATE(), 1),
(5, N'1.01.003', N'National Commercial Bank', N'البنك الأهلي التجاري', 1, 1, 2, 3, 0, 1, 1, N'حساب البنك الأهلي التجاري', N'SAR', 250000.00, 250000.00, GETDATE(), 1),
(6, N'1.01.004', N'Al Rajhi Bank', N'بنك الراجحي', 1, 1, 2, 3, 0, 1, 1, N'حساب بنك الراجحي', N'SAR', 180000.00, 180000.00, GETDATE(), 1),
(7, N'1.01.005', N'Riyad Bank', N'بنك الرياض', 1, 1, 2, 3, 0, 1, 1, N'حساب بنك الرياض', N'SAR', 120000.00, 120000.00, GETDATE(), 1);

-- 1.2 الأصول الثابتة
INSERT INTO ChartOfAccounts (AccountId, AccountCode, AccountName, AccountNameAr, AccountTypeId, AccountGroupId, ParentAccountId, AccountLevel, IsParent, IsActive, AllowPosting, Description, CurrencyCode, OpeningBalance, CurrentBalance, CreatedDate, CreatedBy)
VALUES 
(8, N'1.02.000', N'Fixed Assets', N'الأصول الثابتة', 1, 1, 1, 2, 1, 1, 0, N'الأصول طويلة الأجل', N'SAR', 0.00, 0.00, GETDATE(), 1),
(9, N'1.02.001', N'Land and Real Estate', N'الأراضي والعقارات', 1, 1, 8, 3, 0, 1, 1, N'الأراضي والعقارات الوقفية', N'SAR', 2500000.00, 2500000.00, GETDATE(), 1),
(10, N'1.02.002', N'Buildings and Constructions', N'المباني والإنشاءات', 1, 1, 8, 3, 0, 1, 1, N'المباني والإنشاءات الوقفية', N'SAR', 1800000.00, 1800000.00, GETDATE(), 1);

-- ===== 2. الخصوم (Liabilities) =====
INSERT INTO ChartOfAccounts (AccountId, AccountCode, AccountName, AccountNameAr, AccountTypeId, AccountGroupId, ParentAccountId, AccountLevel, IsParent, IsActive, AllowPosting, Description, CurrencyCode, OpeningBalance, CurrentBalance, CreatedDate, CreatedBy)
VALUES 
(11, N'2.00.000', N'Liabilities', N'الخصوم', 2, 2, NULL, 1, 1, 1, 0, N'مجموعة الخصوم الرئيسية', N'SAR', 0.00, 0.00, GETDATE(), 1),
(12, N'2.01.000', N'Current Liabilities', N'الخصوم المتداولة', 2, 2, 11, 2, 1, 1, 0, N'الخصوم قصيرة الأجل', N'SAR', 0.00, 0.00, GETDATE(), 1),
(13, N'2.01.001', N'Accounts Payable', N'الموردون والذمم الدائنة', 2, 2, 12, 3, 0, 1, 1, N'الموردون والذمم الدائنة', N'SAR', 25000.00, 25000.00, GETDATE(), 1);

-- ===== 3. حقوق الملكية (Equity) =====
INSERT INTO ChartOfAccounts (AccountId, AccountCode, AccountName, AccountNameAr, AccountTypeId, AccountGroupId, ParentAccountId, AccountLevel, IsParent, IsActive, AllowPosting, Description, CurrencyCode, OpeningBalance, CurrentBalance, CreatedDate, CreatedBy)
VALUES 
(14, N'3.00.000', N'Equity', N'حقوق الملكية', 3, 3, NULL, 1, 1, 1, 0, N'مجموعة حقوق الملكية', N'SAR', 0.00, 0.00, GETDATE(), 1),
(15, N'3.01.001', N'Endowment Capital', N'رأس المال الوقفي', 3, 3, 14, 3, 0, 1, 1, N'رأس المال الوقفي الأساسي', N'SAR', 5000000.00, 5000000.00, GETDATE(), 1);

-- ===== 4. الإيرادات (Revenues) =====
INSERT INTO ChartOfAccounts (AccountId, AccountCode, AccountName, AccountNameAr, AccountTypeId, AccountGroupId, ParentAccountId, AccountLevel, IsParent, IsActive, AllowPosting, Description, CurrencyCode, OpeningBalance, CurrentBalance, CreatedDate, CreatedBy)
VALUES 
(16, N'4.00.000', N'Revenues', N'الإيرادات', 4, 4, NULL, 1, 1, 1, 0, N'مجموعة الإيرادات الرئيسية', N'SAR', 0.00, 0.00, GETDATE(), 1),
(17, N'4.01.001', N'Residential Rental Income', N'إيرادات الإيجارات السكنية', 4, 4, 16, 3, 0, 1, 1, N'إيرادات إيجار العقارات السكنية', N'SAR', 0.00, 0.00, GETDATE(), 1),
(18, N'4.01.002', N'Commercial Rental Income', N'إيرادات الإيجارات التجارية', 4, 4, 16, 3, 0, 1, 1, N'إيرادات إيجار العقارات التجارية', N'SAR', 0.00, 0.00, GETDATE(), 1);

-- ===== 5. المصروفات (Expenses) =====
INSERT INTO ChartOfAccounts (AccountId, AccountCode, AccountName, AccountNameAr, AccountTypeId, AccountGroupId, ParentAccountId, AccountLevel, IsParent, IsActive, AllowPosting, Description, CurrencyCode, OpeningBalance, CurrentBalance, CreatedDate, CreatedBy)
VALUES 
(19, N'5.00.000', N'Expenses', N'المصروفات', 5, 5, NULL, 1, 1, 1, 0, N'مجموعة المصروفات الرئيسية', N'SAR', 0.00, 0.00, GETDATE(), 1),
(20, N'5.01.001', N'Salaries and Wages', N'الرواتب والأجور', 5, 5, 19, 3, 0, 1, 1, N'رواتب وأجور الموظفين', N'SAR', 0.00, 0.00, GETDATE(), 1),
(21, N'5.01.002', N'Maintenance Expenses', N'مصروفات الصيانة', 5, 5, 19, 3, 0, 1, 1, N'مصروفات صيانة العقارات', N'SAR', 0.00, 0.00, GETDATE(), 1),
(22, N'5.01.003', N'Utilities Expenses', N'مصروفات الكهرباء والماء', 5, 5, 19, 3, 0, 1, 1, N'مصروفات الكهرباء والماء والهاتف', N'SAR', 0.00, 0.00, GETDATE(), 1);

SET IDENTITY_INSERT ChartOfAccounts OFF;
GO

-- عرض النتائج
SELECT COUNT(*) as 'إجمالي الحسابات المدرجة' FROM ChartOfAccounts;

SELECT 
    AccountCode as 'رمز الحساب',
    AccountNameAr as 'اسم الحساب',
    AccountLevel as 'مستوى الحساب'
FROM ChartOfAccounts 
ORDER BY AccountCode;

PRINT N'تم إدراج 22 حساب محاسبي أساسي بنجاح';
