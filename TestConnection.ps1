# اختبار الاتصال بقاعدة البيانات وتحميل البيانات
# Database Connection and Data Loading Test

Write-Host "=== اختبار الاتصال بقاعدة البيانات ===" -ForegroundColor Green
Write-Host "=== Database Connection Test ===" -ForegroundColor Green
Write-Host ""

$connectionString = "Server=NAJEEB;Database=AwqafManagement;Integrated Security=true;TrustServerCertificate=true;"

try {
    # تحميل مكتبة SQL Server
    Add-Type -AssemblyName "System.Data"
    
    Write-Host "1. اختبار الاتصال..." -ForegroundColor Yellow
    Write-Host "1. Testing connection..." -ForegroundColor Yellow
    
    $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    $connection.Open()
    
    Write-Host "✓ تم الاتصال بنجاح" -ForegroundColor Green
    Write-Host "✓ Connection successful" -ForegroundColor Green
    
    # اختبار اسم قاعدة البيانات
    $command = New-Object System.Data.SqlClient.SqlCommand("SELECT DB_NAME()", $connection)
    $dbName = $command.ExecuteScalar()
    Write-Host "   اسم قاعدة البيانات: $dbName" -ForegroundColor Cyan
    Write-Host "   Database name: $dbName" -ForegroundColor Cyan
    Write-Host ""
    
    # اختبار عدد الحسابات
    Write-Host "2. اختبار بيانات الحسابات..." -ForegroundColor Yellow
    Write-Host "2. Testing accounts data..." -ForegroundColor Yellow
    
    $accountsQuery = "SELECT COUNT(*) FROM ChartOfAccounts WHERE IsActive = 1"
    $accountsCommand = New-Object System.Data.SqlClient.SqlCommand($accountsQuery, $connection)
    $accountsCount = $accountsCommand.ExecuteScalar()
    
    Write-Host "   عدد الحسابات: $accountsCount" -ForegroundColor Cyan
    Write-Host "   Number of accounts: $accountsCount" -ForegroundColor Cyan
    
    # اختبار أنواع الحسابات
    $typesQuery = "SELECT COUNT(*) FROM AccountTypes WHERE IsActive = 1"
    $typesCommand = New-Object System.Data.SqlClient.SqlCommand($typesQuery, $connection)
    $typesCount = $typesCommand.ExecuteScalar()
    
    Write-Host "   عدد أنواع الحسابات: $typesCount" -ForegroundColor Cyan
    Write-Host "   Number of account types: $typesCount" -ForegroundColor Cyan
    
    # اختبار مجموعات الحسابات
    $groupsQuery = "SELECT COUNT(*) FROM AccountGroups WHERE IsActive = 1"
    $groupsCommand = New-Object System.Data.SqlClient.SqlCommand($groupsQuery, $connection)
    $groupsCount = $groupsCommand.ExecuteScalar()
    
    Write-Host "   عدد مجموعات الحسابات: $groupsCount" -ForegroundColor Cyan
    Write-Host "   Number of account groups: $groupsCount" -ForegroundColor Cyan
    Write-Host ""
    
    # عرض عينة من الحسابات
    if ($accountsCount -gt 0) {
        Write-Host "3. عرض عينة من الحسابات..." -ForegroundColor Yellow
        Write-Host "3. Showing sample accounts..." -ForegroundColor Yellow
        
        $sampleQuery = @"
            SELECT TOP 10 
                AccountCode,
                AccountNameAr,
                AccountLevel,
                CASE WHEN ParentAccountId IS NULL THEN N'رئيسي' ELSE N'فرعي' END as AccountType,
                OpeningBalance
            FROM ChartOfAccounts 
            WHERE IsActive = 1 
            ORDER BY AccountCode
"@
        
        $sampleCommand = New-Object System.Data.SqlClient.SqlCommand($sampleQuery, $connection)
        $reader = $sampleCommand.ExecuteReader()
        
        Write-Host "   رمز الحساب`t`tاسم الحساب`t`t`tالمستوى`tالنوع`t`tالرصيد" -ForegroundColor White
        Write-Host "   ----------`t`t----------`t`t`t------`t----`t`t------" -ForegroundColor White
        
        while ($reader.Read()) {
            $code = $reader["AccountCode"]
            $nameAr = $reader["AccountNameAr"]
            $level = $reader["AccountLevel"]
            $type = $reader["AccountType"]
            $balance = $reader["OpeningBalance"]
            
            Write-Host "   $code`t`t$nameAr`t`t$level`t$type`t$balance" -ForegroundColor Cyan
        }
        
        $reader.Close()
    }
    
    $connection.Close()
    
    Write-Host ""
    Write-Host "=== انتهى الاختبار بنجاح ===" -ForegroundColor Green
    Write-Host "=== Test completed successfully ===" -ForegroundColor Green
    
} catch {
    Write-Host "❌ خطأ في الاختبار: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "❌ Test error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "اضغط أي مفتاح للمتابعة..." -ForegroundColor Yellow
Write-Host "Press any key to continue..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
