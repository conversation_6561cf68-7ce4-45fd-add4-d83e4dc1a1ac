-- ===================================================================
-- جداول نظام الأمان والمستخدمين
-- ===================================================================

USE AwqafManagement;
GO

-- جدول الأدوار (Roles)
CREATE TABLE Roles (
    RoleId INT IDENTITY(1,1) PRIMARY KEY,
    RoleName NVARCHAR(50) NOT NULL UNIQUE,
    RoleNameAr NVARCHAR(100) NOT NULL,
    Description NVARCHAR(255),
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    CreatedBy INT,
    ModifiedDate DATETIME2,
    ModifiedBy INT
);

-- جدول الصلاحيات (Permissions)
CREATE TABLE Permissions (
    PermissionId INT IDENTITY(1,1) PRIMARY KEY,
    PermissionName NVARCHAR(50) NOT NULL UNIQUE,
    PermissionNameAr NVARCHAR(100) NOT NULL,
    ModuleName NVARCHAR(50) NOT NULL,
    ModuleNameAr NVARCHAR(100) NOT NULL,
    Description NVARCHAR(255),
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE()
);

-- جدول ربط الأدوار بالصلاحيات (RolePermissions)
CREATE TABLE RolePermissions (
    RolePermissionId INT IDENTITY(1,1) PRIMARY KEY,
    RoleId INT NOT NULL,
    PermissionId INT NOT NULL,
    CanView BIT NOT NULL DEFAULT 0,
    CanAdd BIT NOT NULL DEFAULT 0,
    CanEdit BIT NOT NULL DEFAULT 0,
    CanDelete BIT NOT NULL DEFAULT 0,
    CanPrint BIT NOT NULL DEFAULT 0,
    CanExport BIT NOT NULL DEFAULT 0,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    CreatedBy INT,
    CONSTRAINT FK_RolePermissions_Role FOREIGN KEY (RoleId) REFERENCES Roles(RoleId),
    CONSTRAINT FK_RolePermissions_Permission FOREIGN KEY (PermissionId) REFERENCES Permissions(PermissionId),
    CONSTRAINT UK_RolePermissions UNIQUE (RoleId, PermissionId)
);

-- جدول المستخدمين (Users)
CREATE TABLE Users (
    UserId INT IDENTITY(1,1) PRIMARY KEY,
    Username NVARCHAR(50) NOT NULL UNIQUE,
    PasswordHash NVARCHAR(255) NOT NULL,
    Salt NVARCHAR(50) NOT NULL,
    FullName NVARCHAR(100) NOT NULL,
    Email NVARCHAR(100),
    Phone NVARCHAR(20),
    IsActive BIT NOT NULL DEFAULT 1,
    IsLocked BIT NOT NULL DEFAULT 0,
    FailedLoginAttempts INT NOT NULL DEFAULT 0,
    LastLoginDate DATETIME2,
    LastPasswordChange DATETIME2 NOT NULL DEFAULT GETDATE(),
    PasswordExpiryDate DATETIME2,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    CreatedBy INT,
    ModifiedDate DATETIME2,
    ModifiedBy INT
);

-- جدول ربط المستخدمين بالأدوار (UserRoles)
CREATE TABLE UserRoles (
    UserRoleId INT IDENTITY(1,1) PRIMARY KEY,
    UserId INT NOT NULL,
    RoleId INT NOT NULL,
    AssignedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    AssignedBy INT,
    IsActive BIT NOT NULL DEFAULT 1,
    CONSTRAINT FK_UserRoles_User FOREIGN KEY (UserId) REFERENCES Users(UserId),
    CONSTRAINT FK_UserRoles_Role FOREIGN KEY (RoleId) REFERENCES Roles(RoleId),
    CONSTRAINT UK_UserRoles UNIQUE (UserId, RoleId)
);

-- جدول سجل تسجيل الدخول (LoginHistory)
CREATE TABLE LoginHistory (
    LoginId INT IDENTITY(1,1) PRIMARY KEY,
    UserId INT,
    Username NVARCHAR(50) NOT NULL,
    LoginDate DATETIME2 NOT NULL DEFAULT GETDATE(),
    IPAddress NVARCHAR(45),
    UserAgent NVARCHAR(255),
    IsSuccessful BIT NOT NULL,
    FailureReason NVARCHAR(255),
    LogoutDate DATETIME2,
    CONSTRAINT FK_LoginHistory_User FOREIGN KEY (UserId) REFERENCES Users(UserId)
);

-- إنشاء الفهارس
CREATE INDEX IX_Users_Username ON Users(Username);
CREATE INDEX IX_Users_Email ON Users(Email);
CREATE INDEX IX_Users_IsActive ON Users(IsActive);
CREATE INDEX IX_LoginHistory_UserId ON LoginHistory(UserId);
CREATE INDEX IX_LoginHistory_LoginDate ON LoginHistory(LoginDate);
CREATE INDEX IX_RolePermissions_RoleId ON RolePermissions(RoleId);
CREATE INDEX IX_UserRoles_UserId ON UserRoles(UserId);

PRINT N'تم إنشاء جداول نظام الأمان والمستخدمين بنجاح';
GO
