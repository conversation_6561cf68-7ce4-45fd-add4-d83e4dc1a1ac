## About

The legacy .NET Data Provider for SQL Server. These classes provide access to versions of SQL Server and encapsulate database-specific protocols, including tabular data stream (TDS).

**System.Data.SqlClient is deprecated. Please use Microsoft.Data.SqlClient instead.**

## Main Types

The main types provided by this library are:

- System.Data.SqlClient.SqlConnection
- System.Data.SqlClient.SqlException
- System.Data.SqlClient.SqlParameter
- System.Data.SqlDbType
- System.Data.SqlClient.SqlDataReader
- System.Data.SqlClient.SqlCommand
- System.Data.SqlClient.SqlTransaction
- System.Data.SqlClient.SqlParameterCollection
- System.Data.SqlClient.SqlClientFactory

## Additional Documentation

- [Introducing the new Microsoft.Data.SqlClient](https://devblogs.microsoft.com/dotnet/introducing-the-new-microsoftdatasqlclient/)
- [https://techcommunity.microsoft.com/t5/sql-server-blog/announcement-system-data-sqlclient-package-is-now-deprecated/ba-p/4227205](Announcement: System.Data.SqlClient package is now deprecated)
- For conceptual information about using this namespace when programming with .NET, see [SQL Server and ADO.NET](https://learn.microsoft.com/en-us/dotnet/framework/data/adonet/sql/).
- API reference for System.Data.SqlClient can be found in: https://learn.microsoft.com/en-us/dotnet/api/system.data.sqlclient?view=netframework-4.8.1
- API reference for Microsoft.Data.SqlClient can be found in: https://learn.microsoft.com/en-us/dotnet/api/microsoft.data.sqlclient

## Related Packages

- https://www.nuget.org/packages/Microsoft.Data.SqlClient

## License

System.Data.SqlClient is released as open source under the [MIT license](https://licenses.nuget.org/MIT).