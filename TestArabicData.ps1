# ===================================================================
# اختبار البيانات العربية الجديدة
# Test New Arabic Data
# ===================================================================

Write-Host "=== اختبار البيانات العربية الجديدة ===" -ForegroundColor Green
Write-Host "=== Testing New Arabic Data ===" -ForegroundColor Green
Write-Host ""

try {
    # الاتصال بقاعدة البيانات
    $connectionString = "Server=NAJEEB;Database=AwqafManagement;Integrated Security=true;TrustServerCertificate=true"
    $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    $connection.Open()
    
    Write-Host "✓ تم الاتصال بقاعدة البيانات بنجاح" -ForegroundColor Green
    Write-Host "✓ Database connection successful" -ForegroundColor Green
    Write-Host "   اسم قاعدة البيانات: $($connection.Database)" -ForegroundColor Cyan
    Write-Host "   Database name: $($connection.Database)" -ForegroundColor Cyan
    Write-Host ""
    
    # اختبار البيانات
    Write-Host "=== إحصائيات البيانات ===" -ForegroundColor Yellow
    Write-Host "=== Data Statistics ===" -ForegroundColor Yellow
    
    # عدد الحسابات
    $cmd = $connection.CreateCommand()
    $cmd.CommandText = "SELECT COUNT(*) FROM ChartOfAccounts WHERE IsActive = 1"
    $accountsCount = $cmd.ExecuteScalar()
    Write-Host "   عدد الحسابات: $accountsCount" -ForegroundColor White
    Write-Host "   Number of accounts: $accountsCount" -ForegroundColor White
    
    # عدد أنواع الحسابات
    $cmd.CommandText = "SELECT COUNT(*) FROM AccountTypes"
    $typesCount = $cmd.ExecuteScalar()
    Write-Host "   عدد أنواع الحسابات: $typesCount" -ForegroundColor White
    Write-Host "   Number of account types: $typesCount" -ForegroundColor White
    
    # عدد مجموعات الحسابات
    $cmd.CommandText = "SELECT COUNT(*) FROM AccountGroups"
    $groupsCount = $cmd.ExecuteScalar()
    Write-Host "   عدد مجموعات الحسابات: $groupsCount" -ForegroundColor White
    Write-Host "   Number of account groups: $groupsCount" -ForegroundColor White
    Write-Host ""
    
    # عرض الحسابات الرئيسية
    Write-Host "=== الحسابات الرئيسية ===" -ForegroundColor Yellow
    Write-Host "=== Main Accounts ===" -ForegroundColor Yellow
    
    $cmd.CommandText = @"
        SELECT AccountCode, AccountNameAr, AccountName, AccountLevel, 
               CASE WHEN IsParent = 1 THEN N'نعم' ELSE N'لا' END as IsParent,
               OpeningBalance
        FROM ChartOfAccounts 
        WHERE IsActive = 1 AND AccountLevel = 1
        ORDER BY AccountCode
"@
    
    $reader = $cmd.ExecuteReader()
    Write-Host "   رمز الحساب           اسم الحساب                      المستوى النوع           الرصيد" -ForegroundColor Cyan
    Write-Host "   ----------           ----------                      ------  ----            ------" -ForegroundColor Cyan
    
    while ($reader.Read()) {
        $code = $reader["AccountCode"]
        $nameAr = $reader["AccountNameAr"]
        $level = $reader["AccountLevel"]
        $isParent = $reader["IsParent"]
        $balance = $reader["OpeningBalance"]
        
        Write-Host "   $($code.PadRight(20)) $($nameAr.PadRight(30)) $level       $isParent    $balance" -ForegroundColor White
    }
    $reader.Close()
    Write-Host ""
    
    # عرض الحسابات الفرعية
    Write-Host "=== الحسابات الفرعية (عينة) ===" -ForegroundColor Yellow
    Write-Host "=== Sub Accounts (Sample) ===" -ForegroundColor Yellow
    
    $cmd.CommandText = @"
        SELECT TOP 10 AccountCode, AccountNameAr, AccountName, AccountLevel, 
               CASE WHEN IsParent = 1 THEN N'نعم' ELSE N'لا' END as IsParent,
               OpeningBalance
        FROM ChartOfAccounts 
        WHERE IsActive = 1 AND AccountLevel > 1
        ORDER BY AccountCode
"@
    
    $reader = $cmd.ExecuteReader()
    Write-Host "   رمز الحساب           اسم الحساب                      المستوى النوع           الرصيد" -ForegroundColor Cyan
    Write-Host "   ----------           ----------                      ------  ----            ------" -ForegroundColor Cyan
    
    while ($reader.Read()) {
        $code = $reader["AccountCode"]
        $nameAr = $reader["AccountNameAr"]
        $level = $reader["AccountLevel"]
        $isParent = $reader["IsParent"]
        $balance = $reader["OpeningBalance"]
        
        Write-Host "   $($code.PadRight(20)) $($nameAr.PadRight(30)) $level       $isParent    $balance" -ForegroundColor White
    }
    $reader.Close()
    Write-Host ""
    
    # اختبار الترميز
    Write-Host "=== اختبار الترميز العربي ===" -ForegroundColor Yellow
    Write-Host "=== Arabic Encoding Test ===" -ForegroundColor Yellow
    
    $cmd.CommandText = "SELECT TOP 3 AccountNameAr FROM ChartOfAccounts WHERE IsActive = 1 ORDER BY AccountCode"
    $reader = $cmd.ExecuteReader()
    
    $testNames = @()
    while ($reader.Read()) {
        $testNames += $reader["AccountNameAr"]
    }
    $reader.Close()
    
    foreach ($name in $testNames) {
        Write-Host "   ✓ $name" -ForegroundColor Green
    }
    
    $connection.Close()
    Write-Host ""
    Write-Host "=== انتهى الاختبار بنجاح ===" -ForegroundColor Green
    Write-Host "=== Test completed successfully ===" -ForegroundColor Green
    
} catch {
    Write-Host "❌ خطأ في الاختبار: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "❌ Test error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "اضغط أي مفتاح للمتابعة..." -ForegroundColor Yellow
Write-Host "Press any key to continue..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
