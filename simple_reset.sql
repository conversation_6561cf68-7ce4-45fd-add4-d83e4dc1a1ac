USE AwqafManagement;
GO

-- إعادة تعيين كلمة مرور المدير بطريقة بسيطة
-- سنستخدم salt بسيط وكلمة مرور مشفرة محسوبة مسبقاً

-- حذف المستخدم الحالي
DELETE FROM UserRoles WHERE UserId = (SELECT UserId FROM Users WHERE Username = 'admin');
DELETE FROM Users WHERE Username = 'admin';

-- إنشاء المستخدم من جديد مع كلمة مرور بسيطة
INSERT INTO Users (Username, PasswordHash, Salt, FullName, Email, IsActive) 
VALUES ('admin', 'admin123', 'simple', 'مدير النظام', '<EMAIL>', 1);

-- ربط المدير بدور مدير النظام
DECLARE @AdminUserId INT = (SELECT UserId FROM Users WHERE Username = 'admin');
DECLARE @AdminRoleId INT = (SELECT RoleId FROM Roles WHERE RoleName = 'Administrator');

IF @AdminRoleId IS NOT NULL
BEGIN
    INSERT INTO UserRoles (UserId, RoleId) VALUES (@AdminUserId, @AdminRoleId);
END

PRINT 'تم إعادة تعيين كلمة مرور المدير';
PRINT 'اسم المستخدم: admin';
PRINT 'كلمة المرور: admin123';

-- عرض البيانات للتأكد
SELECT Username, PasswordHash, Salt, FullName FROM Users WHERE Username = 'admin';
