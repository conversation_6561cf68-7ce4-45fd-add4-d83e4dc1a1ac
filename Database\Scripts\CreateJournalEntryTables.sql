-- إن<PERSON>اء جداول القيود اليومية
-- Create Journal Entry Tables

USE AwqafManagement;
GO

-- إنشاء جدول مراكز التكلفة
-- Create Cost Centers Table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'CostCenters')
BEGIN
    CREATE TABLE CostCenters (
        CostCenterId INT IDENTITY(1,1) PRIMARY KEY,
        CostCenterCode NVARCHAR(20) NOT NULL UNIQUE,
        CostCenterNameAr NVARCHAR(100) NOT NULL,
        CostCenterNameEn NVARCHAR(100) NULL,
        Description NVARCHAR(500) NULL,
        IsActive BIT NOT NULL DEFAULT 1,
        CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
        CreatedBy NVARCHAR(50) NULL,
        ModifiedDate DATETIME2 NULL,
        ModifiedBy NVARCHAR(50) NULL
    );

    -- إنشاء فهارس
    CREATE INDEX IX_CostCenters_Code ON CostCenters(CostCenterCode);
    CREATE INDEX IX_CostCenters_IsActive ON CostCenters(IsActive);

    PRINT 'تم إنشاء جدول مراكز التكلفة بنجاح';
END
ELSE
BEGIN
    PRINT 'جدول مراكز التكلفة موجود مسبقاً';
END
GO

-- إنشاء جدول القيود اليومية الرئيسي
-- Create Journal Entries Main Table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'JournalEntries')
BEGIN
    CREATE TABLE JournalEntries (
        JournalEntryId INT IDENTITY(1,1) PRIMARY KEY,
        JournalNumber NVARCHAR(50) NOT NULL UNIQUE,
        JournalDate DATE NOT NULL,
        JournalType INT NOT NULL, -- 0=General, 1=Recurring, 2=Reversing, 3=Closing, 4=Adjusting
        Status INT NOT NULL DEFAULT 0, -- 0=Draft, 1=Posted, 2=Cancelled, 3=Reversed
        GeneralDescription NVARCHAR(500) NOT NULL,
        TotalDebit DECIMAL(18,2) NOT NULL DEFAULT 0,
        TotalCredit DECIMAL(18,2) NOT NULL DEFAULT 0,
        CostCenterId INT NULL,
        CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
        CreatedBy NVARCHAR(50) NULL,
        ModifiedDate DATETIME2 NULL,
        ModifiedBy NVARCHAR(50) NULL,
        PostedDate DATETIME2 NULL,
        PostedBy NVARCHAR(50) NULL,
        
        -- قيود التحقق
        CONSTRAINT CK_JournalEntries_JournalType CHECK (JournalType BETWEEN 0 AND 4),
        CONSTRAINT CK_JournalEntries_Status CHECK (Status BETWEEN 0 AND 3),
        CONSTRAINT CK_JournalEntries_Amounts CHECK (TotalDebit >= 0 AND TotalCredit >= 0),
        CONSTRAINT CK_JournalEntries_Balance CHECK (TotalDebit = TotalCredit OR Status = 0), -- يجب أن يكون متوازناً إلا إذا كان مسودة
        
        -- المفاتيح الخارجية
        CONSTRAINT FK_JournalEntries_CostCenter FOREIGN KEY (CostCenterId) 
            REFERENCES CostCenters(CostCenterId)
    );

    -- إنشاء فهارس
    CREATE INDEX IX_JournalEntries_Number ON JournalEntries(JournalNumber);
    CREATE INDEX IX_JournalEntries_Date ON JournalEntries(JournalDate);
    CREATE INDEX IX_JournalEntries_Type ON JournalEntries(JournalType);
    CREATE INDEX IX_JournalEntries_Status ON JournalEntries(Status);
    CREATE INDEX IX_JournalEntries_CostCenter ON JournalEntries(CostCenterId);
    CREATE INDEX IX_JournalEntries_CreatedDate ON JournalEntries(CreatedDate);

    PRINT 'تم إنشاء جدول القيود اليومية الرئيسي بنجاح';
END
ELSE
BEGIN
    PRINT 'جدول القيود اليومية الرئيسي موجود مسبقاً';
END
GO

-- إنشاء جدول تفاصيل القيود اليومية
-- Create Journal Entry Details Table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'JournalEntryDetails')
BEGIN
    CREATE TABLE JournalEntryDetails (
        JournalEntryDetailId INT IDENTITY(1,1) PRIMARY KEY,
        JournalEntryId INT NOT NULL,
        AccountId INT NOT NULL,
        LineNumber INT NOT NULL,
        DebitAmount DECIMAL(18,2) NOT NULL DEFAULT 0,
        CreditAmount DECIMAL(18,2) NOT NULL DEFAULT 0,
        Reference NVARCHAR(100) NULL,
        Description NVARCHAR(500) NULL,
        
        -- قيود التحقق
        CONSTRAINT CK_JournalEntryDetails_Amounts CHECK (DebitAmount >= 0 AND CreditAmount >= 0),
        CONSTRAINT CK_JournalEntryDetails_NotBoth CHECK (NOT (DebitAmount > 0 AND CreditAmount > 0)), -- لا يمكن أن يكون مدين ودائن معاً
        CONSTRAINT CK_JournalEntryDetails_HasAmount CHECK (DebitAmount > 0 OR CreditAmount > 0), -- يجب أن يكون هناك مبلغ
        CONSTRAINT CK_JournalEntryDetails_LineNumber CHECK (LineNumber > 0),
        
        -- المفاتيح الخارجية
        CONSTRAINT FK_JournalEntryDetails_JournalEntry FOREIGN KEY (JournalEntryId) 
            REFERENCES JournalEntries(JournalEntryId) ON DELETE CASCADE,
        CONSTRAINT FK_JournalEntryDetails_Account FOREIGN KEY (AccountId) 
            REFERENCES ChartOfAccounts(AccountId),
            
        -- قيد الفرادة
        CONSTRAINT UQ_JournalEntryDetails_Line UNIQUE (JournalEntryId, LineNumber)
    );

    -- إنشاء فهارس
    CREATE INDEX IX_JournalEntryDetails_JournalEntry ON JournalEntryDetails(JournalEntryId);
    CREATE INDEX IX_JournalEntryDetails_Account ON JournalEntryDetails(AccountId);
    CREATE INDEX IX_JournalEntryDetails_LineNumber ON JournalEntryDetails(JournalEntryId, LineNumber);
    CREATE INDEX IX_JournalEntryDetails_Amounts ON JournalEntryDetails(DebitAmount, CreditAmount);

    PRINT 'تم إنشاء جدول تفاصيل القيود اليومية بنجاح';
END
ELSE
BEGIN
    PRINT 'جدول تفاصيل القيود اليومية موجود مسبقاً';
END
GO

-- إدراج بيانات مراكز التكلفة الافتراضية
-- Insert Default Cost Centers
IF NOT EXISTS (SELECT * FROM CostCenters)
BEGIN
    INSERT INTO CostCenters (CostCenterCode, CostCenterNameAr, Description, IsActive)
    VALUES
        ('CC001', N'الإدارة العامة', N'مركز تكلفة الإدارة العامة', 1),
        ('CC002', N'المحاسبة والمالية', N'مركز تكلفة المحاسبة والمالية', 1),
        ('CC003', N'الموارد البشرية', N'مركز تكلفة الموارد البشرية', 1),
        ('CC004', N'تقنية المعلومات', N'مركز تكلفة تقنية المعلومات', 1),
        ('CC005', N'الصيانة والخدمات', N'مركز تكلفة الصيانة والخدمات', 1),
        ('CC006', N'التسويق والمبيعات', N'مركز تكلفة التسويق والمبيعات', 1),
        ('CC007', N'المشاريع الخاصة', N'مركز تكلفة المشاريع الخاصة', 1),
        ('CC008', N'البحث والتطوير', N'مركز تكلفة البحث والتطوير', 1);

    PRINT 'تم إدراج مراكز التكلفة الافتراضية بنجاح';
END
ELSE
BEGIN
    PRINT 'مراكز التكلفة موجودة مسبقاً';
END
GO

-- إنشاء إجراءات مخزنة للقيود اليومية
-- Create Stored Procedures for Journal Entries

-- إجراء للتحقق من توازن القيد
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_ValidateJournalEntryBalance')
    DROP PROCEDURE sp_ValidateJournalEntryBalance;
GO

CREATE PROCEDURE sp_ValidateJournalEntryBalance
    @JournalEntryId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @TotalDebit DECIMAL(18,2);
    DECLARE @TotalCredit DECIMAL(18,2);
    DECLARE @IsBalanced BIT = 0;
    
    SELECT 
        @TotalDebit = SUM(DebitAmount),
        @TotalCredit = SUM(CreditAmount)
    FROM JournalEntryDetails
    WHERE JournalEntryId = @JournalEntryId;
    
    IF @TotalDebit = @TotalCredit
        SET @IsBalanced = 1;
    
    -- تحديث إجماليات القيد
    UPDATE JournalEntries
    SET TotalDebit = ISNULL(@TotalDebit, 0),
        TotalCredit = ISNULL(@TotalCredit, 0)
    WHERE JournalEntryId = @JournalEntryId;
    
    SELECT @IsBalanced AS IsBalanced, @TotalDebit AS TotalDebit, @TotalCredit AS TotalCredit;
END
GO

-- إجراء لترحيل القيد
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_PostJournalEntry')
    DROP PROCEDURE sp_PostJournalEntry;
GO

CREATE PROCEDURE sp_PostJournalEntry
    @JournalEntryId INT,
    @PostedBy NVARCHAR(50)
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRANSACTION;
    
    BEGIN TRY
        -- التحقق من توازن القيد
        DECLARE @IsBalanced BIT;
        DECLARE @TotalDebit DECIMAL(18,2);
        DECLARE @TotalCredit DECIMAL(18,2);
        
        EXEC sp_ValidateJournalEntryBalance @JournalEntryId;
        
        SELECT @IsBalanced = CASE WHEN TotalDebit = TotalCredit THEN 1 ELSE 0 END
        FROM JournalEntries
        WHERE JournalEntryId = @JournalEntryId;
        
        IF @IsBalanced = 0
        BEGIN
            RAISERROR(N'لا يمكن ترحيل قيد غير متوازن', 16, 1);
            RETURN;
        END
        
        -- تحديث حالة القيد
        UPDATE JournalEntries
        SET Status = 1, -- Posted
            PostedDate = GETDATE(),
            PostedBy = @PostedBy
        WHERE JournalEntryId = @JournalEntryId AND Status = 0; -- Draft only
        
        IF @@ROWCOUNT = 0
        BEGIN
            RAISERROR(N'لا يمكن ترحيل القيد. قد يكون مرحلاً مسبقاً أو غير موجود', 16, 1);
            RETURN;
        END
        
        COMMIT TRANSACTION;
        SELECT 1 AS Success, N'تم ترحيل القيد بنجاح' AS Message;
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END
GO

-- إنشاء مشاهد للتقارير
-- Create Views for Reporting

-- مشهد لعرض القيود مع التفاصيل
IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_JournalEntriesWithDetails')
    DROP VIEW vw_JournalEntriesWithDetails;
GO

CREATE VIEW vw_JournalEntriesWithDetails
AS
SELECT 
    je.JournalEntryId,
    je.JournalNumber,
    je.JournalDate,
    je.JournalType,
    CASE je.JournalType
        WHEN 0 THEN N'عام'
        WHEN 1 THEN N'متكرر'
        WHEN 2 THEN N'عكسي'
        WHEN 3 THEN N'إقفال'
        WHEN 4 THEN N'تسوية'
        ELSE N'غير محدد'
    END AS JournalTypeName,
    je.Status,
    CASE je.Status
        WHEN 0 THEN N'مسودة'
        WHEN 1 THEN N'مرحل'
        WHEN 2 THEN N'ملغي'
        WHEN 3 THEN N'معكوس'
        ELSE N'غير محدد'
    END AS StatusName,
    je.GeneralDescription,
    je.TotalDebit,
    je.TotalCredit,
    cc.CostCenterNameAr AS CostCenterName,
    je.CreatedDate,
    je.CreatedBy,
    je.PostedDate,
    je.PostedBy,
    jed.JournalEntryDetailId,
    jed.LineNumber,
    coa.AccountCode,
    coa.AccountNameAr AS AccountName,
    jed.DebitAmount,
    jed.CreditAmount,
    jed.Reference,
    jed.Description AS LineDescription
FROM JournalEntries je
LEFT JOIN CostCenters cc ON je.CostCenterId = cc.CostCenterId
LEFT JOIN JournalEntryDetails jed ON je.JournalEntryId = jed.JournalEntryId
LEFT JOIN ChartOfAccounts coa ON jed.AccountId = coa.AccountId;
GO

PRINT 'تم إنشاء جداول وإجراءات القيود اليومية بنجاح';
PRINT 'Journal Entry tables and procedures created successfully';
