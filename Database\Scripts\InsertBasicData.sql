-- ===================================================================
-- إدراج البيانات الأساسية
-- Insert Basic Data
-- ===================================================================

USE AwqafManagement;
GO

PRINT N'=== إدراج البيانات الأساسية ===';

-- إدراج مجموعات الحسابات
INSERT INTO AccountGroups (GroupCode, GroupName, GroupNameAr, AccountTypeId, IsActive, CreatedDate) VALUES
('CUR_ASSETS', 'Current Assets', N'الأصول المتداولة', 1003, 1, GETDATE()),
('FIX_ASSETS', 'Fixed Assets', N'الأصول الثابتة', 1003, 1, GETDATE()),
('CUR_LIAB', 'Current Liabilities', N'الخصوم المتداولة', 1004, 1, GETDATE()),
('EQUITY', 'Equity', N'حقوق الملكية', 1005, 1, GETDATE()),
('REVENUE', 'Revenue', N'الإيرادات', 1006, 1, GETDATE()),
('EXPENSES', 'Expenses', N'المصروفات', 1007, 1, GETDATE());

PRINT N'✓ تم إدراج مجموعات الحسابات';

-- التحقق من النتائج
SELECT AccountGroupId, GroupName, GroupNameAr FROM AccountGroups;

PRINT N'=== انتهى إدراج البيانات الأساسية ===';
