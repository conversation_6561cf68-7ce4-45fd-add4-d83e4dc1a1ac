-- إصلاح بسيط لحقول المستخدم
-- Simple fix for user fields
USE AwqafManagement
GO

-- التحقق من وجود الجداول والحقول
-- Check if tables and fields exist
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'JournalEntries')
BEGIN
    PRINT 'جدول JournalEntries موجود'
    
    -- التحقق من نوع حقل CreatedBy
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[JournalEntries]') AND name = 'CreatedBy' AND system_type_id = 56)
    BEGIN
        PRINT 'حقل CreatedBy من نوع int - يحتاج تحديث'
        
        -- حذف الجدول وإعادة إنشاؤه
        IF EXISTS (SELECT * FROM sys.tables WHERE name = 'JournalEntryDetails')
            DROP TABLE JournalEntryDetails
        IF EXISTS (SELECT * FROM sys.tables WHERE name = 'JournalEntries')
            DROP TABLE JournalEntries
        
        PRINT 'تم حذف الجداول القديمة'
    END
    ELSE
    BEGIN
        PRINT 'حقل CreatedBy من النوع الصحيح'
    END
END

-- إعادة إنشاء الجداول بالتعريف الصحيح
-- Recreate tables with correct definition
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'JournalEntries')
BEGIN
    PRINT 'إنشاء جدول JournalEntries جديد...'
    
    CREATE TABLE [dbo].[JournalEntries](
        [JournalEntryId] [int] IDENTITY(1,1) NOT NULL,
        [JournalNumber] [nvarchar](50) NOT NULL,
        [JournalDate] [datetime] NOT NULL,
        [JournalType] [int] NOT NULL DEFAULT(1),
        [Status] [int] NOT NULL DEFAULT(1),
        [Description] [nvarchar](500) NULL,
        [Reference] [nvarchar](100) NULL,
        [TotalDebit] [decimal](18, 2) NOT NULL DEFAULT(0),
        [TotalCredit] [decimal](18, 2) NOT NULL DEFAULT(0),
        [CreatedBy] [nvarchar](50) NOT NULL,
        [CreatedDate] [datetime] NOT NULL DEFAULT(GETDATE()),
        [ModifiedBy] [nvarchar](50) NULL,
        [ModifiedDate] [datetime] NULL,
        [ApprovedBy] [nvarchar](50) NULL,
        [ApprovedDate] [datetime] NULL,
        [PostedBy] [nvarchar](50) NULL,
        [PostedDate] [datetime] NULL,
        [Notes] [nvarchar](1000) NULL,
        CONSTRAINT [PK_JournalEntries] PRIMARY KEY CLUSTERED ([JournalEntryId] ASC),
        CONSTRAINT [UK_JournalEntries_JournalNumber] UNIQUE NONCLUSTERED ([JournalNumber] ASC)
    )
    
    PRINT 'تم إنشاء جدول JournalEntries'
END

IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'JournalEntryDetails')
BEGIN
    PRINT 'إنشاء جدول JournalEntryDetails جديد...'
    
    CREATE TABLE [dbo].[JournalEntryDetails](
        [JournalEntryDetailId] [int] IDENTITY(1,1) NOT NULL,
        [JournalEntryId] [int] NOT NULL,
        [AccountId] [int] NOT NULL,
        [LineNumber] [int] NOT NULL,
        [DebitAmount] [decimal](18, 2) NOT NULL DEFAULT(0),
        [CreditAmount] [decimal](18, 2) NOT NULL DEFAULT(0),
        [Description] [nvarchar](500) NULL,
        [Reference] [nvarchar](100) NULL,
        [CostCenterId] [int] NULL,
        [CreatedDate] [datetime] NOT NULL DEFAULT(GETDATE()),
        [CreatedBy] [nvarchar](50) NOT NULL,
        CONSTRAINT [PK_JournalEntryDetails] PRIMARY KEY CLUSTERED ([JournalEntryDetailId] ASC),
        CONSTRAINT [FK_JournalEntryDetails_JournalEntries] FOREIGN KEY([JournalEntryId]) 
            REFERENCES [dbo].[JournalEntries] ([JournalEntryId]) ON DELETE CASCADE,
        CONSTRAINT [FK_JournalEntryDetails_ChartOfAccounts] FOREIGN KEY([AccountId]) 
            REFERENCES [dbo].[ChartOfAccounts] ([AccountId]),
        CONSTRAINT [FK_JournalEntryDetails_CostCenters] FOREIGN KEY([CostCenterId]) 
            REFERENCES [dbo].[CostCenters] ([CostCenterId])
    )
    
    PRINT 'تم إنشاء جدول JournalEntryDetails'
END

-- إضافة بيانات تجريبية
IF NOT EXISTS (SELECT * FROM JournalEntries WHERE JournalNumber = '1')
BEGIN
    PRINT 'إضافة بيانات تجريبية...'
    
    INSERT INTO JournalEntries (JournalNumber, JournalDate, JournalType, Status, Description, Reference, TotalDebit, TotalCredit, CreatedBy, CreatedDate, Notes)
    VALUES ('1', GETDATE(), 1, 1, N'قيد افتتاحي للأرصدة', N'REF-001', 50000.00, 50000.00, '1', GETDATE(), N'قيد افتتاحي لبداية السنة المالية')

    DECLARE @JournalEntryId INT = SCOPE_IDENTITY()

    INSERT INTO JournalEntryDetails (JournalEntryId, AccountId, LineNumber, DebitAmount, CreditAmount, Description, Reference, CreatedDate, CreatedBy)
    VALUES 
    (@JournalEntryId, 1, 1, 30000.00, 0.00, N'رصيد النقدية في الصندوق', N'REF-001-1', GETDATE(), '1'),
    (@JournalEntryId, 2, 2, 20000.00, 0.00, N'رصيد البنك', N'REF-001-2', GETDATE(), '1'),
    (@JournalEntryId, 3, 3, 0.00, 50000.00, N'رأس المال', N'REF-001-3', GETDATE(), '1')
    
    PRINT 'تم إضافة البيانات التجريبية'
END

PRINT 'تم الانتهاء من إصلاح حقول المستخدم!'
GO
