@echo off
echo ========================================
echo اختبار شاشة القيود اليومية
echo Testing Journal Entries Screen
echo ========================================
echo.

echo 1. اختبار قاعدة البيانات...
echo    Testing database...

sqlcmd -S NAJEEB -d AwqafManagement -E -Q "SELECT COUNT(*) as AccountCount FROM ChartOfAccounts WHERE IsActive = 1"
if %ERRORLEVEL% NEQ 0 (
    echo خطأ: لا يمكن الاتصال بقاعدة البيانات
    echo Error: Cannot connect to database
    pause
    exit /b 1
)

echo.
echo 2. اختبار مراكز التكلفة...
echo    Testing cost centers...

sqlcmd -S NAJEEB -d AwqafManagement -E -Q "SELECT COUNT(*) as CostCenterCount FROM CostCenters WHERE IsActive = 1"
if %ERRORLEVEL% NEQ 0 (
    echo خطأ: مشكلة في مراكز التكلفة
    echo Error: Problem with cost centers
    pause
    exit /b 1
)

echo.
echo 3. اختبار جداول القيود...
echo    Testing journal tables...

sqlcmd -S NAJEEB -d AwqafManagement -E -Q "SELECT COUNT(*) as TableCount FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME IN ('JournalEntries', 'JournalEntryDetails')"
if %ERRORLEVEL% NEQ 0 (
    echo خطأ: جداول القيود غير موجودة
    echo Error: Journal tables not found
    pause
    exit /b 1
)

echo.
echo ✅ جميع الاختبارات نجحت!
echo ✅ All tests passed!
echo.
echo تشغيل التطبيق...
echo Starting application...

start "" ".\bin\Debug\Awqaf_Managment.exe"

echo.
echo 📋 خطوات الاختبار:
echo 📋 Test Steps:
echo.
echo 1. سجل الدخول بـ admin / admin123
echo    Login with admin / admin123
echo.
echo 2. اضغط زر "المحاسبة"
echo    Click "Accounting" button
echo.
echo 3. اختر "القيود اليومية"
echo    Select "Journal Entries"
echo.
echo 4. اختبر الوظائف التالية:
echo    Test the following functions:
echo    - إنشاء قيد جديد (New)
echo    - البحث عن حساب (F2)
echo    - إدخال البيانات
echo    - حفظ القيد (Save)
echo.
echo ========================================

pause
