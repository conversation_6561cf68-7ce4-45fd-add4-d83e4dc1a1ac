-- إدراج الحسابات الرئيسية والفرعية بالأعمدة الصحيحة
-- Insert Main and Sub Accounts with Correct Columns

USE AwqafManagement;
GO

-- حذف البيانات الموجودة وإعادة تعيين الهوية
DELETE FROM ChartOfAccounts;
DBCC CHECKIDENT ('ChartOfAccounts', RESEED, 0);
GO

-- إدراج الحسابات الرئيسية والفرعية
SET IDENTITY_INSERT ChartOfAccounts ON;

-- ===== 1. الأصول (Assets) =====
INSERT INTO ChartOfAccounts (AccountId, AccountCode, AccountName, AccountNameAr, AccountTypeId, AccountGroupId, ParentAccountId, AccountLevel, IsParent, IsActive, AllowPosting, Description, CurrencyCode, OpeningBalance, CurrentBalance, CreatedDate, CreatedBy)
VALUES 
-- 1.0 الأصول - الحساب الرئيسي
(1, N'1.00.000', N'Assets', N'الأصول', 1, 1, NULL, 1, 1, 1, 0, N'مجموعة الأصول الرئيسية', N'SAR', 0.00, 0.00, GETDATE(), 1),

-- 1.1 الأصول المتداولة
(2, N'1.01.000', N'Current Assets', N'الأصول المتداولة', 1, 1, 1, 2, 1, 1, 0, N'الأصول قصيرة الأجل', N'SAR', 0.00, 0.00, GETDATE(), 1),

-- 1.1.1 النقدية والبنوك
(3, N'1.01.001', N'Main Cash Box', N'الصندوق الرئيسي', 1, 1, 2, 3, 0, 1, 1, N'صندوق النقدية الرئيسي', N'SAR', 50000.00, 50000.00, GETDATE(), 1),
(4, N'1.01.002', N'Sub Cash Box', N'صندوق فرعي', 1, 1, 2, 3, 0, 1, 1, N'صندوق نقدية فرعي', N'SAR', 15000.00, 15000.00, GETDATE(), 1),
(5, N'1.01.003', N'National Commercial Bank', N'البنك الأهلي التجاري', 1, 1, 2, 3, 0, 1, 1, N'حساب البنك الأهلي التجاري', N'SAR', 250000.00, 250000.00, GETDATE(), 1),
(6, N'1.01.004', N'Al Rajhi Bank', N'بنك الراجحي', 1, 1, 2, 3, 0, 1, 1, N'حساب بنك الراجحي', N'SAR', 180000.00, 180000.00, GETDATE(), 1),
(7, N'1.01.005', N'Riyad Bank', N'بنك الرياض', 1, 1, 2, 3, 0, 1, 1, N'حساب بنك الرياض', N'SAR', 120000.00, 120000.00, GETDATE(), 1),
(8, N'1.01.006', N'Samba Bank', N'بنك سامبا', 1, 1, 2, 3, 0, 1, 1, N'حساب بنك سامبا', N'SAR', 95000.00, 95000.00, GETDATE(), 1),

-- 1.1.2 العملاء والذمم المدينة
(9, N'1.01.010', N'Accounts Receivable', N'العملاء والذمم المدينة', 1, 1, 2, 3, 1, 1, 0, N'مجموعة العملاء والذمم المدينة', N'SAR', 0.00, 0.00, GETDATE(), 1),
(10, N'1.01.011', N'Client - Real Estate Endowments Co.', N'عميل - شركة الأوقاف العقارية', 1, 1, 9, 3, 0, 1, 1, N'شركة الأوقاف العقارية', N'SAR', 75000.00, 75000.00, GETDATE(), 1),
(11, N'1.01.012', N'Client - Islamic Charity Foundation', N'عميل - مؤسسة الخير الإسلامية', 1, 1, 9, 3, 0, 1, 1, N'مؤسسة الخير الإسلامية', N'SAR', 45000.00, 45000.00, GETDATE(), 1),
(12, N'1.01.013', N'Client - Charity Association', N'عميل - جمعية البر الخيرية', 1, 1, 9, 3, 0, 1, 1, N'جمعية البر الخيرية', N'SAR', 32000.00, 32000.00, GETDATE(), 1),

-- 1.1.3 المخزون
(13, N'1.01.020', N'Inventory', N'المخزون', 1, 1, 2, 3, 1, 1, 0, N'مجموعة المخزون', N'SAR', 0.00, 0.00, GETDATE(), 1),
(14, N'1.01.021', N'Food Inventory', N'مخزون المواد الغذائية', 1, 1, 13, 3, 0, 1, 1, N'مخزون المواد الغذائية', N'SAR', 25000.00, 25000.00, GETDATE(), 1),
(15, N'1.01.022', N'Cleaning Supplies Inventory', N'مخزون المواد التنظيفية', 1, 1, 13, 3, 0, 1, 1, N'مخزون المواد التنظيفية', N'SAR', 8000.00, 8000.00, GETDATE(), 1),

-- 1.2 الأصول الثابتة
(16, N'1.02.000', N'Fixed Assets', N'الأصول الثابتة', 1, 1, 1, 2, 1, 1, 0, N'الأصول طويلة الأجل', N'SAR', 0.00, 0.00, GETDATE(), 1),

-- 1.2.1 العقارات والأراضي
(17, N'1.02.001', N'Land and Real Estate', N'الأراضي والعقارات', 1, 1, 16, 3, 0, 1, 1, N'الأراضي والعقارات الوقفية', N'SAR', 2500000.00, 2500000.00, GETDATE(), 1),
(18, N'1.02.002', N'Buildings and Constructions', N'المباني والإنشاءات', 1, 1, 16, 3, 0, 1, 1, N'المباني والإنشاءات الوقفية', N'SAR', 1800000.00, 1800000.00, GETDATE(), 1),
(19, N'1.02.003', N'Residential Buildings', N'المباني السكنية', 1, 1, 16, 3, 0, 1, 1, N'المباني السكنية الوقفية', N'SAR', 1200000.00, 1200000.00, GETDATE(), 1),
(20, N'1.02.004', N'Commercial Buildings', N'المباني التجارية', 1, 1, 16, 3, 0, 1, 1, N'المباني التجارية الوقفية', N'SAR', 800000.00, 800000.00, GETDATE(), 1),

-- 1.2.2 المعدات والأثاث
(21, N'1.02.010', N'Furniture and Equipment', N'الأثاث والمعدات', 1, 1, 16, 3, 0, 1, 1, N'الأثاث والمعدات المكتبية', N'SAR', 150000.00, 150000.00, GETDATE(), 1),
(22, N'1.02.011', N'Computer Equipment', N'أجهزة الحاسوب', 1, 1, 16, 3, 0, 1, 1, N'أجهزة الحاسوب والتقنية', N'SAR', 85000.00, 85000.00, GETDATE(), 1),
(23, N'1.02.012', N'Vehicles', N'السيارات والمركبات', 1, 1, 16, 3, 0, 1, 1, N'السيارات والمركبات', N'SAR', 120000.00, 120000.00, GETDATE(), 1),

-- ===== 2. الخصوم (Liabilities) =====
(24, N'2.00.000', N'Liabilities', N'الخصوم', 2, 2, NULL, 1, 1, 1, 0, N'مجموعة الخصوم الرئيسية', N'SAR', 0.00, 0.00, GETDATE(), 1),

-- 2.1 الخصوم المتداولة
(25, N'2.01.000', N'Current Liabilities', N'الخصوم المتداولة', 2, 2, 24, 2, 1, 1, 0, N'الخصوم قصيرة الأجل', N'SAR', 0.00, 0.00, GETDATE(), 1),

-- 2.1.1 الموردون والذمم الدائنة
(26, N'2.01.001', N'Accounts Payable', N'الموردون والذمم الدائنة', 2, 2, 25, 3, 0, 1, 1, N'الموردون والذمم الدائنة', N'SAR', 25000.00, 25000.00, GETDATE(), 1),
(27, N'2.01.002', N'Supplier - Advanced Construction Co.', N'مورد - شركة الإنشاءات المتقدمة', 2, 2, 25, 3, 0, 1, 1, N'شركة الإنشاءات المتقدمة', N'SAR', 15000.00, 15000.00, GETDATE(), 1),
(28, N'2.01.003', N'Supplier - Technical Services Co.', N'مورد - شركة الخدمات التقنية', 2, 2, 25, 3, 0, 1, 1, N'شركة الخدمات التقنية', N'SAR', 8000.00, 8000.00, GETDATE(), 1),

-- 2.1.2 المصروفات المستحقة
(29, N'2.01.010', N'Accrued Expenses', N'المصروفات المستحقة', 2, 2, 25, 3, 0, 1, 1, N'المصروفات المستحقة الدفع', N'SAR', 15000.00, 15000.00, GETDATE(), 1),
(30, N'2.01.011', N'Accrued Salaries', N'رواتب مستحقة', 2, 2, 25, 3, 0, 1, 1, N'رواتب الموظفين المستحقة', N'SAR', 12000.00, 12000.00, GETDATE(), 1);

-- ===== 3. حقوق الملكية (Equity) =====
(31, N'3.00.000', N'Equity', N'حقوق الملكية', 3, 3, NULL, 1, 1, 1, 0, N'مجموعة حقوق الملكية', N'SAR', 0.00, 0.00, GETDATE(), 1),

-- 3.1 رأس المال
(32, N'3.01.000', N'Capital', N'رأس المال', 3, 3, 31, 2, 1, 1, 0, N'مجموعة رأس المال', N'SAR', 0.00, 0.00, GETDATE(), 1),
(33, N'3.01.001', N'Endowment Capital', N'رأس المال الوقفي', 3, 3, 32, 3, 0, 1, 1, N'رأس المال الوقفي الأساسي', N'SAR', 5000000.00, 5000000.00, GETDATE(), 1),
(34, N'3.01.002', N'Retained Earnings', N'الأرباح المحتجزة', 3, 3, 32, 3, 0, 1, 1, N'الأرباح المحتجزة من السنوات السابقة', N'SAR', 500000.00, 500000.00, GETDATE(), 1),
(35, N'3.01.003', N'General Reserve', N'احتياطي عام', 3, 3, 32, 3, 0, 1, 1, N'الاحتياطي العام', N'SAR', 200000.00, 200000.00, GETDATE(), 1),

-- ===== 4. الإيرادات (Revenues) =====
(36, N'4.00.000', N'Revenues', N'الإيرادات', 4, 4, NULL, 1, 1, 1, 0, N'مجموعة الإيرادات الرئيسية', N'SAR', 0.00, 0.00, GETDATE(), 1),

-- 4.1 إيرادات التشغيل
(37, N'4.01.000', N'Operating Revenues', N'إيرادات التشغيل', 4, 4, 36, 2, 1, 1, 0, N'إيرادات التشغيل الأساسية', N'SAR', 0.00, 0.00, GETDATE(), 1),

-- 4.1.1 إيرادات الإيجارات
(38, N'4.01.001', N'Residential Rental Income', N'إيرادات الإيجارات السكنية', 4, 4, 37, 3, 0, 1, 1, N'إيرادات إيجار العقارات السكنية', N'SAR', 0.00, 0.00, GETDATE(), 1),
(39, N'4.01.002', N'Commercial Rental Income', N'إيرادات الإيجارات التجارية', 4, 4, 37, 3, 0, 1, 1, N'إيرادات إيجار العقارات التجارية', N'SAR', 0.00, 0.00, GETDATE(), 1),
(40, N'4.01.003', N'Administrative Rental Income', N'إيرادات الإيجارات الإدارية', 4, 4, 37, 3, 0, 1, 1, N'إيرادات إيجار المكاتب الإدارية', N'SAR', 0.00, 0.00, GETDATE(), 1),

-- 4.1.2 إيرادات الاستثمارات
(41, N'4.01.010', N'Investment Income', N'إيرادات الاستثمارات', 4, 4, 37, 3, 0, 1, 1, N'إيرادات الاستثمارات المالية', N'SAR', 0.00, 0.00, GETDATE(), 1),
(42, N'4.01.011', N'Dividend Income', N'أرباح الأسهم', 4, 4, 37, 3, 0, 1, 1, N'أرباح الأسهم والاستثمارات', N'SAR', 0.00, 0.00, GETDATE(), 1),
(43, N'4.01.012', N'Bank Deposit Interest', N'فوائد الودائع البنكية', 4, 4, 37, 3, 0, 1, 1, N'فوائد الودائع البنكية', N'SAR', 0.00, 0.00, GETDATE(), 1),

-- 4.2 إيرادات أخرى
(44, N'4.02.000', N'Other Revenues', N'إيرادات أخرى', 4, 4, 36, 2, 1, 1, 0, N'الإيرادات الأخرى', N'SAR', 0.00, 0.00, GETDATE(), 1),
(45, N'4.02.001', N'Donations and Gifts', N'التبرعات والهبات', 4, 4, 44, 3, 0, 1, 1, N'التبرعات والهبات الواردة', N'SAR', 0.00, 0.00, GETDATE(), 1),
(46, N'4.02.002', N'Miscellaneous Income', N'إيرادات متنوعة', 4, 4, 44, 3, 0, 1, 1, N'إيرادات متنوعة أخرى', N'SAR', 0.00, 0.00, GETDATE(), 1),

-- ===== 5. المصروفات (Expenses) =====
(47, N'5.00.000', N'Expenses', N'المصروفات', 5, 5, NULL, 1, 1, 1, 0, N'مجموعة المصروفات الرئيسية', N'SAR', 0.00, 0.00, GETDATE(), 1),

-- 5.1 مصروفات التشغيل
(48, N'5.01.000', N'Operating Expenses', N'مصروفات التشغيل', 5, 5, 47, 2, 1, 1, 0, N'مصروفات التشغيل الأساسية', N'SAR', 0.00, 0.00, GETDATE(), 1),

-- 5.1.1 الرواتب والأجور
(49, N'5.01.001', N'Salaries and Wages', N'الرواتب والأجور', 5, 5, 48, 3, 0, 1, 1, N'رواتب وأجور الموظفين', N'SAR', 0.00, 0.00, GETDATE(), 1),
(50, N'5.01.002', N'Employee Allowances', N'بدلات الموظفين', 5, 5, 48, 3, 0, 1, 1, N'بدلات ومكافآت الموظفين', N'SAR', 0.00, 0.00, GETDATE(), 1),
(51, N'5.01.003', N'Social Insurance', N'التأمينات الاجتماعية', 5, 5, 48, 3, 0, 1, 1, N'التأمينات الاجتماعية للموظفين', N'SAR', 0.00, 0.00, GETDATE(), 1),

-- 5.1.2 مصروفات الصيانة
(52, N'5.01.010', N'Maintenance Expenses', N'مصروفات الصيانة', 5, 5, 48, 3, 0, 1, 1, N'مصروفات صيانة العقارات', N'SAR', 0.00, 0.00, GETDATE(), 1),
(53, N'5.01.011', N'Building Maintenance', N'صيانة المباني', 5, 5, 48, 3, 0, 1, 1, N'صيانة المباني والإنشاءات', N'SAR', 0.00, 0.00, GETDATE(), 1),
(54, N'5.01.012', N'Equipment Maintenance', N'صيانة المعدات', 5, 5, 48, 3, 0, 1, 1, N'صيانة المعدات والأجهزة', N'SAR', 0.00, 0.00, GETDATE(), 1),

-- 5.1.3 المصروفات الإدارية
(55, N'5.01.020', N'Administrative Expenses', N'المصروفات الإدارية', 5, 5, 48, 3, 0, 1, 1, N'المصروفات الإدارية العامة', N'SAR', 0.00, 0.00, GETDATE(), 1),
(56, N'5.01.021', N'Utilities Expenses', N'مصروفات الكهرباء والماء', 5, 5, 48, 3, 0, 1, 1, N'مصروفات الكهرباء والماء والهاتف', N'SAR', 0.00, 0.00, GETDATE(), 1),
(57, N'5.01.022', N'Insurance Expenses', N'مصروفات التأمين', 5, 5, 48, 3, 0, 1, 1, N'مصروفات التأمين على العقارات', N'SAR', 0.00, 0.00, GETDATE(), 1),
(58, N'5.01.023', N'Stationery Expenses', N'مصروفات القرطاسية', 5, 5, 48, 3, 0, 1, 1, N'مصروفات القرطاسية والمكتبية', N'SAR', 0.00, 0.00, GETDATE(), 1),
(59, N'5.01.024', N'Communication Expenses', N'مصروفات الاتصالات', 5, 5, 48, 3, 0, 1, 1, N'مصروفات الهاتف والإنترنت', N'SAR', 0.00, 0.00, GETDATE(), 1),
(60, N'5.01.025', N'Travel Expenses', N'مصروفات السفر والانتقال', 5, 5, 48, 3, 0, 1, 1, N'مصروفات السفر والانتقال', N'SAR', 0.00, 0.00, GETDATE(), 1);

SET IDENTITY_INSERT ChartOfAccounts OFF;
GO

-- عرض ملخص الحسابات المدرجة
SELECT
    CASE AccountLevel
        WHEN 1 THEN N'رئيسي'
        WHEN 2 THEN N'فرعي'
        WHEN 3 THEN N'تفصيلي'
    END as 'نوع الحساب',
    COUNT(*) as 'عدد الحسابات'
FROM ChartOfAccounts
GROUP BY AccountLevel
ORDER BY AccountLevel;

-- عرض الحسابات الرئيسية
SELECT
    AccountCode as 'رمز الحساب',
    AccountNameAr as 'اسم الحساب',
    CASE AccountTypeId
        WHEN 1 THEN N'أصول'
        WHEN 2 THEN N'خصوم'
        WHEN 3 THEN N'حقوق ملكية'
        WHEN 4 THEN N'إيرادات'
        WHEN 5 THEN N'مصروفات'
    END as 'نوع الحساب'
FROM ChartOfAccounts
WHERE AccountLevel = 1
ORDER BY AccountCode;

PRINT N'تم إدراج 60 حساب محاسبي شامل (رئيسي وفرعي وتفصيلي) بنجاح';
