-- ===================================================================
-- إجراءات مخزنة متقدمة لإدارة الحسابات
-- Advanced Stored Procedures for Account Management
-- ===================================================================

USE AwqafManagement;
GO

-- ===================================================================
-- إجراء إضافة حساب جديد مع التحقق من التكرار
-- Add New Account with Duplicate Check
-- ===================================================================
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_AddAccountAdvanced')
    DROP PROCEDURE sp_AddAccountAdvanced;
GO

CREATE PROCEDURE sp_AddAccountAdvanced
    @AccountCode NVARCHAR(50),
    @AccountNameAr NVARCHAR(200),
    @AccountNameEn NVARCHAR(200) = NULL,
    @AccountTypeId INT,
    @AccountGroupId INT,
    @ParentAccountId INT = NULL,
    @LevelType INT = 3, -- Default to Detail
    @Nature INT = 1, -- Default to Debit
    @OpeningBalance DECIMAL(18,2) = 0,
    @OpeningBalanceType INT = 1,
    @CurrencyId INT = 1,
    @Description NVARCHAR(500) = NULL,
    @AllowPosting BIT = 1,
    @AllowDirectEntry BIT = 1,
    @IsActive BIT = 1,
    @CreatedBy INT,
    @Result INT OUTPUT,
    @Message NVARCHAR(500) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- التحقق من صحة البيانات
        IF @AccountCode IS NULL OR LTRIM(RTRIM(@AccountCode)) = ''
        BEGIN
            SET @Result = -1;
            SET @Message = N'رمز الحساب مطلوب';
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        IF @AccountNameAr IS NULL OR LTRIM(RTRIM(@AccountNameAr)) = ''
        BEGIN
            SET @Result = -2;
            SET @Message = N'اسم الحساب بالعربية مطلوب';
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        -- التحقق من عدم تكرار رمز الحساب
        IF EXISTS (SELECT 1 FROM ChartOfAccounts WHERE AccountCode = @AccountCode)
        BEGIN
            SET @Result = -3;
            SET @Message = N'رمز الحساب موجود مسبقاً';
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        -- التحقق من عدم تكرار اسم الحساب
        IF EXISTS (SELECT 1 FROM ChartOfAccounts WHERE AccountNameAr = @AccountNameAr)
        BEGIN
            SET @Result = -4;
            SET @Message = N'اسم الحساب موجود مسبقاً';
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        -- التحقق من وجود نوع الحساب
        IF NOT EXISTS (SELECT 1 FROM AccountTypes WHERE AccountTypeId = @AccountTypeId AND IsActive = 1)
        BEGIN
            SET @Result = -5;
            SET @Message = N'نوع الحساب غير صحيح أو غير نشط';
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        -- التحقق من وجود مجموعة الحساب
        IF NOT EXISTS (SELECT 1 FROM AccountGroups WHERE AccountGroupId = @AccountGroupId AND IsActive = 1)
        BEGIN
            SET @Result = -6;
            SET @Message = N'مجموعة الحساب غير صحيحة أو غير نشطة';
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        -- التحقق من الحساب الأب إذا كان محدد
        IF @ParentAccountId IS NOT NULL
        BEGIN
            IF NOT EXISTS (SELECT 1 FROM ChartOfAccounts WHERE AccountId = @ParentAccountId AND IsActive = 1)
            BEGIN
                SET @Result = -7;
                SET @Message = N'الحساب الأب غير صحيح أو غير نشط';
                ROLLBACK TRANSACTION;
                RETURN;
            END
            
            -- تحديث الحساب الأب ليصبح حساب أب
            UPDATE ChartOfAccounts 
            SET IsParent = 1, IsParentAccount = 1, AllowPosting = 0
            WHERE AccountId = @ParentAccountId;
        END
        
        -- تحديد مستوى الحساب تلقائياً
        DECLARE @AccountLevel INT = 1;
        IF @ParentAccountId IS NOT NULL
        BEGIN
            SELECT @AccountLevel = ISNULL(AccountLevel, 0) + 1
            FROM ChartOfAccounts 
            WHERE AccountId = @ParentAccountId;
        END
        
        -- إدراج الحساب الجديد
        INSERT INTO ChartOfAccounts (
            AccountCode, AccountNameAr, AccountNameEn, AccountTypeId, AccountGroupId,
            ParentAccountId, LevelType, Nature, OpeningBalance, OpeningBalanceType,
            CurrencyId, Description, AllowPosting, AllowDirectEntry, IsActive,
            AccountLevel, IsParent, IsParentAccount, CreatedDate, CreatedBy
        ) VALUES (
            @AccountCode, @AccountNameAr, @AccountNameEn, @AccountTypeId, @AccountGroupId,
            @ParentAccountId, @LevelType, @Nature, @OpeningBalance, @OpeningBalanceType,
            @CurrencyId, @Description, @AllowPosting, @AllowDirectEntry, @IsActive,
            @AccountLevel, 0, 0, GETDATE(), @CreatedBy
        );
        
        SET @Result = SCOPE_IDENTITY();
        SET @Message = N'تم إضافة الحساب بنجاح';
        
        COMMIT TRANSACTION;
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        
        SET @Result = -999;
        SET @Message = N'خطأ في إضافة الحساب: ' + ERROR_MESSAGE();
        
        -- تسجيل الخطأ
        INSERT INTO ErrorLog (ErrorMessage, ErrorProcedure, ErrorLine, ErrorDate)
        VALUES (ERROR_MESSAGE(), 'sp_AddAccountAdvanced', ERROR_LINE(), GETDATE());
        
    END CATCH
END
GO

-- ===================================================================
-- إجراء تحديث حساب مع التحقق من التكرار
-- Update Account with Duplicate Check
-- ===================================================================
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_UpdateAccountAdvanced')
    DROP PROCEDURE sp_UpdateAccountAdvanced;
GO

CREATE PROCEDURE sp_UpdateAccountAdvanced
    @AccountId INT,
    @AccountCode NVARCHAR(50),
    @AccountNameAr NVARCHAR(200),
    @AccountNameEn NVARCHAR(200) = NULL,
    @AccountTypeId INT,
    @AccountGroupId INT,
    @ParentAccountId INT = NULL,
    @LevelType INT,
    @Nature INT,
    @OpeningBalance DECIMAL(18,2),
    @OpeningBalanceType INT,
    @CurrencyId INT,
    @Description NVARCHAR(500) = NULL,
    @AllowPosting BIT,
    @AllowDirectEntry BIT,
    @IsActive BIT,
    @ModifiedBy INT,
    @Result INT OUTPUT,
    @Message NVARCHAR(500) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- التحقق من وجود الحساب
        IF NOT EXISTS (SELECT 1 FROM ChartOfAccounts WHERE AccountId = @AccountId)
        BEGIN
            SET @Result = -1;
            SET @Message = N'الحساب غير موجود';
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        -- التحقق من عدم تكرار رمز الحساب (عدا الحساب الحالي)
        IF EXISTS (SELECT 1 FROM ChartOfAccounts WHERE AccountCode = @AccountCode AND AccountId != @AccountId)
        BEGIN
            SET @Result = -2;
            SET @Message = N'رمز الحساب موجود مسبقاً';
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        -- التحقق من عدم تكرار اسم الحساب (عدا الحساب الحالي)
        IF EXISTS (SELECT 1 FROM ChartOfAccounts WHERE AccountNameAr = @AccountNameAr AND AccountId != @AccountId)
        BEGIN
            SET @Result = -3;
            SET @Message = N'اسم الحساب موجود مسبقاً';
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        -- التحقق من عدم جعل الحساب أب لنفسه
        IF @ParentAccountId = @AccountId
        BEGIN
            SET @Result = -4;
            SET @Message = N'لا يمكن جعل الحساب أب لنفسه';
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        -- التحقق من عدم وجود دورة في الهيكل الهرمي
        IF @ParentAccountId IS NOT NULL
        BEGIN
            DECLARE @CheckParentId INT = @ParentAccountId;
            DECLARE @LoopCounter INT = 0;
            
            WHILE @CheckParentId IS NOT NULL AND @LoopCounter < 10
            BEGIN
                IF @CheckParentId = @AccountId
                BEGIN
                    SET @Result = -5;
                    SET @Message = N'لا يمكن إنشاء دورة في الهيكل الهرمي';
                    ROLLBACK TRANSACTION;
                    RETURN;
                END
                
                SELECT @CheckParentId = ParentAccountId
                FROM ChartOfAccounts 
                WHERE AccountId = @CheckParentId;
                
                SET @LoopCounter = @LoopCounter + 1;
            END
        END
        
        -- حفظ البيانات القديمة للمراجعة
        DECLARE @OldData NVARCHAR(MAX);
        SELECT @OldData = (
            SELECT * FROM ChartOfAccounts WHERE AccountId = @AccountId FOR JSON AUTO
        );
        
        -- تحديث الحساب
        UPDATE ChartOfAccounts SET
            AccountCode = @AccountCode,
            AccountNameAr = @AccountNameAr,
            AccountNameEn = @AccountNameEn,
            AccountTypeId = @AccountTypeId,
            AccountGroupId = @AccountGroupId,
            ParentAccountId = @ParentAccountId,
            LevelType = @LevelType,
            Nature = @Nature,
            OpeningBalance = @OpeningBalance,
            OpeningBalanceType = @OpeningBalanceType,
            CurrencyId = @CurrencyId,
            Description = @Description,
            AllowPosting = @AllowPosting,
            AllowDirectEntry = @AllowDirectEntry,
            IsActive = @IsActive,
            ModifiedDate = GETDATE(),
            ModifiedBy = @ModifiedBy
        WHERE AccountId = @AccountId;
        
        -- تسجيل التعديل في سجل المراجعة
        DECLARE @NewData NVARCHAR(MAX);
        SELECT @NewData = (
            SELECT * FROM ChartOfAccounts WHERE AccountId = @AccountId FOR JSON AUTO
        );
        
        INSERT INTO AccountAuditLog (
            AccountId, AccountCode, Operation, OldData, NewData,
            UserId, UserName, AuditDate, Comments
        ) VALUES (
            @AccountId, @AccountCode, 'UPDATE', @OldData, @NewData,
            @ModifiedBy, (SELECT Username FROM Users WHERE UserId = @ModifiedBy),
            GETDATE(), N'تحديث بيانات الحساب'
        );
        
        SET @Result = 1;
        SET @Message = N'تم تحديث الحساب بنجاح';
        
        COMMIT TRANSACTION;
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        
        SET @Result = -999;
        SET @Message = N'خطأ في تحديث الحساب: ' + ERROR_MESSAGE();
        
    END CATCH
END
GO

-- ===================================================================
-- إجراء حذف حساب مع التحقق من الاستخدام
-- Delete Account with Usage Check
-- ===================================================================
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_DeleteAccountAdvanced')
    DROP PROCEDURE sp_DeleteAccountAdvanced;
GO

CREATE PROCEDURE sp_DeleteAccountAdvanced
    @AccountId INT,
    @DeletedBy INT,
    @ForceDelete BIT = 0,
    @Result INT OUTPUT,
    @Message NVARCHAR(500) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- التحقق من وجود الحساب
        IF NOT EXISTS (SELECT 1 FROM ChartOfAccounts WHERE AccountId = @AccountId)
        BEGIN
            SET @Result = -1;
            SET @Message = N'الحساب غير موجود';
            ROLLBACK TRANSACTION;
            RETURN;
        END
        
        -- التحقق من وجود حسابات فرعية
        IF EXISTS (SELECT 1 FROM ChartOfAccounts WHERE ParentAccountId = @AccountId)
        BEGIN
            IF @ForceDelete = 0
            BEGIN
                SET @Result = -2;
                SET @Message = N'لا يمكن حذف الحساب لوجود حسابات فرعية';
                ROLLBACK TRANSACTION;
                RETURN;
            END
            ELSE
            BEGIN
                -- حذف الحسابات الفرعية أولاً
                DELETE FROM ChartOfAccounts WHERE ParentAccountId = @AccountId;
            END
        END
        
        -- التحقق من وجود قيود محاسبية (إذا كان الجدول موجود)
        IF EXISTS (SELECT * FROM sys.tables WHERE name = 'JournalEntryDetails')
        BEGIN
            IF EXISTS (SELECT 1 FROM JournalEntryDetails WHERE AccountId = @AccountId)
            BEGIN
                SET @Result = -3;
                SET @Message = N'لا يمكن حذف الحساب لوجود قيود محاسبية';
                ROLLBACK TRANSACTION;
                RETURN;
            END
        END
        
        -- حفظ بيانات الحساب قبل الحذف
        DECLARE @AccountData NVARCHAR(MAX);
        DECLARE @AccountCode NVARCHAR(50);
        
        SELECT @AccountData = (
            SELECT * FROM ChartOfAccounts WHERE AccountId = @AccountId FOR JSON AUTO
        ), @AccountCode = AccountCode
        FROM ChartOfAccounts WHERE AccountId = @AccountId;
        
        -- حذف الحساب
        DELETE FROM ChartOfAccounts WHERE AccountId = @AccountId;
        
        -- تسجيل الحذف في سجل المراجعة
        INSERT INTO AccountAuditLog (
            AccountId, AccountCode, Operation, OldData,
            UserId, UserName, AuditDate, Comments
        ) VALUES (
            @AccountId, @AccountCode, 'DELETE', @AccountData,
            @DeletedBy, (SELECT Username FROM Users WHERE UserId = @DeletedBy),
            GETDATE(), N'حذف الحساب'
        );
        
        SET @Result = 1;
        SET @Message = N'تم حذف الحساب بنجاح';
        
        COMMIT TRANSACTION;
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        
        SET @Result = -999;
        SET @Message = N'خطأ في حذف الحساب: ' + ERROR_MESSAGE();
        
    END CATCH
END
GO

-- ===================================================================
-- إجراء البحث المتقدم في الحسابات
-- Advanced Account Search
-- ===================================================================
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_SearchAccountsAdvanced')
    DROP PROCEDURE sp_SearchAccountsAdvanced;
GO

CREATE PROCEDURE sp_SearchAccountsAdvanced
    @SearchTerm NVARCHAR(200) = NULL,
    @AccountTypeId INT = NULL,
    @AccountGroupId INT = NULL,
    @ParentAccountId INT = NULL,
    @IsActive BIT = NULL,
    @IsParentOnly BIT = 0,
    @MinBalance DECIMAL(18,2) = NULL,
    @MaxBalance DECIMAL(18,2) = NULL,
    @SortBy NVARCHAR(50) = 'AccountCode',
    @SortDirection NVARCHAR(4) = 'ASC',
    @PageNumber INT = 1,
    @PageSize INT = 50
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize;
    DECLARE @SQL NVARCHAR(MAX);
    DECLARE @WhereClause NVARCHAR(MAX) = '';
    DECLARE @OrderClause NVARCHAR(200);

    -- بناء جملة WHERE ديناميكياً
    SET @WhereClause = 'WHERE 1=1';

    IF @SearchTerm IS NOT NULL AND LTRIM(RTRIM(@SearchTerm)) != ''
    BEGIN
        SET @WhereClause = @WhereClause + ' AND (
            ca.AccountCode LIKE ''%' + @SearchTerm + '%'' OR
            ca.AccountNameAr LIKE ''%' + @SearchTerm + '%'' OR
            ca.AccountNameEn LIKE ''%' + @SearchTerm + '%'' OR
            ca.Description LIKE ''%' + @SearchTerm + '%''
        )';
    END

    IF @AccountTypeId IS NOT NULL
        SET @WhereClause = @WhereClause + ' AND ca.AccountTypeId = ' + CAST(@AccountTypeId AS NVARCHAR(10));

    IF @AccountGroupId IS NOT NULL
        SET @WhereClause = @WhereClause + ' AND ca.AccountGroupId = ' + CAST(@AccountGroupId AS NVARCHAR(10));

    IF @ParentAccountId IS NOT NULL
        SET @WhereClause = @WhereClause + ' AND ca.ParentAccountId = ' + CAST(@ParentAccountId AS NVARCHAR(10));

    IF @IsActive IS NOT NULL
        SET @WhereClause = @WhereClause + ' AND ca.IsActive = ' + CAST(@IsActive AS NVARCHAR(1));

    IF @IsParentOnly = 1
        SET @WhereClause = @WhereClause + ' AND (ca.IsParent = 1 OR ca.IsParentAccount = 1)';

    IF @MinBalance IS NOT NULL
        SET @WhereClause = @WhereClause + ' AND ca.OpeningBalance >= ' + CAST(@MinBalance AS NVARCHAR(20));

    IF @MaxBalance IS NOT NULL
        SET @WhereClause = @WhereClause + ' AND ca.OpeningBalance <= ' + CAST(@MaxBalance AS NVARCHAR(20));

    -- بناء جملة ORDER BY
    SET @OrderClause = 'ORDER BY ' + @SortBy + ' ' + @SortDirection;

    -- بناء الاستعلام الكامل
    SET @SQL = '
    SELECT
        ca.AccountId,
        ca.AccountCode,
        ca.AccountNameAr,
        ca.AccountNameEn,
        ca.AccountTypeId,
        at.AccountTypeNameAr,
        ca.AccountGroupId,
        ag.GroupNameAr,
        ca.ParentAccountId,
        pa.AccountNameAr as ParentAccountName,
        ca.LevelType,
        ca.Nature,
        ca.OpeningBalance,
        ca.CurrentBalance,
        ca.IsActive,
        ca.AllowPosting,
        ca.Description,
        ca.CreatedDate,
        COUNT(*) OVER() as TotalRecords
    FROM ChartOfAccounts ca
    LEFT JOIN AccountTypes at ON ca.AccountTypeId = at.AccountTypeId
    LEFT JOIN AccountGroups ag ON ca.AccountGroupId = ag.AccountGroupId
    LEFT JOIN ChartOfAccounts pa ON ca.ParentAccountId = pa.AccountId
    ' + @WhereClause + '
    ' + @OrderClause + '
    OFFSET ' + CAST(@Offset AS NVARCHAR(10)) + ' ROWS
    FETCH NEXT ' + CAST(@PageSize AS NVARCHAR(10)) + ' ROWS ONLY';

    EXEC sp_executesql @SQL;
END
GO

-- ===================================================================
-- إجراء الحصول على الهيكل الهرمي للحسابات
-- Get Account Hierarchy
-- ===================================================================
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_GetAccountHierarchy')
    DROP PROCEDURE sp_GetAccountHierarchy;
GO

CREATE PROCEDURE sp_GetAccountHierarchy
    @RootAccountId INT = NULL,
    @MaxDepth INT = 10,
    @IncludeInactive BIT = 0
AS
BEGIN
    SET NOCOUNT ON;

    WITH AccountHierarchy AS (
        -- المستوى الأول (الجذر)
        SELECT
            AccountId,
            AccountCode,
            AccountNameAr,
            AccountNameEn,
            ParentAccountId,
            AccountLevel,
            IsParent,
            IsActive,
            OpeningBalance,
            0 as Depth,
            CAST(AccountCode as NVARCHAR(MAX)) as HierarchyPath,
            CAST(AccountNameAr as NVARCHAR(MAX)) as HierarchyName
        FROM ChartOfAccounts
        WHERE (@RootAccountId IS NULL AND ParentAccountId IS NULL)
           OR (@RootAccountId IS NOT NULL AND AccountId = @RootAccountId)
           AND (@IncludeInactive = 1 OR IsActive = 1)

        UNION ALL

        -- المستويات الفرعية
        SELECT
            c.AccountId,
            c.AccountCode,
            c.AccountNameAr,
            c.AccountNameEn,
            c.ParentAccountId,
            c.AccountLevel,
            c.IsParent,
            c.IsActive,
            c.OpeningBalance,
            h.Depth + 1,
            h.HierarchyPath + ' > ' + c.AccountCode,
            h.HierarchyName + ' > ' + c.AccountNameAr
        FROM ChartOfAccounts c
        INNER JOIN AccountHierarchy h ON c.ParentAccountId = h.AccountId
        WHERE h.Depth < @MaxDepth
          AND (@IncludeInactive = 1 OR c.IsActive = 1)
    )
    SELECT
        AccountId,
        AccountCode,
        AccountNameAr,
        AccountNameEn,
        ParentAccountId,
        AccountLevel,
        IsParent,
        IsActive,
        OpeningBalance,
        Depth,
        HierarchyPath,
        HierarchyName,
        REPLICATE('  ', Depth) + AccountNameAr as IndentedName
    FROM AccountHierarchy
    ORDER BY HierarchyPath;
END
GO

-- ===================================================================
-- إجراء التحقق من صحة البيانات
-- Data Validation Procedure
-- ===================================================================
IF EXISTS (SELECT * FROM sys.procedures WHERE name = 'sp_ValidateAccountData')
    DROP PROCEDURE sp_ValidateAccountData;
GO

CREATE PROCEDURE sp_ValidateAccountData
    @ShowDetails BIT = 1
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @ErrorCount INT = 0;
    DECLARE @WarningCount INT = 0;

    -- إنشاء جدول مؤقت للنتائج
    CREATE TABLE #ValidationResults (
        ValidationId INT IDENTITY(1,1),
        Severity NVARCHAR(20),
        Category NVARCHAR(50),
        Description NVARCHAR(500),
        AccountId INT NULL,
        AccountCode NVARCHAR(50) NULL,
        AccountName NVARCHAR(200) NULL
    );

    -- فحص الحسابات المكررة (رمز الحساب)
    INSERT INTO #ValidationResults (Severity, Category, Description, AccountCode)
    SELECT
        'خطأ' as Severity,
        'تكرار البيانات' as Category,
        'رمز حساب مكرر: ' + AccountCode as Description,
        AccountCode
    FROM ChartOfAccounts
    GROUP BY AccountCode
    HAVING COUNT(*) > 1;

    SET @ErrorCount = @ErrorCount + @@ROWCOUNT;

    -- فحص الحسابات المكررة (اسم الحساب)
    INSERT INTO #ValidationResults (Severity, Category, Description, AccountName)
    SELECT
        'خطأ' as Severity,
        'تكرار البيانات' as Category,
        'اسم حساب مكرر: ' + AccountNameAr as Description,
        AccountNameAr
    FROM ChartOfAccounts
    GROUP BY AccountNameAr
    HAVING COUNT(*) > 1;

    SET @ErrorCount = @ErrorCount + @@ROWCOUNT;

    -- فحص الحسابات اليتيمة (حساب أب غير موجود)
    INSERT INTO #ValidationResults (Severity, Category, Description, AccountId, AccountCode, AccountName)
    SELECT
        'خطأ' as Severity,
        'علاقات خاطئة' as Category,
        'حساب أب غير موجود' as Description,
        c.AccountId,
        c.AccountCode,
        c.AccountNameAr
    FROM ChartOfAccounts c
    WHERE c.ParentAccountId IS NOT NULL
      AND NOT EXISTS (SELECT 1 FROM ChartOfAccounts p WHERE p.AccountId = c.ParentAccountId);

    SET @ErrorCount = @ErrorCount + @@ROWCOUNT;

    -- فحص الحسابات الأب التي تسمح بالترحيل
    INSERT INTO #ValidationResults (Severity, Category, Description, AccountId, AccountCode, AccountName)
    SELECT
        'تحذير' as Severity,
        'إعدادات خاطئة' as Category,
        'حساب أب يسمح بالترحيل' as Description,
        AccountId,
        AccountCode,
        AccountNameAr
    FROM ChartOfAccounts
    WHERE (IsParent = 1 OR IsParentAccount = 1) AND AllowPosting = 1;

    SET @WarningCount = @WarningCount + @@ROWCOUNT;

    -- فحص الحسابات بدون نوع أو مجموعة
    INSERT INTO #ValidationResults (Severity, Category, Description, AccountId, AccountCode, AccountName)
    SELECT
        'تحذير' as Severity,
        'بيانات ناقصة' as Category,
        'حساب بدون نوع أو مجموعة صحيحة' as Description,
        c.AccountId,
        c.AccountCode,
        c.AccountNameAr
    FROM ChartOfAccounts c
    WHERE NOT EXISTS (SELECT 1 FROM AccountTypes at WHERE at.AccountTypeId = c.AccountTypeId AND at.IsActive = 1)
       OR NOT EXISTS (SELECT 1 FROM AccountGroups ag WHERE ag.AccountGroupId = c.AccountGroupId AND ag.IsActive = 1);

    SET @WarningCount = @WarningCount + @@ROWCOUNT;

    -- عرض النتائج
    IF @ShowDetails = 1
    BEGIN
        SELECT * FROM #ValidationResults ORDER BY Severity DESC, Category, ValidationId;
    END

    -- عرض الملخص
    SELECT
        @ErrorCount as ErrorCount,
        @WarningCount as WarningCount,
        (@ErrorCount + @WarningCount) as TotalIssues,
        CASE
            WHEN @ErrorCount = 0 AND @WarningCount = 0 THEN N'البيانات سليمة'
            WHEN @ErrorCount = 0 THEN N'توجد تحذيرات فقط'
            ELSE N'توجد أخطاء تحتاج إصلاح'
        END as ValidationStatus;

    DROP TABLE #ValidationResults;
END
GO

PRINT N'✓ تم إنشاء الإجراءات المخزنة المتقدمة بنجاح';
GO
