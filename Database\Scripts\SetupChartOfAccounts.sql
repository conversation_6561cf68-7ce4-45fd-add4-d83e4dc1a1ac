-- ===================================================================
-- إعداد شامل للدليل المحاسبي - نظام إدارة الأوقاف
-- Complete Chart of Accounts Setup - Awqaf Management System
-- ===================================================================

USE AwqafManagement;
GO

PRINT N'=== بدء إعداد الدليل المحاسبي ===';
PRINT '=== Starting Chart of Accounts Setup ===';
PRINT '';

-- ===================================================================
-- الخطوة 1: حذف البيانات الموجودة (إذا وجدت)
-- Step 1: Clear existing data (if any)
-- ===================================================================

PRINT N'الخطوة 1: حذف البيانات الموجودة...';
PRINT 'Step 1: Clearing existing data...';

-- تعطيل فحص المفاتيح الخارجية مؤقتاً
-- Temporarily disable foreign key checks
IF OBJECT_ID('ChartOfAccounts', 'U') IS NOT NULL
BEGIN
    DELETE FROM ChartOfAccounts;
    PRINT N'✓ تم حذف الحسابات الموجودة';
END

IF OBJECT_ID('AccountGroups', 'U') IS NOT NULL
BEGIN
    DELETE FROM AccountGroups;
    PRINT N'✓ تم حذف مجموعات الحسابات الموجودة';
END

IF OBJECT_ID('AccountTypes', 'U') IS NOT NULL
BEGIN
    DELETE FROM AccountTypes;
    PRINT N'✓ تم حذف أنواع الحسابات الموجودة';
END

-- إعادة تعيين العدادات التلقائية
-- Reset identity counters
IF OBJECT_ID('ChartOfAccounts', 'U') IS NOT NULL
    DBCC CHECKIDENT ('ChartOfAccounts', RESEED, 0);

IF OBJECT_ID('AccountGroups', 'U') IS NOT NULL
    DBCC CHECKIDENT ('AccountGroups', RESEED, 0);

IF OBJECT_ID('AccountTypes', 'U') IS NOT NULL
    DBCC CHECKIDENT ('AccountTypes', RESEED, 0);

PRINT N'✓ تم إعادة تعيين العدادات التلقائية';
PRINT '';

-- ===================================================================
-- الخطوة 2: إدراج أنواع الحسابات
-- Step 2: Insert Account Types
-- ===================================================================

PRINT N'الخطوة 2: إدراج أنواع الحسابات...';
PRINT 'Step 2: Inserting Account Types...';

INSERT INTO AccountTypes (AccountTypeCode, AccountTypeName, AccountTypeNameAr, Description, DisplayOrder, IsActive, CreatedDate)
VALUES
    ('AST', 'Assets', N'الأصول', N'جميع الأصول المملوكة للمؤسسة', 1, 1, GETDATE()),
    ('LIB', 'Liabilities', N'الخصوم', N'جميع الالتزامات والديون', 2, 1, GETDATE()),
    ('EQT', 'Equity', N'حقوق الملكية', N'حقوق أصحاب المؤسسة', 3, 1, GETDATE()),
    ('REV', 'Revenue', N'الإيرادات', N'جميع الإيرادات والدخل', 4, 1, GETDATE()),
    ('EXP', 'Expenses', N'المصروفات', N'جميع المصروفات والتكاليف', 5, 1, GETDATE());

PRINT N'✓ تم إدراج ' + CAST(@@ROWCOUNT AS NVARCHAR(10)) + N' أنواع حسابات';
PRINT '';

-- ===================================================================
-- الخطوة 3: إدراج مجموعات الحسابات
-- Step 3: Insert Account Groups
-- ===================================================================

PRINT N'الخطوة 3: إدراج مجموعات الحسابات...';
PRINT 'Step 3: Inserting Account Groups...';

INSERT INTO AccountGroups (GroupCode, GroupName, GroupNameAr, AccountTypeId, Description, DisplayOrder, IsActive, CreatedDate)
VALUES
    ('CA', 'Current Assets', N'أصول متداولة', 1, N'الأصول التي يمكن تحويلها لنقد خلال سنة', 1, 1, GETDATE()),
    ('FA', 'Fixed Assets', N'أصول ثابتة', 1, N'الأصول طويلة الأجل', 2, 1, GETDATE()),
    ('CL', 'Current Liabilities', N'خصوم متداولة', 2, N'الالتزامات المستحقة خلال سنة', 1, 1, GETDATE()),
    ('LL', 'Long-term Liabilities', N'خصوم طويلة الأجل', 2, N'الالتزامات طويلة الأجل', 2, 1, GETDATE()),
    ('EQ', 'Equity', N'حقوق الملكية', 3, N'حقوق أصحاب المؤسسة', 1, 1, GETDATE()),
    ('OR', 'Operating Revenue', N'إيرادات التشغيل', 4, N'الإيرادات من الأنشطة الرئيسية', 1, 1, GETDATE()),
    ('OE', 'Operating Expenses', N'مصروفات التشغيل', 5, N'المصروفات التشغيلية', 1, 1, GETDATE());

PRINT N'✓ تم إدراج ' + CAST(@@ROWCOUNT AS NVARCHAR(10)) + N' مجموعات حسابات';
PRINT '';

-- ===================================================================
-- الخطوة 4: إدراج العملات (إذا لم تكن موجودة)
-- Step 4: Insert Currencies (if not exists)
-- ===================================================================

PRINT N'الخطوة 4: التحقق من العملات...';
PRINT 'Step 4: Checking Currencies...';

IF NOT EXISTS (SELECT 1 FROM Currencies WHERE CurrencyCode = 'SAR')
BEGIN
    INSERT INTO Currencies (CurrencyCode, CurrencyNameAr, CurrencyNameEn, Symbol, ExchangeRate, IsBaseCurrency, IsActive, CreatedDate)
    VALUES ('SAR', N'ريال سعودي', 'Saudi Riyal', N'ر.س', 1.0, 1, 1, GETDATE());
    PRINT N'✓ تم إدراج الريال السعودي';
END
ELSE
    PRINT N'✓ الريال السعودي موجود مسبقاً';

IF NOT EXISTS (SELECT 1 FROM Currencies WHERE CurrencyCode = 'USD')
BEGIN
    INSERT INTO Currencies (CurrencyCode, CurrencyNameAr, CurrencyNameEn, Symbol, ExchangeRate, IsBaseCurrency, IsActive, CreatedDate)
    VALUES ('USD', N'دولار أمريكي', 'US Dollar', '$', 3.75, 0, 1, GETDATE());
    PRINT N'✓ تم إدراج الدولار الأمريكي';
END
ELSE
    PRINT N'✓ الدولار الأمريكي موجود مسبقاً';

PRINT '';

-- ===================================================================
-- الخطوة 5: إدراج الحسابات الافتراضية
-- Step 5: Insert Default Accounts
-- ===================================================================

PRINT N'الخطوة 5: إدراج الحسابات الافتراضية...';
PRINT 'Step 5: Inserting Default Accounts...';

-- سيتم تشغيل ملف InsertDefaultChartOfAccounts.sql هنا
-- أو يمكن نسخ محتوياته مباشرة

PRINT N'✓ يرجى تشغيل ملف InsertDefaultChartOfAccounts.sql لإدراج الحسابات';
PRINT '✓ Please run InsertDefaultChartOfAccounts.sql to insert accounts';
PRINT '';

-- ===================================================================
-- الخطوة 6: التحقق من النتائج
-- Step 6: Verification
-- ===================================================================

PRINT N'=== التحقق من النتائج ===';
PRINT '=== Verification Results ===';
PRINT '';

DECLARE @AccountTypesCount INT = (SELECT COUNT(*) FROM AccountTypes);
DECLARE @AccountGroupsCount INT = (SELECT COUNT(*) FROM AccountGroups);
DECLARE @CurrenciesCount INT = (SELECT COUNT(*) FROM Currencies);
DECLARE @AccountsCount INT = (SELECT COUNT(*) FROM ChartOfAccounts);

PRINT N'عدد أنواع الحسابات: ' + CAST(@AccountTypesCount AS NVARCHAR(10));
PRINT N'عدد مجموعات الحسابات: ' + CAST(@AccountGroupsCount AS NVARCHAR(10));
PRINT N'عدد العملات: ' + CAST(@CurrenciesCount AS NVARCHAR(10));
PRINT N'عدد الحسابات: ' + CAST(@AccountsCount AS NVARCHAR(10));

PRINT '';
PRINT N'=== اكتمل إعداد الدليل المحاسبي ===';
PRINT '=== Chart of Accounts Setup Complete ===';

-- عرض الحسابات الرئيسية
IF @AccountsCount > 0
BEGIN
    PRINT '';
    PRINT N'الحسابات الرئيسية:';
    PRINT 'Main Accounts:';
    SELECT AccountCode as 'رمز الحساب', AccountNameAr as 'اسم الحساب', AccountNameEn as 'Account Name'
    FROM ChartOfAccounts 
    WHERE LevelType = 'Main'
    ORDER BY AccountCode;
END
