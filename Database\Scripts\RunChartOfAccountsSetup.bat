@echo off
chcp 65001 >nul
echo ===================================================================
echo إعداد الدليل المحاسبي - نظام إدارة الأوقاف
echo Chart of Accounts Setup - Awqaf Management System
echo ===================================================================
echo.

echo التحقق من الاتصال بقاعدة البيانات...
echo Checking database connection...

:: التحقق من وجود SQL Server
sqlcmd -S NAJEEB -E -Q "SELECT @@VERSION" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ خطأ: لا يمكن الاتصال بخادم SQL Server: NAJEEB
    echo ❌ Error: Cannot connect to SQL Server: NAJEEB
    echo.
    echo يرجى التأكد من:
    echo Please check:
    echo 1. تشغيل خدمة SQL Server
    echo    SQL Server service is running
    echo 2. صحة اسم الخادم: NAJEEB
    echo    Server name is correct: NAJEEB
    echo 3. صلاحيات الوصول
    echo    Access permissions
    echo.
    pause
    exit /b 1
)

echo ✅ تم الاتصال بخادم SQL Server بنجاح
echo ✅ Successfully connected to SQL Server
echo.

:: التحقق من وجود قاعدة البيانات
sqlcmd -S NAJEEB -E -Q "SELECT name FROM sys.databases WHERE name = 'AwqafManagement'" -h -1 | findstr "AwqafManagement" >nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ خطأ: قاعدة البيانات AwqafManagement غير موجودة
    echo ❌ Error: Database AwqafManagement does not exist
    echo.
    echo يرجى تشغيل ملفات إنشاء قاعدة البيانات أولاً:
    echo Please run database creation scripts first:
    echo 1. Database\01_CreateDatabase.sql
    echo 2. Database\Scripts\04_Create_Accounting_Tables.sql
    echo.
    pause
    exit /b 1
)

echo ✅ قاعدة البيانات AwqafManagement موجودة
echo ✅ Database AwqafManagement exists
echo.

echo هل تريد حذف البيانات الموجودة وإعادة البدء من جديد؟
echo Do you want to clear existing data and start fresh?
echo [Y] نعم / Yes    [N] لا / No    [C] إلغاء / Cancel
set /p choice="اختر [Y/N/C]: "

if /i "%choice%"=="C" (
    echo تم إلغاء العملية
    echo Operation cancelled
    pause
    exit /b 0
)

if /i "%choice%"=="Y" (
    echo.
    echo حذف البيانات الموجودة...
    echo Clearing existing data...
    
    sqlcmd -S NAJEEB -E -i ClearChartOfAccounts.sql
    if %ERRORLEVEL% NEQ 0 (
        echo ❌ خطأ في حذف البيانات
        echo ❌ Error clearing data
        pause
        exit /b 1
    )
    
    echo ✅ تم حذف البيانات بنجاح
    echo ✅ Data cleared successfully
    echo.
)

echo اختبار بنية الجداول أولاً...
echo Testing table structure first...

sqlcmd -S NAJEEB -E -i TestChartOfAccountsStructure.sql
if %ERRORLEVEL% NEQ 0 (
    echo ❌ خطأ في بنية الجداول
    echo ❌ Error in table structure
    echo.
    echo يرجى تشغيل ملفات إنشاء الجداول أولاً:
    echo Please run table creation scripts first:
    echo Database\Scripts\04_Create_Accounting_Tables.sql
    pause
    exit /b 1
)

echo ✅ بنية الجداول صحيحة
echo ✅ Table structure is correct
echo.

echo إدراج البيانات الافتراضية...
echo Inserting default data...

sqlcmd -S NAJEEB -E -i InsertDefaultChartOfAccounts.sql
if %ERRORLEVEL% NEQ 0 (
    echo ❌ خطأ في إدراج البيانات
    echo ❌ Error inserting data
    pause
    exit /b 1
)

echo ✅ تم إدراج البيانات الافتراضية بنجاح
echo ✅ Default data inserted successfully
echo.

echo التحقق من النتائج...
echo Verifying results...

echo.
echo === إحصائيات قاعدة البيانات ===
echo === Database Statistics ===

sqlcmd -S NAJEEB -E -Q "USE AwqafManagement; SELECT 'أنواع الحسابات' as 'النوع', COUNT(*) as 'العدد' FROM AccountTypes UNION ALL SELECT 'مجموعات الحسابات', COUNT(*) FROM AccountGroups UNION ALL SELECT 'الحسابات', COUNT(*) FROM ChartOfAccounts UNION ALL SELECT 'العملات', COUNT(*) FROM Currencies"

echo.
echo === الحسابات الرئيسية ===
echo === Main Accounts ===

sqlcmd -S NAJEEB -E -Q "USE AwqafManagement; SELECT AccountCode as 'رمز الحساب', AccountNameAr as 'اسم الحساب' FROM ChartOfAccounts WHERE LevelType = 'Main' ORDER BY AccountCode"

echo.
echo ===================================================================
echo ✅ تم إعداد الدليل المحاسبي بنجاح!
echo ✅ Chart of Accounts setup completed successfully!
echo ===================================================================
echo.
echo يمكنك الآن:
echo You can now:
echo 1. تشغيل التطبيق واستخدام الدليل المحاسبي
echo    Run the application and use the Chart of Accounts
echo 2. إضافة حسابات جديدة حسب احتياجاتك
echo    Add new accounts as needed
echo 3. تعديل الحسابات الموجودة
echo    Modify existing accounts
echo.

pause
