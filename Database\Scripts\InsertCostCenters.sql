USE AwqafManagement;
GO

-- حذف البيانات الموجودة إذا كانت موجودة
DELETE FROM CostCenters;
GO

-- إدراج مراكز التكلفة الافتراضية
-- نحاول إدراج البيانات بطرق مختلفة حسب بنية الجدول

-- الطريقة الأولى: مع جميع الأعمدة
BEGIN TRY
    INSERT INTO CostCenters (CostCenterCode, CostCenterNameAr, CostCenterNameEn, Description, IsActive)
    VALUES 
        ('CC001', N'الإدارة العامة', 'General Administration', N'مركز تكلفة الإدارة العامة', 1),
        ('CC002', N'المحاسبة والمالية', 'Accounting & Finance', N'مركز تكلفة المحاسبة والمالية', 1),
        ('CC003', N'الموارد البشرية', 'Human Resources', N'مركز تكلفة الموارد البشرية', 1),
        ('CC004', N'تقنية المعلومات', 'Information Technology', N'مركز تكلفة تقنية المعلومات', 1),
        ('CC005', N'الصيانة والخدمات', 'Maintenance & Services', N'مركز تكلفة الصيانة والخدمات', 1),
        ('CC006', N'التسويق والمبيعات', 'Marketing & Sales', N'مركز تكلفة التسويق والمبيعات', 1),
        ('CC007', N'المشاريع الخاصة', 'Special Projects', N'مركز تكلفة المشاريع الخاصة', 1),
        ('CC008', N'البحث والتطوير', 'Research & Development', N'مركز تكلفة البحث والتطوير', 1);
    
    PRINT 'تم إدراج مراكز التكلفة بنجاح - الطريقة الأولى';
END TRY
BEGIN CATCH
    PRINT 'فشل في الطريقة الأولى: ' + ERROR_MESSAGE();
    
    -- الطريقة الثانية: بدون CostCenterNameEn
    BEGIN TRY
        INSERT INTO CostCenters (CostCenterCode, CostCenterNameAr, Description, IsActive)
        VALUES 
            ('CC001', N'الإدارة العامة', N'مركز تكلفة الإدارة العامة', 1),
            ('CC002', N'المحاسبة والمالية', N'مركز تكلفة المحاسبة والمالية', 1),
            ('CC003', N'الموارد البشرية', N'مركز تكلفة الموارد البشرية', 1),
            ('CC004', N'تقنية المعلومات', N'مركز تكلفة تقنية المعلومات', 1),
            ('CC005', N'الصيانة والخدمات', N'مركز تكلفة الصيانة والخدمات', 1),
            ('CC006', N'التسويق والمبيعات', N'مركز تكلفة التسويق والمبيعات', 1),
            ('CC007', N'المشاريع الخاصة', N'مركز تكلفة المشاريع الخاصة', 1),
            ('CC008', N'البحث والتطوير', N'مركز تكلفة البحث والتطوير', 1);
        
        PRINT 'تم إدراج مراكز التكلفة بنجاح - الطريقة الثانية';
    END TRY
    BEGIN CATCH
        PRINT 'فشل في الطريقة الثانية: ' + ERROR_MESSAGE();
        
        -- الطريقة الثالثة: مع CostCenterName بدلاً من CostCenterNameAr
        BEGIN TRY
            INSERT INTO CostCenters (CostCenterCode, CostCenterName, Description, IsActive)
            VALUES 
                ('CC001', N'الإدارة العامة', N'مركز تكلفة الإدارة العامة', 1),
                ('CC002', N'المحاسبة والمالية', N'مركز تكلفة المحاسبة والمالية', 1),
                ('CC003', N'الموارد البشرية', N'مركز تكلفة الموارد البشرية', 1),
                ('CC004', N'تقنية المعلومات', N'مركز تكلفة تقنية المعلومات', 1),
                ('CC005', N'الصيانة والخدمات', N'مركز تكلفة الصيانة والخدمات', 1),
                ('CC006', N'التسويق والمبيعات', N'مركز تكلفة التسويق والمبيعات', 1),
                ('CC007', N'المشاريع الخاصة', N'مركز تكلفة المشاريع الخاصة', 1),
                ('CC008', N'البحث والتطوير', N'مركز تكلفة البحث والتطوير', 1);
            
            PRINT 'تم إدراج مراكز التكلفة بنجاح - الطريقة الثالثة';
        END TRY
        BEGIN CATCH
            PRINT 'فشل في الطريقة الثالثة: ' + ERROR_MESSAGE();
            
            -- الطريقة الرابعة: الحد الأدنى من الأعمدة
            BEGIN TRY
                INSERT INTO CostCenters (CostCenterCode, CostCenterName)
                VALUES 
                    ('CC001', N'الإدارة العامة'),
                    ('CC002', N'المحاسبة والمالية'),
                    ('CC003', N'الموارد البشرية'),
                    ('CC004', N'تقنية المعلومات'),
                    ('CC005', N'الصيانة والخدمات'),
                    ('CC006', N'التسويق والمبيعات'),
                    ('CC007', N'المشاريع الخاصة'),
                    ('CC008', N'البحث والتطوير');
                
                PRINT 'تم إدراج مراكز التكلفة بنجاح - الطريقة الرابعة';
            END TRY
            BEGIN CATCH
                PRINT 'فشل في جميع الطرق: ' + ERROR_MESSAGE();
            END CATCH
        END CATCH
    END CATCH
END CATCH

-- عرض النتائج
SELECT * FROM CostCenters;
GO
