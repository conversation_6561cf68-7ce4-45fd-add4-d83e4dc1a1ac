@echo off
chcp 65001 > nul
echo ========================================
echo 🧪 اختبار شامل للنظام
echo 🧪 Comprehensive System Test
echo ========================================
echo.

echo 📋 1. اختبار قاعدة البيانات...
echo 📋 1. Testing Database...
echo.

REM Test database connection
sqlcmd -S NAJEEB -d AwqafManagement -E -Q "SELECT 1 as TestConnection" > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ خطأ: لا يمكن الاتصال بقاعدة البيانات
    echo ❌ Error: Cannot connect to database
    pause
    exit /b 1
)
echo ✅ الاتصال بقاعدة البيانات ناجح
echo ✅ Database connection successful

echo.
echo 📊 2. اختبار جداول النظام...
echo 📊 2. Testing System Tables...

REM Test main tables
sqlcmd -S NAJEEB -d AwqafManagement -E -Q "SELECT COUNT(*) as AccountTypesCount FROM AccountTypes WHERE IsActive = 1"
sqlcmd -S NAJEEB -d AwqafManagement -E -Q "SELECT COUNT(*) as AccountGroupsCount FROM AccountGroups WHERE IsActive = 1"
sqlcmd -S NAJEEB -d AwqafManagement -E -Q "SELECT COUNT(*) as AccountsCount FROM ChartOfAccounts WHERE IsActive = 1"

echo.
echo 🏗️ 3. اختبار بناء المشروع...
echo 🏗️ 3. Testing Project Build...

REM Check if executable exists
if exist "bin\Debug\Awqaf_Managment_Backup.exe" (
    echo ✅ ملف التطبيق موجود
    echo ✅ Application file exists
) else (
    echo ❌ ملف التطبيق غير موجود
    echo ❌ Application file not found
    pause
    exit /b 1
)

echo.
echo 🖥️ 4. اختبار تشغيل التطبيق...
echo 🖥️ 4. Testing Application Launch...

REM Try to launch application
start "" "bin\Debug\Awqaf_Managment_Backup.exe"
timeout /t 3 > nul

REM Check if process is running
tasklist /FI "IMAGENAME eq Awqaf_Managment_Backup.exe" 2>NUL | find /I /N "Awqaf_Managment_Backup.exe" > nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ التطبيق يعمل بنجاح
    echo ✅ Application is running successfully
) else (
    echo ❌ فشل في تشغيل التطبيق
    echo ❌ Failed to launch application
)

echo.
echo 📝 5. اختبار وحدات النظام...
echo 📝 5. Testing System Modules...

echo.
echo   📈 أ. وحدة الدليل المحاسبي:
echo   📈 a. Chart of Accounts Module:
sqlcmd -S NAJEEB -d AwqafManagement -E -Q "SELECT TOP 5 AccountCode, AccountNameAr FROM ChartOfAccounts WHERE IsActive = 1 ORDER BY AccountCode"

echo.
echo   📋 ب. وحدة القيود اليومية:
echo   📋 b. Journal Entries Module:
sqlcmd -S NAJEEB -d AwqafManagement -E -Q "SELECT COUNT(*) as JournalEntriesCount FROM JournalEntries" 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ⚠️ جدول القيود اليومية غير موجود أو فارغ
    echo ⚠️ Journal entries table not found or empty
) else (
    echo ✅ جدول القيود اليومية موجود
    echo ✅ Journal entries table exists
)

echo.
echo   💰 ج. وحدة العملات:
echo   💰 c. Currency Module:
sqlcmd -S NAJEEB -d AwqafManagement -E -Q "SELECT COUNT(*) as CurrenciesCount FROM Currencies WHERE IsActive = 1" 2>nul

echo.
echo 🔍 6. اختبار البيانات التجريبية...
echo 🔍 6. Testing Sample Data...

sqlcmd -S NAJEEB -d AwqafManagement -E -Q "SELECT 'الأصول' as Category, COUNT(*) as Count FROM ChartOfAccounts WHERE AccountCode LIKE '1%' AND IsActive = 1 UNION ALL SELECT 'الخصوم' as Category, COUNT(*) as Count FROM ChartOfAccounts WHERE AccountCode LIKE '2%' AND IsActive = 1 UNION ALL SELECT 'حقوق الملكية' as Category, COUNT(*) as Count FROM ChartOfAccounts WHERE AccountCode LIKE '3%' AND IsActive = 1 UNION ALL SELECT 'الإيرادات' as Category, COUNT(*) as Count FROM ChartOfAccounts WHERE AccountCode LIKE '4%' AND IsActive = 1 UNION ALL SELECT 'المصروفات' as Category, COUNT(*) as Count FROM ChartOfAccounts WHERE AccountCode LIKE '5%' AND IsActive = 1"

echo.
echo ========================================
echo 📊 ملخص نتائج الاختبار
echo 📊 Test Results Summary
echo ========================================

echo ✅ قاعدة البيانات: متصلة
echo ✅ Database: Connected

echo ✅ التطبيق: يعمل
echo ✅ Application: Running

echo ✅ الدليل المحاسبي: جاهز
echo ✅ Chart of Accounts: Ready

echo ⚠️ القيود اليومية: قيد الاختبار
echo ⚠️ Journal Entries: Under Testing

echo.
echo 🎯 التوصيات للاختبار اليدوي:
echo 🎯 Manual Testing Recommendations:
echo.
echo 1. افتح شاشة الدليل المحاسبي واختبر:
echo    - إضافة حساب جديد
echo    - تعديل حساب موجود
echo    - حذف حساب
echo    - البحث في الحسابات
echo.
echo 2. افتح شاشة القيود اليومية واختبر:
echo    - إنشاء قيد جديد
echo    - إضافة تفاصيل القيد
echo    - حفظ القيد
echo    - البحث عن الحسابات
echo.
echo 3. اختبر الواجهة العربية:
echo    - التأكد من عرض النصوص العربية
echo    - اختبار اتجاه النص من اليمين لليسار
echo    - التأكد من عمل الخطوط العربية
echo.

echo ========================================
echo اضغط أي مفتاح للإغلاق...
echo Press any key to close...
pause > nul
