@echo off
echo ========================================
echo System Quick Test
echo ========================================
echo.

echo 1. Testing Database Connection...
sqlcmd -S NAJEEB -d AwqafManagement -E -Q "SELECT 1 as TestConnection" > nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Cannot connect to database
    pause
    exit /b 1
)
echo SUCCESS: Database connection OK

echo.
echo 2. Testing System Tables...
sqlcmd -S NAJEEB -d AwqafManagement -E -Q "SELECT COUNT(*) as AccountTypesCount FROM AccountTypes WHERE IsActive = 1"
sqlcmd -S NAJEEB -d AwqafManagement -E -Q "SELECT COUNT(*) as AccountGroupsCount FROM AccountGroups WHERE IsActive = 1"
sqlcmd -S NAJEEB -d AwqafManagement -E -Q "SELECT COUNT(*) as AccountsCount FROM ChartOfAccounts WHERE IsActive = 1"

echo.
echo 3. Testing Application File...
if exist "bin\Debug\Awqaf_Managment_Backup.exe" (
    echo SUCCESS: Application file exists
) else (
    echo ERROR: Application file not found
    pause
    exit /b 1
)

echo.
echo 4. Testing Application Launch...
start "" "bin\Debug\Awqaf_Managment_Backup.exe"
timeout /t 3 > nul

tasklist /FI "IMAGENAME eq Awqaf_Managment_Backup.exe" 2>NUL | find /I /N "Awqaf_Managment_Backup.exe" > nul
if %ERRORLEVEL% EQU 0 (
    echo SUCCESS: Application is running
) else (
    echo WARNING: Application may not be running
)

echo.
echo 5. Testing Chart of Accounts Data...
sqlcmd -S NAJEEB -d AwqafManagement -E -Q "SELECT TOP 5 AccountCode, AccountNameAr FROM ChartOfAccounts WHERE IsActive = 1 ORDER BY AccountCode"

echo.
echo 6. Testing Journal Entries Table...
sqlcmd -S NAJEEB -d AwqafManagement -E -Q "SELECT COUNT(*) as JournalEntriesCount FROM JournalEntries" 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo WARNING: Journal entries table not found or empty
) else (
    echo SUCCESS: Journal entries table exists
)

echo.
echo ========================================
echo Test Summary:
echo - Database: Connected
echo - Application: Running
echo - Chart of Accounts: Ready
echo - Journal Entries: Available
echo ========================================
echo.

echo Manual Testing Steps:
echo 1. Test Chart of Accounts screen
echo 2. Test Journal Entries screen  
echo 3. Test Account Lookup screen
echo 4. Verify Arabic text display
echo 5. Test add/edit/delete operations
echo.

echo Press any key to close...
pause > nul
