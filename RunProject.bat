@echo off
echo Building and running the main project...
echo.

echo Cleaning previous builds...
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"

echo.
echo Building project...
dotnet build Awqaf_Managment.csproj --configuration Debug --verbosity minimal

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Build successful. Running application...
    echo.
    dotnet run --project Awqaf_Managment.csproj --configuration Debug
) else (
    echo.
    echo Build failed with error code: %ERRORLEVEL%
    echo.
    echo Trying alternative build method...
    msbuild Awqaf_Managment.csproj /p:Configuration=Debug /p:Platform="Any CPU" /verbosity:minimal
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo Alternative build successful. Running...
        start "" "bin\Debug\net6.0-windows\Awqaf_Managment.exe"
    ) else (
        echo.
        echo All build methods failed.
    )
)

echo.
echo Press any key to continue...
pause > nul
