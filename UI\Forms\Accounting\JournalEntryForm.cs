using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Models.Accounting;
using Services.Accounting;
using UI.Helpers;

namespace UI.Forms.Accounting
{
    public partial class JournalEntryForm : Form
    {
        private List<JournalEntryDetail> _entryDetails;
        private List<ChartOfAccount> _accounts;
        private List<Currency> _currencies;
        private int _currentEntryId = 0;
        private bool _isEditMode = false;

        public JournalEntryForm()
        {
            InitializeComponent();
            InitializeData();
            SetupForm();
        }

        private void InitializeData()
        {
            try
            {
                _entryDetails = new List<JournalEntryDetail>();
                _accounts = ChartOfAccountsService.GetAllAccounts() ?? new List<ChartOfAccount>();
                _currencies = CurrencyService.GetAllCurrencies() ?? new List<Currency>();
            }
            catch (Exception ex)
            {
                UIHelper.ShowErrorMessage($"خطأ في تحميل البيانات: {ex.Message}");
                _entryDetails = new List<JournalEntryDetail>();
                _accounts = new List<ChartOfAccount>();
                _currencies = new List<Currency>();
            }
        }

        private void SetupForm()
        {
            try
            {
                // إعداد النموذج
                this.Text = "القيود اليومية";
                this.WindowState = FormWindowState.Maximized;
                this.RightToLeft = RightToLeft.Yes;
                this.RightToLeftLayout = true;

                // إعداد التاريخ الافتراضي
                if (dtpEntryDate != null)
                {
                    dtpEntryDate.Value = DateTime.Now;
                }

                // تحميل العملات
                LoadCurrencies();

                // إعداد الشبكة
                SetupDataGridView();

                // إعداد الأزرار
                SetupButtons();

                // إضافة صف جديد
                AddNewRow();
            }
            catch (Exception ex)
            {
                UIHelper.ShowErrorMessage($"خطأ في إعداد النموذج: {ex.Message}");
            }
        }

        private void LoadCurrencies()
        {
            try
            {
                if (cmbCurrency != null && _currencies != null)
                {
                    cmbCurrency.DataSource = _currencies;
                    cmbCurrency.DisplayMember = "CurrencyNameAr";
                    cmbCurrency.ValueMember = "CurrencyId";
                    
                    // اختيار العملة الأساسية
                    var baseCurrency = _currencies.FirstOrDefault(c => c.IsBaseCurrency);
                    if (baseCurrency != null)
                    {
                        cmbCurrency.SelectedValue = baseCurrency.CurrencyId;
                    }
                }
            }
            catch (Exception ex)
            {
                UIHelper.ShowErrorMessage($"خطأ في تحميل العملات: {ex.Message}");
            }
        }

        private void SetupDataGridView()
        {
            try
            {
                if (dgvEntryDetails == null) return;

                dgvEntryDetails.AutoGenerateColumns = false;
                dgvEntryDetails.AllowUserToAddRows = false;
                dgvEntryDetails.AllowUserToDeleteRows = true;
                dgvEntryDetails.MultiSelect = false;
                dgvEntryDetails.SelectionMode = DataGridViewSelectionMode.FullRowSelect;

                // إعداد الأعمدة
                dgvEntryDetails.Columns.Clear();

                // عمود رقم الحساب
                var accountColumn = new DataGridViewComboBoxColumn
                {
                    Name = "AccountId",
                    HeaderText = "رقم الحساب",
                    DataPropertyName = "AccountId",
                    Width = 200,
                    DataSource = _accounts,
                    DisplayMember = "AccountCode",
                    ValueMember = "AccountId"
                };
                dgvEntryDetails.Columns.Add(accountColumn);

                // عمود اسم الحساب
                var accountNameColumn = new DataGridViewTextBoxColumn
                {
                    Name = "AccountName",
                    HeaderText = "اسم الحساب",
                    DataPropertyName = "AccountName",
                    Width = 250,
                    ReadOnly = true
                };
                dgvEntryDetails.Columns.Add(accountNameColumn);

                // عمود البيان
                var descriptionColumn = new DataGridViewTextBoxColumn
                {
                    Name = "Description",
                    HeaderText = "البيان",
                    DataPropertyName = "Description",
                    Width = 200
                };
                dgvEntryDetails.Columns.Add(descriptionColumn);

                // عمود المدين
                var debitColumn = new DataGridViewTextBoxColumn
                {
                    Name = "DebitAmount",
                    HeaderText = "مدين",
                    DataPropertyName = "DebitAmount",
                    Width = 120,
                    DefaultCellStyle = new DataGridViewCellStyle
                    {
                        Format = "N2",
                        Alignment = DataGridViewContentAlignment.MiddleRight
                    }
                };
                dgvEntryDetails.Columns.Add(debitColumn);

                // عمود الدائن
                var creditColumn = new DataGridViewTextBoxColumn
                {
                    Name = "CreditAmount",
                    HeaderText = "دائن",
                    DataPropertyName = "CreditAmount",
                    Width = 120,
                    DefaultCellStyle = new DataGridViewCellStyle
                    {
                        Format = "N2",
                        Alignment = DataGridViewContentAlignment.MiddleRight
                    }
                };
                dgvEntryDetails.Columns.Add(creditColumn);

                // ربط البيانات
                dgvEntryDetails.DataSource = _entryDetails;

                // إعداد الأحداث
                dgvEntryDetails.CellValueChanged += DgvEntryDetails_CellValueChanged;
                dgvEntryDetails.CurrentCellDirtyStateChanged += DgvEntryDetails_CurrentCellDirtyStateChanged;
                dgvEntryDetails.UserDeletingRow += DgvEntryDetails_UserDeletingRow;
            }
            catch (Exception ex)
            {
                UIHelper.ShowErrorMessage($"خطأ في إعداد الشبكة: {ex.Message}");
            }
        }

        private void SetupButtons()
        {
            try
            {
                if (btnSave != null)
                {
                    UIHelper.StyleButton(btnSave, Color.FromArgb(40, 167, 69), Color.White);
                }

                if (btnNew != null)
                {
                    UIHelper.StyleButton(btnNew, Color.FromArgb(0, 123, 255), Color.White);
                }

                if (btnDelete != null)
                {
                    UIHelper.StyleButton(btnDelete, Color.FromArgb(220, 53, 69), Color.White);
                }

                if (btnClose != null)
                {
                    UIHelper.StyleButton(btnClose, Color.FromArgb(108, 117, 125), Color.White);
                }
            }
            catch (Exception ex)
            {
                UIHelper.ShowErrorMessage($"خطأ في إعداد الأزرار: {ex.Message}");
            }
        }

        private void AddNewRow()
        {
            try
            {
                var newDetail = new JournalEntryDetail
                {
                    JournalEntryDetailId = 0,
                    JournalEntryId = _currentEntryId,
                    AccountId = 0,
                    Description = "",
                    DebitAmount = 0,
                    CreditAmount = 0,
                    CreatedDate = DateTime.Now,
                    CreatedBy = "1"
                };

                _entryDetails.Add(newDetail);
                
                if (dgvEntryDetails != null)
                {
                    dgvEntryDetails.DataSource = null;
                    dgvEntryDetails.DataSource = _entryDetails;
                    dgvEntryDetails.Refresh();
                }
            }
            catch (Exception ex)
            {
                UIHelper.ShowErrorMessage($"خطأ في إضافة صف جديد: {ex.Message}");
            }
        }

        private void DgvEntryDetails_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (e.RowIndex < 0 || e.ColumnIndex < 0) return;

                var grid = sender as DataGridView;
                if (grid == null) return;

                // تحديث اسم الحساب عند تغيير رقم الحساب
                if (grid.Columns[e.ColumnIndex].Name == "AccountId")
                {
                    var accountId = grid.Rows[e.RowIndex].Cells["AccountId"].Value;
                    if (accountId != null && int.TryParse(accountId.ToString(), out int accId))
                    {
                        var account = _accounts.FirstOrDefault(a => a.AccountId == accId);
                        if (account != null)
                        {
                            grid.Rows[e.RowIndex].Cells["AccountName"].Value = account.AccountNameAr;
                        }
                    }
                }

                // حساب الإجماليات
                CalculateTotals();

                // إضافة صف جديد إذا كان هذا آخر صف
                if (e.RowIndex == grid.Rows.Count - 1)
                {
                    AddNewRow();
                }
            }
            catch (Exception ex)
            {
                UIHelper.ShowErrorMessage($"خطأ في تحديث الخلية: {ex.Message}");
            }
        }

        private void DgvEntryDetails_CurrentCellDirtyStateChanged(object sender, EventArgs e)
        {
            try
            {
                var grid = sender as DataGridView;
                if (grid?.IsCurrentCellDirty == true)
                {
                    grid.CommitEdit(DataGridViewDataErrorContexts.Commit);
                }
            }
            catch (Exception ex)
            {
                UIHelper.ShowErrorMessage($"خطأ في تحديث الخلية: {ex.Message}");
            }
        }

        private void DgvEntryDetails_UserDeletingRow(object sender, DataGridViewRowCancelEventArgs e)
        {
            try
            {
                if (_entryDetails.Count <= 1)
                {
                    e.Cancel = true;
                    UIHelper.ShowWarningMessage("يجب أن يحتوي القيد على صف واحد على الأقل");
                    return;
                }

                var result = UIHelper.ShowConfirmationMessage("هل تريد حذف هذا الصف؟");
                if (result != DialogResult.Yes)
                {
                    e.Cancel = true;
                }
            }
            catch (Exception ex)
            {
                UIHelper.ShowErrorMessage($"خطأ في حذف الصف: {ex.Message}");
                e.Cancel = true;
            }
        }

        private void CalculateTotals()
        {
            try
            {
                decimal totalDebit = 0;
                decimal totalCredit = 0;

                foreach (var detail in _entryDetails)
                {
                    totalDebit += detail.DebitAmount;
                    totalCredit += detail.CreditAmount;
                }

                if (lblTotalDebit != null)
                {
                    lblTotalDebit.Text = totalDebit.ToString("N2");
                }

                if (lblTotalCredit != null)
                {
                    lblTotalCredit.Text = totalCredit.ToString("N2");
                }

                if (lblDifference != null)
                {
                    var difference = totalDebit - totalCredit;
                    lblDifference.Text = difference.ToString("N2");
                    lblDifference.ForeColor = difference == 0 ? Color.Green : Color.Red;
                }
            }
            catch (Exception ex)
            {
                UIHelper.ShowErrorMessage($"خطأ في حساب الإجماليات: {ex.Message}");
            }
        }
    }
}
