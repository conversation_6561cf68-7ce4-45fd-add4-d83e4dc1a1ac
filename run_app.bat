@echo off
echo Starting Awqaf Management System...
echo.

REM Check if the executable exists
if not exist "bin\Debug\Awqaf_Managment.exe" (
    echo Error: Application not found!
    echo Please build the project first by running: build.bat
    echo.
    pause
    exit /b 1
)

echo Application found. Starting...
echo.

REM Start the application
start "" "bin\Debug\Awqaf_Managment.exe"

echo Application started successfully!
echo.
echo Default login credentials:
echo Username: admin
echo Password: admin123
echo.
echo If you encounter database connection issues:
echo 1. Make sure SQL Server is running
echo 2. Run setup_database.bat to create the database
echo.

timeout /t 3 /nobreak >nul
