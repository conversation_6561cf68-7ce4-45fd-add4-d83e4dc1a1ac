using System;
using System.Drawing;
using System.Windows.Forms;
using Awqaf_Managment.Common;
using Awqaf_Managment.Common.Helpers;

namespace Awqaf_Managment.UI.Forms
{
    public partial class DashboardForm : Form
    {
        public DashboardForm()
        {
            InitializeComponent();
            InitializeCustomComponents();
        }

        private void InitializeCustomComponents()
        {
            // تطبيق دعم RTL
            UIHelper.ApplyRTLSupport(this);
            
            // تطبيق الخط العربي
            UIHelper.ApplyArabicFont(this);
            
            // تخصيص الألوان
            this.BackColor = Constants.BackgroundColor;
            
            // إضافة محتوى الصفحة الرئيسية
            CreateDashboardContent();
        }

        private void CreateDashboardContent()
        {
            // إنشاء لوحة رئيسية
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(Constants.FormPadding),
                BackColor = Constants.BackgroundColor
            };

            // عنوان الترحيب
            var lblWelcome = new Label
            {
                Text = "مرحباً بك في نظام إدارة الأوقاف",
                Font = Constants.HeaderFont,
                ForeColor = Constants.PrimaryColor,
                AutoSize = true,
                Location = new Point(50, 50)
            };

            // وصف النظام
            var lblDescription = new Label
            {
                Text = "نظام شامل لإدارة العقارات والمحاسبة المالية",
                Font = Constants.DefaultFont,
                ForeColor = Constants.TextColor,
                AutoSize = true,
                Location = new Point(50, 100)
            };

            // إحصائيات سريعة
            var statsPanel = CreateStatsPanel();
            statsPanel.Location = new Point(50, 150);

            // إضافة العناصر للوحة الرئيسية
            mainPanel.Controls.Add(lblWelcome);
            mainPanel.Controls.Add(lblDescription);
            mainPanel.Controls.Add(statsPanel);

            // إضافة اللوحة الرئيسية للنموذج
            this.Controls.Add(mainPanel);
        }

        private Panel CreateStatsPanel()
        {
            var panel = new Panel
            {
                Size = new Size(800, 200),
                BackColor = Constants.WhiteColor
            };

            // بطاقة العقارات
            var propertyCard = CreateStatCard("العقارات", "25", Constants.SuccessColor, new Point(20, 20));
            
            // بطاقة العملاء
            var customerCard = CreateStatCard("العملاء", "150", Constants.PrimaryColor, new Point(220, 20));
            
            // بطاقة العقود
            var contractCard = CreateStatCard("العقود", "75", Constants.WarningColor, new Point(420, 20));
            
            // بطاقة الإيرادات
            var revenueCard = CreateStatCard("الإيرادات", "500,000 ر.س", Constants.ErrorColor, new Point(620, 20));

            panel.Controls.Add(propertyCard);
            panel.Controls.Add(customerCard);
            panel.Controls.Add(contractCard);
            panel.Controls.Add(revenueCard);

            return panel;
        }

        private Panel CreateStatCard(string title, string value, Color color, Point location)
        {
            var card = new Panel
            {
                Size = new Size(180, 150),
                BackColor = color,
                Location = location
            };

            var lblTitle = new Label
            {
                Text = title,
                Font = Constants.ButtonFont,
                ForeColor = Constants.WhiteColor,
                AutoSize = true,
                Location = new Point(10, 20)
            };

            var lblValue = new Label
            {
                Text = value,
                Font = Constants.HeaderFont,
                ForeColor = Constants.WhiteColor,
                AutoSize = true,
                Location = new Point(10, 60)
            };

            card.Controls.Add(lblTitle);
            card.Controls.Add(lblValue);

            return card;
        }
    }
}
