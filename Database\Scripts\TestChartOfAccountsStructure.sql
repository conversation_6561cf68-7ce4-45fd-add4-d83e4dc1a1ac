-- ===================================================================
-- اختبار بنية جداول الدليل المحاسبي
-- Test Chart of Accounts Table Structure
-- ===================================================================

USE AwqafManagement;
GO

PRINT N'=== اختبار بنية الجداول ===';
PRINT '=== Testing Table Structure ===';
PRINT '';

-- ===================================================================
-- اختبار جدول أنواع الحسابات
-- ===================================================================
PRINT N'1. اختبار جدول AccountTypes:';
PRINT '1. Testing AccountTypes table:';

IF OBJECT_ID('AccountTypes', 'U') IS NOT NULL
BEGIN
    PRINT N'✓ جدول AccountTypes موجود';
    
    -- عرض أعمدة الجدول
    SELECT 
        COLUMN_NAME as 'اسم العمود',
        DATA_TYPE as 'نوع البيانات',
        IS_NULLABLE as 'يقبل NULL',
        COLUMN_DEFAULT as 'القيمة الافتراضية'
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'AccountTypes'
    ORDER BY ORDINAL_POSITION;
END
ELSE
BEGIN
    PRINT N'❌ جدول AccountTypes غير موجود';
END

PRINT '';

-- ===================================================================
-- اختبار جدول مجموعات الحسابات
-- ===================================================================
PRINT N'2. اختبار جدول AccountGroups:';
PRINT '2. Testing AccountGroups table:';

IF OBJECT_ID('AccountGroups', 'U') IS NOT NULL
BEGIN
    PRINT N'✓ جدول AccountGroups موجود';
    
    -- عرض أعمدة الجدول
    SELECT 
        COLUMN_NAME as 'اسم العمود',
        DATA_TYPE as 'نوع البيانات',
        IS_NULLABLE as 'يقبل NULL',
        COLUMN_DEFAULT as 'القيمة الافتراضية'
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'AccountGroups'
    ORDER BY ORDINAL_POSITION;
END
ELSE
BEGIN
    PRINT N'❌ جدول AccountGroups غير موجود';
END

PRINT '';

-- ===================================================================
-- اختبار جدول دليل الحسابات
-- ===================================================================
PRINT N'3. اختبار جدول ChartOfAccounts:';
PRINT '3. Testing ChartOfAccounts table:';

IF OBJECT_ID('ChartOfAccounts', 'U') IS NOT NULL
BEGIN
    PRINT N'✓ جدول ChartOfAccounts موجود';
    
    -- عرض أعمدة الجدول
    SELECT 
        COLUMN_NAME as 'اسم العمود',
        DATA_TYPE as 'نوع البيانات',
        IS_NULLABLE as 'يقبل NULL',
        COLUMN_DEFAULT as 'القيمة الافتراضية'
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'ChartOfAccounts'
    ORDER BY ORDINAL_POSITION;
END
ELSE
BEGIN
    PRINT N'❌ جدول ChartOfAccounts غير موجود';
END

PRINT '';

-- ===================================================================
-- اختبار جدول العملات
-- ===================================================================
PRINT N'4. اختبار جدول Currencies:';
PRINT '4. Testing Currencies table:';

IF OBJECT_ID('Currencies', 'U') IS NOT NULL
BEGIN
    PRINT N'✓ جدول Currencies موجود';
    
    -- عرض أعمدة الجدول
    SELECT 
        COLUMN_NAME as 'اسم العمود',
        DATA_TYPE as 'نوع البيانات',
        IS_NULLABLE as 'يقبل NULL',
        COLUMN_DEFAULT as 'القيمة الافتراضية'
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'Currencies'
    ORDER BY ORDINAL_POSITION;
END
ELSE
BEGIN
    PRINT N'❌ جدول Currencies غير موجود';
END

PRINT '';

-- ===================================================================
-- اختبار المفاتيح الخارجية
-- ===================================================================
PRINT N'5. اختبار المفاتيح الخارجية:';
PRINT '5. Testing Foreign Keys:';

SELECT 
    FK.name AS 'اسم المفتاح الخارجي',
    TP.name AS 'الجدول الرئيسي',
    CP.name AS 'العمود الرئيسي',
    TR.name AS 'الجدول المرجعي',
    CR.name AS 'العمود المرجعي'
FROM sys.foreign_keys FK
INNER JOIN sys.tables TP ON FK.parent_object_id = TP.object_id
INNER JOIN sys.tables TR ON FK.referenced_object_id = TR.object_id
INNER JOIN sys.foreign_key_columns FKC ON FK.object_id = FKC.constraint_object_id
INNER JOIN sys.columns CP ON FKC.parent_column_id = CP.column_id AND FKC.parent_object_id = CP.object_id
INNER JOIN sys.columns CR ON FKC.referenced_column_id = CR.column_id AND FKC.referenced_object_id = CR.object_id
WHERE TP.name IN ('AccountTypes', 'AccountGroups', 'ChartOfAccounts')
ORDER BY TP.name, FK.name;

PRINT '';
PRINT N'=== انتهى اختبار البنية ===';
PRINT '=== Structure Test Complete ===';

-- ===================================================================
-- اختبار إدراج بيانات تجريبية
-- ===================================================================
PRINT '';
PRINT N'=== اختبار إدراج بيانات تجريبية ===';
PRINT '=== Testing Sample Data Insertion ===';

BEGIN TRY
    -- اختبار إدراج نوع حساب
    INSERT INTO AccountTypes (AccountTypeCode, AccountTypeName, AccountTypeNameAr, Description, DisplayOrder, IsActive, CreatedDate)
    VALUES ('TEST', 'Test Type', N'نوع تجريبي', N'نوع حساب للاختبار', 99, 1, GETDATE());
    
    DECLARE @TestTypeId INT = SCOPE_IDENTITY();
    PRINT N'✓ تم إدراج نوع حساب تجريبي بنجاح - ID: ' + CAST(@TestTypeId AS NVARCHAR(10));
    
    -- اختبار إدراج مجموعة حساب
    INSERT INTO AccountGroups (GroupCode, GroupName, GroupNameAr, AccountTypeId, Description, DisplayOrder, IsActive, CreatedDate)
    VALUES ('TST', 'Test Group', N'مجموعة تجريبية', @TestTypeId, N'مجموعة للاختبار', 99, 1, GETDATE());
    
    DECLARE @TestGroupId INT = SCOPE_IDENTITY();
    PRINT N'✓ تم إدراج مجموعة حساب تجريبية بنجاح - ID: ' + CAST(@TestGroupId AS NVARCHAR(10));
    
    -- اختبار إدراج حساب
    INSERT INTO ChartOfAccounts (AccountCode, AccountName, AccountNameAr, AccountTypeId, AccountGroupId, AccountLevel, IsParent, IsActive, AllowPosting, CurrencyCode, OpeningBalance, Description, CreatedDate)
    VALUES ('9.99.999', 'Test Account', N'حساب تجريبي', @TestTypeId, @TestGroupId, 1, 0, 1, 1, 'SAR', 0, N'حساب للاختبار', GETDATE());
    
    DECLARE @TestAccountId INT = SCOPE_IDENTITY();
    PRINT N'✓ تم إدراج حساب تجريبي بنجاح - ID: ' + CAST(@TestAccountId AS NVARCHAR(10));
    
    -- حذف البيانات التجريبية
    DELETE FROM ChartOfAccounts WHERE AccountId = @TestAccountId;
    DELETE FROM AccountGroups WHERE AccountGroupId = @TestGroupId;
    DELETE FROM AccountTypes WHERE AccountTypeId = @TestTypeId;
    
    PRINT N'✓ تم حذف البيانات التجريبية بنجاح';
    PRINT N'✓ جميع الجداول تعمل بشكل صحيح!';
    
END TRY
BEGIN CATCH
    PRINT N'❌ خطأ في اختبار البيانات:';
    PRINT ERROR_MESSAGE();
END CATCH

PRINT '';
PRINT N'=== انتهى الاختبار ===';
PRINT '=== Test Complete ===';
