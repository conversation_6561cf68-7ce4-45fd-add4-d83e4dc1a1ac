-- إصلاح حقول المستخدم في جدول العملات
-- Fix User Fields in Currencies Table
USE AwqafManagement
GO

PRINT 'بدء إصلاح حقول المستخدم في جدول العملات...'

-- التحقق من وجود الجدول
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Currencies')
BEGIN
    PRINT 'جدول العملات موجود'
    
    -- التحقق من نوع حقل CreatedBy
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Currencies]') AND name = 'CreatedBy' AND system_type_id = 56)
    BEGIN
        PRINT 'تحديث حقول المستخدم في جدول العملات...'
        
        -- إضا<PERSON>ة أعمدة مؤقتة
        ALTER TABLE Currencies ADD CreatedBy_New NVARCHAR(50) NULL
        ALTER TABLE Currencies ADD ModifiedBy_New NVARCHAR(50) NULL
        
        -- نسخ البيانات مع التحويل
        UPDATE Currencies SET CreatedBy_New = CAST(CreatedBy AS NVARCHAR(50))
        UPDATE Currencies SET ModifiedBy_New = CASE WHEN ModifiedBy IS NOT NULL THEN CAST(ModifiedBy AS NVARCHAR(50)) ELSE NULL END
        
        -- حذف الأعمدة القديمة
        ALTER TABLE Currencies DROP COLUMN CreatedBy
        ALTER TABLE Currencies DROP COLUMN ModifiedBy
        
        -- إعادة تسمية الأعمدة الجديدة
        EXEC sp_rename 'Currencies.CreatedBy_New', 'CreatedBy', 'COLUMN'
        EXEC sp_rename 'Currencies.ModifiedBy_New', 'ModifiedBy', 'COLUMN'
        
        -- تعيين القيود
        ALTER TABLE Currencies ALTER COLUMN CreatedBy NVARCHAR(50) NOT NULL
        
        PRINT 'تم تحديث جدول العملات بنجاح'
    END
    ELSE
    BEGIN
        PRINT 'جدول العملات محدث مسبقاً'
    END
END
ELSE
BEGIN
    PRINT 'جدول العملات غير موجود'
END

PRINT 'تم الانتهاء من إصلاح حقول المستخدم في جدول العملات!'
GO
