using System;

namespace Awqaf_Managment.UI.Helpers
{
    /// <summary>
    /// فئة مساعدة لعناصر ComboBox
    /// </summary>
    public class ComboBoxItem
    {
        /// <summary>
        /// النص المعروض
        /// </summary>
        public string Text { get; set; }
        
        /// <summary>
        /// القيمة المخزنة
        /// </summary>
        public object Value { get; set; }
        
        /// <summary>
        /// إرجاع النص المعروض
        /// </summary>
        /// <returns></returns>
        public override string ToString()
        {
            return Text ?? string.Empty;
        }
        
        /// <summary>
        /// مقارنة العناصر
        /// </summary>
        /// <param name="obj"></param>
        /// <returns></returns>
        public override bool Equals(object obj)
        {
            if (obj is ComboBoxItem other)
            {
                return Equals(Value, other.Value);
            }
            return false;
        }
        
        /// <summary>
        /// الحصول على hash code
        /// </summary>
        /// <returns></returns>
        public override int GetHashCode()
        {
            return Value?.GetHashCode() ?? 0;
        }
    }
}
