using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Awqaf_Managment.Common;
using Awqaf_Managment.Common.Helpers;
using Awqaf_Managment.DataAccess.Security;
using Awqaf_Managment.Models.Security;
using Awqaf_Managment.Services.Security;

namespace Awqaf_Managment.UI.Forms.Security
{
    public partial class UserManagementForm : Form
    {
        private List<User> users;
        private List<User> filteredUsers;

        public UserManagementForm()
        {
            InitializeComponent();
            InitializeCustomComponents();
            LoadUsers();
        }

        private void InitializeCustomComponents()
        {
            // تطبيق دعم RTL
            UIHelper.ApplyRTLSupport(this);

            // تخصيص DataGridView
            SetupDataGridView();
            
            // إعداد أحداث الأزرار
            btnAdd.Click += BtnAdd_Click;
            btnEdit.Click += BtnEdit_Click;
            btnDelete.Click += BtnDelete_Click;
            btnRefresh.Click += BtnRefresh_Click;
            
            // إعداد البحث
            txtSearch.TextChanged += TxtSearch_TextChanged;
            
            // إعداد أحداث DataGridView
            dgvUsers.SelectionChanged += DgvUsers_SelectionChanged;
            dgvUsers.CellDoubleClick += DgvUsers_CellDoubleClick;
            
            // تطبيق الصلاحيات
            ApplyPermissions();
        }

        private void SetupDataGridView()
        {
            // إعداد خصائص DataGridView
            dgvUsers.AutoGenerateColumns = false;
            dgvUsers.AllowUserToAddRows = false;
            dgvUsers.AllowUserToDeleteRows = false;
            dgvUsers.ReadOnly = true;
            dgvUsers.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgvUsers.MultiSelect = false;
            
            // تخصيص الألوان
            dgvUsers.BackgroundColor = Color.White;
            dgvUsers.GridColor = Color.FromArgb(224, 224, 224);
            dgvUsers.DefaultCellStyle.BackColor = Color.White;
            dgvUsers.DefaultCellStyle.ForeColor = Color.Black;
            dgvUsers.DefaultCellStyle.SelectionBackColor = Constants.PrimaryColor;
            dgvUsers.DefaultCellStyle.SelectionForeColor = Color.White;
            dgvUsers.ColumnHeadersDefaultCellStyle.BackColor = Constants.PrimaryColor;
            dgvUsers.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvUsers.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 12, FontStyle.Bold);
            
            // إضافة الأعمدة
            AddDataGridViewColumns();
        }

        private void AddDataGridViewColumns()
        {
            dgvUsers.Columns.Clear();
            
            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "UserId",
                HeaderText = "المعرف",
                DataPropertyName = "UserId",
                Width = 80,
                Visible = false
            });
            
            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Username",
                HeaderText = "اسم المستخدم",
                DataPropertyName = "Username",
                Width = 150
            });
            
            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "FullName",
                HeaderText = "الاسم الكامل",
                DataPropertyName = "FullName",
                Width = 200
            });
            
            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Email",
                HeaderText = "البريد الإلكتروني",
                DataPropertyName = "Email",
                Width = 200
            });
            
            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "Phone",
                HeaderText = "الهاتف",
                DataPropertyName = "Phone",
                Width = 120
            });
            
            dgvUsers.Columns.Add(new DataGridViewCheckBoxColumn
            {
                Name = "IsActive",
                HeaderText = "نشط",
                DataPropertyName = "IsActive",
                Width = 80
            });
            
            dgvUsers.Columns.Add(new DataGridViewCheckBoxColumn
            {
                Name = "IsLocked",
                HeaderText = "مقفل",
                DataPropertyName = "IsLocked",
                Width = 80
            });
            
            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "LastLoginDate",
                HeaderText = "آخر دخول",
                DataPropertyName = "LastLoginDate",
                Width = 150,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy/MM/dd HH:mm" }
            });
            
            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "CreatedDate",
                HeaderText = "تاريخ الإنشاء",
                DataPropertyName = "CreatedDate",
                Width = 150,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "yyyy/MM/dd" }
            });

            dgvUsers.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "RolesDisplay",
                HeaderText = "الأدوار",
                DataPropertyName = "RolesDisplay",
                Width = 200
            });
        }

        private void LoadUsers()
        {
            try
            {
                users = UserDataAccess.GetAllUsers();
                filteredUsers = users.ToList();
                RefreshDataGridView();
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                UIHelper.ShowError($"خطأ في تحميل المستخدمين: {ex.Message}");
            }
        }

        private void RefreshDataGridView()
        {
            dgvUsers.DataSource = null;
            dgvUsers.DataSource = filteredUsers;
            dgvUsers.Refresh();
        }

        private void ApplyPermissions()
        {
            // التحقق من صلاحيات إدارة المستخدمين
            bool canManage = AuthenticationService.HasPermission("UserManagement", PermissionAction.View);
            bool canAdd = AuthenticationService.HasPermission("UserManagement", PermissionAction.Add);
            bool canEdit = AuthenticationService.HasPermission("UserManagement", PermissionAction.Edit);
            bool canDelete = AuthenticationService.HasPermission("UserManagement", PermissionAction.Delete);
            
            if (!canManage)
            {
                UIHelper.ShowError("ليس لديك صلاحية للوصول إلى إدارة المستخدمين");
                this.Close();
                return;
            }
            
            btnAdd.Visible = canAdd;
            btnEdit.Visible = canEdit;
            btnDelete.Visible = canDelete;
        }

        private void UpdateButtonStates()
        {
            bool hasSelection = dgvUsers.SelectedRows.Count > 0;
            btnEdit.Enabled = hasSelection;
            btnDelete.Enabled = hasSelection;
        }

        private void BtnAdd_Click(object sender, EventArgs e)
        {
            try
            {
                var addForm = new AddEditUserForm();
                if (addForm.ShowDialog() == DialogResult.OK)
                {
                    LoadUsers();
                    UIHelper.ShowSuccess("تم إضافة المستخدم بنجاح");
                }
            }
            catch (Exception ex)
            {
                UIHelper.ShowError($"خطأ في إضافة المستخدم: {ex.Message}");
            }
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            if (dgvUsers.SelectedRows.Count > 0)
            {
                try
                {
                    var selectedUser = dgvUsers.SelectedRows[0].DataBoundItem as User;
                    if (selectedUser != null)
                    {
                        var editForm = new AddEditUserForm(selectedUser);
                        if (editForm.ShowDialog() == DialogResult.OK)
                        {
                            LoadUsers();
                            UIHelper.ShowSuccess("تم تحديث بيانات المستخدم بنجاح");
                        }
                    }
                }
                catch (Exception ex)
                {
                    UIHelper.ShowError($"خطأ في تعديل المستخدم: {ex.Message}");
                }
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            if (dgvUsers.SelectedRows.Count > 0)
            {
                var selectedUser = dgvUsers.SelectedRows[0].DataBoundItem as User;
                if (selectedUser != null)
                {
                    if (UIHelper.ShowConfirm($"هل تريد حذف المستخدم '{selectedUser.FullName}'؟") == DialogResult.Yes)
                    {
                        // سيتم إضافة منطق الحذف لاحقاً
                        UIHelper.ShowInfo("سيتم إضافة منطق حذف المستخدم قريباً");
                    }
                }
            }
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadUsers();
        }

        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            string searchText = txtSearch.Text.Trim().ToLower();
            
            if (string.IsNullOrEmpty(searchText))
            {
                filteredUsers = users.ToList();
            }
            else
            {
                filteredUsers = users.Where(u =>
                    u.Username.ToLower().Contains(searchText) ||
                    u.FullName.ToLower().Contains(searchText) ||
                    (u.Email != null && u.Email.ToLower().Contains(searchText))
                ).ToList();
            }
            
            RefreshDataGridView();
        }

        private void DgvUsers_SelectionChanged(object sender, EventArgs e)
        {
            UpdateButtonStates();
        }

        private void DgvUsers_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                // عرض تفاصيل المستخدم عند النقر المزدوج
                ShowUserDetails();
            }
        }

        /// <summary>
        /// عرض تفاصيل المستخدم المحدد
        /// </summary>
        private void ShowUserDetails()
        {
            if (dgvUsers.SelectedRows.Count == 0)
            {
                UIHelper.ShowWarning("يرجى اختيار مستخدم لعرض التفاصيل");
                return;
            }

            try
            {
                var selectedUser = dgvUsers.SelectedRows[0].DataBoundItem as User;
                if (selectedUser != null)
                {
                    var detailsForm = new UserDetailsForm(selectedUser);
                    detailsForm.ShowDialog();
                }
                else
                {
                    UIHelper.ShowError("لم يتم العثور على المستخدم المحدد");
                }
            }
            catch (Exception ex)
            {
                UIHelper.ShowError($"خطأ في عرض تفاصيل المستخدم: {ex.Message}");
            }
        }
    }
}
