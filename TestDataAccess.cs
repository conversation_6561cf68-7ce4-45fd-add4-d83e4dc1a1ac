using System;
using System.Collections.Generic;
using DataAccess.Accounting;
using Models.Accounting;

namespace Awqaf_Managment
{
    public class TestDataAccess
    {
        public static void TestChartOfAccountsDataAccess()
        {
            try
            {
                Console.WriteLine("=== اختبار تحميل الحسابات ===");
                
                var accounts = ChartOfAccountsDataAccess.GetAllAccounts();
                
                if (accounts == null)
                {
                    Console.WriteLine("خطأ: accounts is null");
                    return;
                }
                
                Console.WriteLine($"تم تحميل {accounts.Count} حساب");
                
                foreach (var account in accounts.Take(5))
                {
                    Console.WriteLine($"الحساب: {account.AccountCode} - {account.AccountNameAr}");
                    Console.WriteLine($"  نوع الحساب: {account.AccountTypeId}");
                    Console.WriteLine($"  الحساب الأب: {account.ParentAccountId}");
                    Console.WriteLine($"  نشط: {account.IsActive}");
                    Console.WriteLine("---");
                }
                
                // اختبار الحسابات الجذرية
                var rootAccounts = accounts.Where(a => a.ParentAccountId == null || a.ParentAccountId == 0);
                Console.WriteLine($"عدد الحسابات الجذرية: {rootAccounts.Count()}");
                
                foreach (var rootAccount in rootAccounts)
                {
                    Console.WriteLine($"حساب جذري: {rootAccount.AccountCode} - {rootAccount.AccountNameAr}");
                }
                
                Console.WriteLine("=== انتهى الاختبار ===");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في الاختبار: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }
    }
}
