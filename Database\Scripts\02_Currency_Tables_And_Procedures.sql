-- =============================================
-- إدارة العملات - Currency Management
-- تاريخ الإنشاء: 2025-01-01
-- الوصف: جداول والإجراءات المخزنة لإدارة العملات
-- =============================================

USE [AwqafManagement]
GO

-- =============================================
-- إنشاء جدول العملات
-- Create Currencies Table
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[Currencies]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[Currencies](
        [CurrencyId] [int] IDENTITY(1,1) NOT NULL,
        [CurrencyCode] [nvarchar](3) NOT NULL,
        [CurrencyNameAr] [nvarchar](100) NOT NULL,
        [CurrencyNameEn] [nvarchar](100) NULL,
        [Symbol] [nvarchar](5) NOT NULL,
        [ExchangeRate] [decimal](18, 6) NOT NULL DEFAULT(1.000000),
        [DecimalPlaces] [int] NOT NULL DEFAULT(2),
        [IsBaseCurrency] [bit] NOT NULL DEFAULT(0),
        [IsActive] [bit] NOT NULL DEFAULT(1),
        [CreatedDate] [datetime2](7) NOT NULL DEFAULT(GETDATE()),
        [ModifiedDate] [datetime2](7) NULL,
        [CreatedBy] [int] NOT NULL DEFAULT(1),
        [ModifiedBy] [int] NULL,
        
        CONSTRAINT [PK_Currencies] PRIMARY KEY CLUSTERED ([CurrencyId] ASC),
        CONSTRAINT [UK_Currencies_Code] UNIQUE NONCLUSTERED ([CurrencyCode] ASC),
        CONSTRAINT [CK_Currencies_ExchangeRate] CHECK ([ExchangeRate] > 0),
        CONSTRAINT [CK_Currencies_DecimalPlaces] CHECK ([DecimalPlaces] >= 0 AND [DecimalPlaces] <= 6)
    )
    
    PRINT 'تم إنشاء جدول العملات بنجاح'
END
ELSE
BEGIN
    PRINT 'جدول العملات موجود مسبقاً'
END
GO

-- =============================================
-- إنشاء فهارس للأداء
-- Create Performance Indexes
-- =============================================
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[Currencies]') AND name = N'IX_Currencies_IsActive')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Currencies_IsActive] ON [dbo].[Currencies] ([IsActive] ASC)
    PRINT 'تم إنشاء فهرس IsActive'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[Currencies]') AND name = N'IX_Currencies_IsBaseCurrency')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_Currencies_IsBaseCurrency] ON [dbo].[Currencies] ([IsBaseCurrency] ASC)
    PRINT 'تم إنشاء فهرس IsBaseCurrency'
END
GO

-- =============================================
-- إدراج البيانات الأساسية للعملات
-- Insert Default Currency Data
-- =============================================
IF NOT EXISTS (SELECT * FROM [dbo].[Currencies])
BEGIN
    INSERT INTO [dbo].[Currencies] 
    ([CurrencyCode], [CurrencyNameAr], [CurrencyNameEn], [Symbol], [ExchangeRate], [DecimalPlaces], [IsBaseCurrency], [IsActive], [CreatedBy])
    VALUES 
    ('SAR', 'ريال سعودي', 'Saudi Riyal', '﷼', 1.000000, 2, 1, 1, 1),
    ('USD', 'دولار أمريكي', 'US Dollar', '$', 3.750000, 2, 0, 1, 1),
    ('EUR', 'يورو', 'Euro', '€', 4.100000, 2, 0, 1, 1),
    ('GBP', 'جنيه إسترليني', 'British Pound', '£', 4.650000, 2, 0, 1, 1),
    ('AED', 'درهم إماراتي', 'UAE Dirham', 'د.إ', 1.020000, 2, 0, 1, 1),
    ('KWD', 'دينار كويتي', 'Kuwaiti Dinar', 'د.ك', 12.250000, 3, 0, 1, 1),
    ('QAR', 'ريال قطري', 'Qatari Riyal', 'ر.ق', 1.030000, 2, 0, 1, 1),
    ('BHD', 'دينار بحريني', 'Bahraini Dinar', 'د.ب', 9.950000, 3, 0, 1, 1),
    ('OMR', 'ريال عماني', 'Omani Rial', 'ر.ع', 9.750000, 3, 0, 1, 1),
    ('JOD', 'دينار أردني', 'Jordanian Dinar', 'د.أ', 5.290000, 3, 0, 1, 1)
    
    PRINT 'تم إدراج البيانات الأساسية للعملات'
END
ELSE
BEGIN
    PRINT 'البيانات الأساسية للعملات موجودة مسبقاً'
END
GO

-- =============================================
-- الإجراءات المخزنة لإدارة العملات
-- Stored Procedures for Currency Management
-- =============================================

-- إجراء الحصول على جميع العملات
-- Get All Currencies Procedure
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SP_GetAllCurrencies]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[SP_GetAllCurrencies]
GO

CREATE PROCEDURE [dbo].[SP_GetAllCurrencies]
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        CurrencyId,
        CurrencyCode,
        CurrencyNameAr,
        CurrencyNameEn,
        Symbol,
        ExchangeRate,
        DecimalPlaces,
        IsBaseCurrency,
        IsActive,
        CreatedDate,
        ModifiedDate,
        CreatedBy,
        ModifiedBy
    FROM [dbo].[Currencies]
    ORDER BY IsBaseCurrency DESC, CurrencyNameAr ASC
END
GO

-- إجراء الحصول على العملات النشطة
-- Get Active Currencies Procedure
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SP_GetActiveCurrencies]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[SP_GetActiveCurrencies]
GO

CREATE PROCEDURE [dbo].[SP_GetActiveCurrencies]
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        CurrencyId,
        CurrencyCode,
        CurrencyNameAr,
        CurrencyNameEn,
        Symbol,
        ExchangeRate,
        DecimalPlaces,
        IsBaseCurrency,
        IsActive,
        CreatedDate,
        ModifiedDate,
        CreatedBy,
        ModifiedBy
    FROM [dbo].[Currencies]
    WHERE IsActive = 1
    ORDER BY IsBaseCurrency DESC, CurrencyNameAr ASC
END
GO

-- إجراء الحصول على عملة بالمعرف
-- Get Currency By ID Procedure
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SP_GetCurrencyById]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[SP_GetCurrencyById]
GO

CREATE PROCEDURE [dbo].[SP_GetCurrencyById]
    @CurrencyId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        CurrencyId,
        CurrencyCode,
        CurrencyNameAr,
        CurrencyNameEn,
        Symbol,
        ExchangeRate,
        DecimalPlaces,
        IsBaseCurrency,
        IsActive,
        CreatedDate,
        ModifiedDate,
        CreatedBy,
        ModifiedBy
    FROM [dbo].[Currencies]
    WHERE CurrencyId = @CurrencyId
END
GO

-- إجراء الحصول على العملة الأساسية
-- Get Base Currency Procedure
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SP_GetBaseCurrency]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[SP_GetBaseCurrency]
GO

CREATE PROCEDURE [dbo].[SP_GetBaseCurrency]
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT TOP 1
        CurrencyId,
        CurrencyCode,
        CurrencyNameAr,
        CurrencyNameEn,
        Symbol,
        ExchangeRate,
        DecimalPlaces,
        IsBaseCurrency,
        IsActive,
        CreatedDate,
        ModifiedDate,
        CreatedBy,
        ModifiedBy
    FROM [dbo].[Currencies]
    WHERE IsBaseCurrency = 1 AND IsActive = 1
END
GO

-- إجراء البحث عن عملة برمز العملة
-- Get Currency By Code Procedure
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SP_GetCurrencyByCode]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[SP_GetCurrencyByCode]
GO

CREATE PROCEDURE [dbo].[SP_GetCurrencyByCode]
    @CurrencyCode NVARCHAR(3)
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        CurrencyId,
        CurrencyCode,
        CurrencyNameAr,
        CurrencyNameEn,
        Symbol,
        ExchangeRate,
        DecimalPlaces,
        IsBaseCurrency,
        IsActive,
        CreatedDate,
        ModifiedDate,
        CreatedBy,
        ModifiedBy
    FROM [dbo].[Currencies]
    WHERE CurrencyCode = @CurrencyCode
END
GO

-- إجراء إضافة عملة جديدة
-- Add Currency Procedure
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SP_AddCurrency]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[SP_AddCurrency]
GO

CREATE PROCEDURE [dbo].[SP_AddCurrency]
    @CurrencyCode NVARCHAR(3),
    @CurrencyNameAr NVARCHAR(100),
    @CurrencyNameEn NVARCHAR(100) = NULL,
    @Symbol NVARCHAR(5),
    @ExchangeRate DECIMAL(18,6),
    @DecimalPlaces INT,
    @IsBaseCurrency BIT,
    @IsActive BIT,
    @CreatedBy INT,
    @ModifiedBy INT = NULL
AS
BEGIN
    SET NOCOUNT ON;

    BEGIN TRY
        BEGIN TRANSACTION

        -- التحقق من عدم وجود رمز العملة مسبقاً
        IF EXISTS (SELECT 1 FROM [dbo].[Currencies] WHERE CurrencyCode = @CurrencyCode)
        BEGIN
            RAISERROR('رمز العملة موجود مسبقاً', 16, 1)
            RETURN -1
        END

        -- إذا كانت العملة أساسية، تحديث العملات الأخرى لتكون غير أساسية
        IF @IsBaseCurrency = 1
        BEGIN
            UPDATE [dbo].[Currencies]
            SET IsBaseCurrency = 0, ModifiedDate = GETDATE(), ModifiedBy = @CreatedBy
            WHERE IsBaseCurrency = 1
        END

        -- إدراج العملة الجديدة
        INSERT INTO [dbo].[Currencies]
        (CurrencyCode, CurrencyNameAr, CurrencyNameEn, Symbol, ExchangeRate,
         DecimalPlaces, IsBaseCurrency, IsActive, CreatedBy, ModifiedBy)
        VALUES
        (@CurrencyCode, @CurrencyNameAr, @CurrencyNameEn, @Symbol, @ExchangeRate,
         @DecimalPlaces, @IsBaseCurrency, @IsActive, @CreatedBy, @ModifiedBy)

        DECLARE @NewCurrencyId INT = SCOPE_IDENTITY()

        COMMIT TRANSACTION

        SELECT @NewCurrencyId AS CurrencyId

    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION
        THROW
    END CATCH
END
GO

-- إجراء تحديث العملة
-- Update Currency Procedure
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SP_UpdateCurrency]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[SP_UpdateCurrency]
GO

CREATE PROCEDURE [dbo].[SP_UpdateCurrency]
    @CurrencyId INT,
    @CurrencyCode NVARCHAR(3),
    @CurrencyNameAr NVARCHAR(100),
    @CurrencyNameEn NVARCHAR(100) = NULL,
    @Symbol NVARCHAR(5),
    @ExchangeRate DECIMAL(18,6),
    @DecimalPlaces INT,
    @IsBaseCurrency BIT,
    @IsActive BIT,
    @CreatedBy INT,
    @ModifiedBy INT = NULL
AS
BEGIN
    SET NOCOUNT ON;

    BEGIN TRY
        BEGIN TRANSACTION

        -- التحقق من وجود العملة
        IF NOT EXISTS (SELECT 1 FROM [dbo].[Currencies] WHERE CurrencyId = @CurrencyId)
        BEGIN
            RAISERROR('العملة غير موجودة', 16, 1)
            RETURN -1
        END

        -- التحقق من عدم وجود رمز العملة لعملة أخرى
        IF EXISTS (SELECT 1 FROM [dbo].[Currencies] WHERE CurrencyCode = @CurrencyCode AND CurrencyId != @CurrencyId)
        BEGIN
            RAISERROR('رمز العملة موجود لعملة أخرى', 16, 1)
            RETURN -1
        END

        -- إذا كانت العملة أساسية، تحديث العملات الأخرى لتكون غير أساسية
        IF @IsBaseCurrency = 1
        BEGIN
            UPDATE [dbo].[Currencies]
            SET IsBaseCurrency = 0, ModifiedDate = GETDATE(), ModifiedBy = ISNULL(@ModifiedBy, @CreatedBy)
            WHERE IsBaseCurrency = 1 AND CurrencyId != @CurrencyId
        END

        -- تحديث العملة
        UPDATE [dbo].[Currencies]
        SET
            CurrencyCode = @CurrencyCode,
            CurrencyNameAr = @CurrencyNameAr,
            CurrencyNameEn = @CurrencyNameEn,
            Symbol = @Symbol,
            ExchangeRate = @ExchangeRate,
            DecimalPlaces = @DecimalPlaces,
            IsBaseCurrency = @IsBaseCurrency,
            IsActive = @IsActive,
            ModifiedDate = GETDATE(),
            ModifiedBy = ISNULL(@ModifiedBy, @CreatedBy)
        WHERE CurrencyId = @CurrencyId

        COMMIT TRANSACTION

        SELECT @@ROWCOUNT AS RowsAffected

    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION
        THROW
    END CATCH
END
GO

-- إجراء حذف العملة
-- Delete Currency Procedure
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[SP_DeleteCurrency]') AND type in (N'P', N'PC'))
    DROP PROCEDURE [dbo].[SP_DeleteCurrency]
GO

CREATE PROCEDURE [dbo].[SP_DeleteCurrency]
    @CurrencyId INT
AS
BEGIN
    SET NOCOUNT ON;

    BEGIN TRY
        BEGIN TRANSACTION

        -- التحقق من وجود العملة
        IF NOT EXISTS (SELECT 1 FROM [dbo].[Currencies] WHERE CurrencyId = @CurrencyId)
        BEGIN
            RAISERROR('العملة غير موجودة', 16, 1)
            RETURN -1
        END

        -- التحقق من أن العملة ليست أساسية
        IF EXISTS (SELECT 1 FROM [dbo].[Currencies] WHERE CurrencyId = @CurrencyId AND IsBaseCurrency = 1)
        BEGIN
            RAISERROR('لا يمكن حذف العملة الأساسية', 16, 1)
            RETURN -1
        END

        -- TODO: التحقق من عدم استخدام العملة في معاملات أخرى
        -- Check if currency is used in other transactions

        -- حذف العملة
        DELETE FROM [dbo].[Currencies]
        WHERE CurrencyId = @CurrencyId

        COMMIT TRANSACTION

        SELECT @@ROWCOUNT AS RowsAffected

    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION
        THROW
    END CATCH
END
GO

PRINT 'تم إنشاء جميع الإجراءات المخزنة للعملات بنجاح'
GO
