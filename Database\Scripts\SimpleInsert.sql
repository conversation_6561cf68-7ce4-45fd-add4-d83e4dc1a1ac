-- ===================================================================
-- إدراج بسيط للبيانات العربية
-- Simple Arabic Data Insert
-- ===================================================================

USE AwqafManagement;
GO

PRINT N'=== إدراج البيانات العربية ===';

-- حذف البيانات الموجودة
DELETE FROM ChartOfAccounts;

-- إدراج الحسابات الرئيسية
INSERT INTO ChartOfAccounts (
    AccountCode, AccountName, AccountNameAr, AccountTypeId, AccountGroupId, 
    ParentAccountId, AccountLevel, IsParent, IsActive, AllowPosting, 
    CurrencyCode, OpeningBalance, Description, CreatedDate
) VALUES 
(N'1.00.000', N'Assets', N'الأصول', 1003, 1008, NULL, 1, 1, 1, 0, N'SAR', 0, N'جميع أصول المؤسسة', GETDATE()),
(N'2.00.000', N'Liabilities', N'الخصوم', 1004, 1010, NULL, 1, 1, 1, 0, N'SAR', 0, N'جميع التزامات المؤسسة', GETDATE()),
(N'3.00.000', N'Equity', N'حقوق الملكية', 1005, 1011, NULL, 1, 1, 1, 0, N'SAR', 0, N'حقوق أصحاب المؤسسة', GETDATE()),
(N'4.00.000', N'Revenue', N'الإيرادات', 1006, 1012, NULL, 1, 1, 1, 0, N'SAR', 0, N'جميع إيرادات المؤسسة', GETDATE()),
(N'5.00.000', N'Expenses', N'المصروفات', 1007, 1013, NULL, 1, 1, 1, 0, N'SAR', 0, N'جميع مصروفات المؤسسة', GETDATE());

PRINT N'✓ تم إدراج الحسابات الرئيسية';

-- إدراج الحسابات الفرعية
INSERT INTO ChartOfAccounts (
    AccountCode, AccountName, AccountNameAr, AccountTypeId, AccountGroupId, 
    ParentAccountId, AccountLevel, IsParent, IsActive, AllowPosting, 
    CurrencyCode, OpeningBalance, Description, CreatedDate
) VALUES 
(N'1.01.000', N'Current Assets', N'الأصول المتداولة', 1003, 1008,
    (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = N'1.00.000'), 2, 1, 1, 0, N'SAR', 0, N'الأصول قصيرة الأجل', GETDATE()),
(N'1.02.000', N'Fixed Assets', N'الأصول الثابتة', 1003, 1009,
    (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = N'1.00.000'), 2, 1, 1, 0, N'SAR', 0, N'الأصول طويلة الأجل', GETDATE()),
(N'2.01.000', N'Current Liabilities', N'الخصوم المتداولة', 1004, 1010,
    (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = N'2.00.000'), 2, 1, 1, 0, N'SAR', 0, N'الالتزامات قصيرة الأجل', GETDATE());

PRINT N'✓ تم إدراج الحسابات الفرعية';

-- إدراج الحسابات التفصيلية
INSERT INTO ChartOfAccounts (
    AccountCode, AccountName, AccountNameAr, AccountTypeId, AccountGroupId, 
    ParentAccountId, AccountLevel, IsParent, IsActive, AllowPosting, 
    CurrencyCode, OpeningBalance, Description, CreatedDate
) VALUES
(N'1.01.001', N'Cash in Hand', N'النقدية في الصندوق', 1003, 1008,
    (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = N'1.01.000'), 3, 0, 1, 1, N'SAR', 50000, N'النقد المتوفر في الصندوق', GETDATE()),
(N'1.01.002', N'Bank - Current Account', N'البنك - الحساب الجاري', 1003, 1008,
    (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = N'1.01.000'), 3, 0, 1, 1, N'SAR', 250000, N'الحساب الجاري في البنك', GETDATE()),
(N'1.01.003', N'Accounts Receivable', N'العملاء والمدينون', 1003, 1008,
    (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = N'1.01.000'), 3, 0, 1, 1, N'SAR', 75000, N'المبالغ المستحقة من العملاء', GETDATE()),
(N'1.02.001', N'Buildings', N'المباني', 1003, 1009,
    (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = N'1.02.000'), 3, 0, 1, 1, N'SAR', 2000000, N'المباني والعقارات', GETDATE()),
(N'1.02.002', N'Equipment', N'المعدات', 1003, 1009,
    (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = N'1.02.000'), 3, 0, 1, 1, N'SAR', 150000, N'المعدات والأجهزة', GETDATE()),
(N'2.01.001', N'Accounts Payable', N'الموردون والدائنون', 1004, 1010,
    (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = N'2.01.000'), 3, 0, 1, 1, N'SAR', 45000, N'المبالغ المستحقة للموردين', GETDATE()),
(N'2.01.002', N'Accrued Expenses', N'المصروفات المستحقة', 1004, 1010,
    (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = N'2.01.000'), 3, 0, 1, 1, N'SAR', 15000, N'المصروفات المستحقة الدفع', GETDATE()),
(N'3.01.001', N'Paid-up Capital', N'رأس المال المدفوع', 1005, 1011,
    (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = N'3.00.000'), 2, 0, 1, 1, N'SAR', 1000000, N'رأس المال المدفوع فعلياً', GETDATE()),
(N'3.02.001', N'Retained Earnings', N'الأرباح المحتجزة', 1005, 1011,
    (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = N'3.00.000'), 2, 0, 1, 1, N'SAR', 500000, N'الأرباح المحتجزة من السنوات السابقة', GETDATE()),
(N'4.01.001', N'Sales Revenue', N'إيرادات المبيعات', 1006, 1012,
    (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = N'4.00.000'), 2, 0, 1, 1, N'SAR', 0, N'إيرادات من بيع البضائع والخدمات', GETDATE()),
(N'4.02.001', N'Other Income', N'إيرادات أخرى', 1006, 1012,
    (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = N'4.00.000'), 2, 0, 1, 1, N'SAR', 0, N'الإيرادات الأخرى المتنوعة', GETDATE()),
(N'5.01.001', N'Salaries and Wages', N'رواتب وأجور', 1007, 1013,
    (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = N'5.00.000'), 2, 0, 1, 1, N'SAR', 0, N'رواتب وأجور الموظفين', GETDATE()),
(N'5.02.001', N'Office Expenses', N'مصروفات إدارية', 1007, 1013,
    (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = N'5.00.000'), 2, 0, 1, 1, N'SAR', 0, N'المصروفات الإدارية والعامة', GETDATE()),
(N'5.03.001', N'Utilities', N'المرافق العامة', 1007, 1013,
    (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = N'5.00.000'), 2, 0, 1, 1, N'SAR', 0, N'فواتير الكهرباء والماء والهاتف', GETDATE());

PRINT N'✓ تم إدراج الحسابات التفصيلية';

-- التحقق من النتائج
DECLARE @Count INT = (SELECT COUNT(*) FROM ChartOfAccounts WHERE IsActive = 1);
PRINT N'عدد الحسابات المدرجة: ' + CAST(@Count AS NVARCHAR(10));

-- عرض عينة
SELECT TOP 5
    AccountCode as N'رمز الحساب',
    AccountNameAr as N'اسم الحساب',
    AccountLevel as N'المستوى'
FROM ChartOfAccounts 
WHERE IsActive = 1 
ORDER BY AccountCode;

PRINT N'=== انتهى الإدراج بنجاح ===';
