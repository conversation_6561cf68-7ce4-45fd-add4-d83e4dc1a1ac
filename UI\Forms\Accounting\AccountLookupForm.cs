using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Awqaf_Managment.Models.Accounting;
using Awqaf_Managment.Services.Accounting;

namespace Awqaf_Managment.UI.Forms.Accounting
{
    /// <summary>
    /// نموذج البحث عن الحسابات
    /// Account Lookup Form
    /// </summary>
    public partial class AccountLookupForm : Form
    {
        #region Fields

        private readonly ChartOfAccountsService _accountService;
        private List<ChartOfAccount> _allAccounts;
        private List<ChartOfAccount> _filteredAccounts;

        #endregion

        #region Properties

        /// <summary>
        /// الحساب المختار
        /// Selected Account
        /// </summary>
        public ChartOfAccount SelectedAccount { get; private set; }

        /// <summary>
        /// هل تم اختيار حساب
        /// Is Account Selected
        /// </summary>
        public bool IsAccountSelected { get; private set; }

        #endregion

        #region Constructor

        /// <summary>
        /// منشئ النموذج
        /// Form Constructor
        /// </summary>
        public AccountLookupForm()
        {
            InitializeComponent();
            _accountService = new ChartOfAccountsService();
            InitializeForm();
        }

        /// <summary>
        /// منشئ النموذج مع تصفية الحسابات
        /// Form Constructor with Account Filter
        /// </summary>
        /// <param name="accountFilter">تصفية الحسابات</param>
        public AccountLookupForm(Func<ChartOfAccount, bool> accountFilter) : this()
        {
            if (accountFilter != null)
            {
                _allAccounts = _allAccounts?.Where(accountFilter).ToList() ?? new List<ChartOfAccount>();
                LoadAccountsToGrid();
            }
        }

        #endregion

        #region Form Initialization

        /// <summary>
        /// تهيئة النموذج
        /// Initialize Form
        /// </summary>
        private void InitializeForm()
        {
            try
            {
                // تحميل البيانات
                LoadData();
                
                // تهيئة الشبكة
                InitializeDataGrid();
                
                // تهيئة الأحداث
                InitializeEvents();
                
                // تطبيق التنسيق
                ApplyModernStyling();
                
                // تحميل البيانات في الشبكة
                LoadAccountsToGrid();
                
                // تركيز على مربع البحث
                txtSearch.Focus();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة النموذج: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تحميل البيانات
        /// Load Data
        /// </summary>
        private void LoadData()
        {
            try
            {
                _allAccounts = ChartOfAccountsService.GetAllAccounts() ?? new List<ChartOfAccount>();
                _filteredAccounts = new List<ChartOfAccount>(_allAccounts);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                _allAccounts = new List<ChartOfAccount>();
                _filteredAccounts = new List<ChartOfAccount>();
            }
        }

        /// <summary>
        /// تهيئة شبكة البيانات
        /// Initialize Data Grid
        /// </summary>
        private void InitializeDataGrid()
        {
            try
            {
                dgvAccounts.AutoGenerateColumns = false;
                dgvAccounts.Columns.Clear();

                // عمود رقم الحساب
                var colAccountCode = new DataGridViewTextBoxColumn
                {
                    Name = "AccountCode",
                    HeaderText = "رقم الحساب",
                    DataPropertyName = "AccountCode",
                    Width = 120,
                    ReadOnly = true
                };
                dgvAccounts.Columns.Add(colAccountCode);

                // عمود اسم الحساب بالعربية
                var colAccountNameAr = new DataGridViewTextBoxColumn
                {
                    Name = "AccountNameAr",
                    HeaderText = "اسم الحساب",
                    DataPropertyName = "AccountNameAr",
                    Width = 250,
                    ReadOnly = true
                };
                dgvAccounts.Columns.Add(colAccountNameAr);

                // عمود اسم الحساب بالإنجليزية
                var colAccountNameEn = new DataGridViewTextBoxColumn
                {
                    Name = "AccountNameEn",
                    HeaderText = "Account Name (EN)",
                    DataPropertyName = "AccountNameEn",
                    Width = 200,
                    ReadOnly = true
                };
                dgvAccounts.Columns.Add(colAccountNameEn);

                // عمود نوع الحساب
                var colAccountType = new DataGridViewTextBoxColumn
                {
                    Name = "AccountType",
                    HeaderText = "نوع الحساب",
                    DataPropertyName = "AccountType",
                    Width = 120,
                    ReadOnly = true
                };
                dgvAccounts.Columns.Add(colAccountType);

                // تنسيق الشبكة
                dgvAccounts.EnableHeadersVisualStyles = false;
                dgvAccounts.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(52, 73, 94);
                dgvAccounts.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
                dgvAccounts.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 9F, FontStyle.Bold);
                dgvAccounts.ColumnHeadersHeight = 35;
                
                dgvAccounts.DefaultCellStyle.Font = new Font("Tahoma", 9F);
                dgvAccounts.DefaultCellStyle.BackColor = Color.White;
                dgvAccounts.DefaultCellStyle.ForeColor = Color.FromArgb(52, 73, 94);
                dgvAccounts.DefaultCellStyle.SelectionBackColor = Color.FromArgb(41, 128, 185);
                dgvAccounts.DefaultCellStyle.SelectionForeColor = Color.White;
                
                dgvAccounts.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(236, 240, 241);
                dgvAccounts.RowTemplate.Height = 30;
                dgvAccounts.GridColor = Color.FromArgb(189, 195, 199);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة الشبكة: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تهيئة الأحداث
        /// Initialize Events
        /// </summary>
        private void InitializeEvents()
        {
            // أحداث البحث
            txtSearch.TextChanged += TxtSearch_TextChanged;
            txtSearch.KeyDown += TxtSearch_KeyDown;
            
            // أحداث الشبكة
            dgvAccounts.CellDoubleClick += DgvAccounts_CellDoubleClick;
            dgvAccounts.KeyDown += DgvAccounts_KeyDown;
            
            // أحداث الأزرار
            btnSelect.Click += BtnSelect_Click;
            btnCancel.Click += BtnCancel_Click;
            
            // أحداث النموذج
            this.KeyDown += AccountLookupForm_KeyDown;
        }

        /// <summary>
        /// تطبيق التنسيق العصري
        /// Apply Modern Styling
        /// </summary>
        private void ApplyModernStyling()
        {
            try
            {
                // تنسيق الأزرار
                ApplyButtonStyle(btnSelect, Color.FromArgb(46, 204, 113), Color.White);
                ApplyButtonStyle(btnCancel, Color.FromArgb(149, 165, 166), Color.White);
                
                // تنسيق مربع البحث
                txtSearch.BorderStyle = BorderStyle.FixedSingle;
                txtSearch.BackColor = Color.White;
                txtSearch.ForeColor = Color.FromArgb(52, 73, 94);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق التنسيق: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق تنسيق الزر
        /// Apply Button Style
        /// </summary>
        /// <param name="button">الزر</param>
        /// <param name="backColor">لون الخلفية</param>
        /// <param name="foreColor">لون النص</param>
        private void ApplyButtonStyle(Button button, Color backColor, Color foreColor)
        {
            button.BackColor = backColor;
            button.ForeColor = foreColor;
            button.FlatStyle = FlatStyle.Flat;
            button.FlatAppearance.BorderSize = 0;
            button.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            button.Cursor = Cursors.Hand;
        }

        #endregion

        #region Data Loading

        /// <summary>
        /// تحميل الحسابات في الشبكة
        /// Load Accounts to Grid
        /// </summary>
        private void LoadAccountsToGrid()
        {
            try
            {
                dgvAccounts.DataSource = null;
                dgvAccounts.DataSource = _filteredAccounts;
                
                if (_filteredAccounts.Count > 0)
                {
                    dgvAccounts.Rows[0].Selected = true;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Search Functionality

        /// <summary>
        /// تصفية الحسابات حسب النص المدخل
        /// Filter Accounts by Search Text
        /// </summary>
        /// <param name="searchText">نص البحث</param>
        private void FilterAccounts(string searchText)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchText))
                {
                    _filteredAccounts = new List<ChartOfAccount>(_allAccounts);
                }
                else
                {
                    searchText = searchText.Trim().ToLower();
                    _filteredAccounts = _allAccounts.Where(account =>
                        (account.AccountCode?.ToLower().Contains(searchText) ?? false) ||
                        (account.AccountNameAr?.ToLower().Contains(searchText) ?? false) ||
                        (account.AccountNameEn?.ToLower().Contains(searchText) ?? false)
                    ).ToList();
                }
                
                LoadAccountsToGrid();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// حدث تغيير نص البحث
        /// Search Text Changed Event
        /// </summary>
        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            FilterAccounts(txtSearch.Text);
        }

        /// <summary>
        /// حدث الضغط على مفتاح في مربع البحث
        /// Search TextBox Key Down Event
        /// </summary>
        private void TxtSearch_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Down && dgvAccounts.Rows.Count > 0)
            {
                dgvAccounts.Focus();
                dgvAccounts.Rows[0].Selected = true;
                e.Handled = true;
            }
            else if (e.KeyCode == Keys.Enter)
            {
                SelectAccount();
                e.Handled = true;
            }
        }

        /// <summary>
        /// حدث النقر المزدوج على خلية في الشبكة
        /// DataGridView Cell Double Click Event
        /// </summary>
        private void DgvAccounts_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                SelectAccount();
            }
        }

        /// <summary>
        /// حدث الضغط على مفتاح في الشبكة
        /// DataGridView Key Down Event
        /// </summary>
        private void DgvAccounts_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                SelectAccount();
                e.Handled = true;
            }
        }

        /// <summary>
        /// حدث النقر على زر الاختيار
        /// Select Button Click Event
        /// </summary>
        private void BtnSelect_Click(object sender, EventArgs e)
        {
            SelectAccount();
        }

        /// <summary>
        /// حدث النقر على زر الإلغاء
        /// Cancel Button Click Event
        /// </summary>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            CancelSelection();
        }

        /// <summary>
        /// حدث الضغط على مفتاح في النموذج
        /// Form Key Down Event
        /// </summary>
        private void AccountLookupForm_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Escape)
            {
                CancelSelection();
                e.Handled = true;
            }
        }

        #endregion

        #region Selection Methods

        /// <summary>
        /// اختيار الحساب
        /// Select Account
        /// </summary>
        private void SelectAccount()
        {
            try
            {
                if (dgvAccounts.CurrentRow != null && dgvAccounts.CurrentRow.DataBoundItem is ChartOfAccount account)
                {
                    SelectedAccount = account;
                    IsAccountSelected = true;
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("يرجى اختيار حساب من القائمة", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في اختيار الحساب: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// إلغاء الاختيار
        /// Cancel Selection
        /// </summary>
        private void CancelSelection()
        {
            SelectedAccount = null;
            IsAccountSelected = false;
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        #endregion
    }
}
