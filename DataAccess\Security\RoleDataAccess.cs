using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using Awqaf_Managment.Common;
using Awqaf_Managment.Models.Security;

namespace Awqaf_Managment.DataAccess.Security
{
    /// <summary>
    /// طبقة الوصول لبيانات الأدوار
    /// </summary>
    public static class RoleDataAccess
    {
        /// <summary>
        /// الحصول على جميع الأدوار
        /// </summary>
        /// <returns>قائمة الأدوار</returns>
        public static List<Role> GetAllRoles()
        {
            var roles = new List<Role>();

            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT RoleId, RoleName, RoleNameAr, Description, IsActive, CreatedDate
                        FROM Roles 
                        WHERE IsActive = 1
                        ORDER BY RoleNameAr";

                    using (var command = new SqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                roles.Add(new Role
                                {
                                    RoleId = Convert.ToInt32(reader["RoleId"]),
                                    RoleName = reader["RoleName"].ToString(),
                                    RoleNameAr = reader["RoleNameAr"].ToString(),
                                    Description = reader["Description"].ToString(),
                                    IsActive = Convert.ToBoolean(reader["IsActive"]),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"])
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب الأدوار: {ex.Message}");
            }

            return roles;
        }

        /// <summary>
        /// الحصول على دور بواسطة المعرف
        /// </summary>
        /// <param name="roleId">معرف الدور</param>
        /// <returns>الدور أو null إذا لم يوجد</returns>
        public static Role GetRoleById(int roleId)
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT RoleId, RoleName, RoleNameAr, Description, IsActive, CreatedDate
                        FROM Roles 
                        WHERE RoleId = @RoleId";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@RoleId", roleId);

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new Role
                                {
                                    RoleId = Convert.ToInt32(reader["RoleId"]),
                                    RoleName = reader["RoleName"].ToString(),
                                    RoleNameAr = reader["RoleNameAr"].ToString(),
                                    Description = reader["Description"].ToString(),
                                    IsActive = Convert.ToBoolean(reader["IsActive"]),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"])
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب الدور: {ex.Message}");
            }

            return null;
        }

        /// <summary>
        /// إنشاء دور جديد
        /// </summary>
        /// <param name="role">بيانات الدور</param>
        /// <returns>معرف الدور الجديد</returns>
        public static int CreateRole(Role role)
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    string query = @"
                        INSERT INTO Roles (RoleName, RoleNameAr, Description, IsActive, CreatedDate)
                        VALUES (@RoleName, @RoleNameAr, @Description, @IsActive, GETDATE());
                        SELECT SCOPE_IDENTITY();";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@RoleName", role.RoleName);
                        command.Parameters.AddWithValue("@RoleNameAr", role.RoleNameAr);
                        command.Parameters.AddWithValue("@Description", role.Description ?? "");
                        command.Parameters.AddWithValue("@IsActive", role.IsActive);

                        return Convert.ToInt32(command.ExecuteScalar());
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء الدور: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث دور
        /// </summary>
        /// <param name="role">بيانات الدور</param>
        public static void UpdateRole(Role role)
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    string query = @"
                        UPDATE Roles 
                        SET RoleName = @RoleName,
                            RoleNameAr = @RoleNameAr,
                            Description = @Description,
                            IsActive = @IsActive
                        WHERE RoleId = @RoleId";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@RoleId", role.RoleId);
                        command.Parameters.AddWithValue("@RoleName", role.RoleName);
                        command.Parameters.AddWithValue("@RoleNameAr", role.RoleNameAr);
                        command.Parameters.AddWithValue("@Description", role.Description ?? "");
                        command.Parameters.AddWithValue("@IsActive", role.IsActive);

                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث الدور: {ex.Message}");
            }
        }

        /// <summary>
        /// حذف دور (حذف منطقي)
        /// </summary>
        /// <param name="roleId">معرف الدور</param>
        public static void DeleteRole(int roleId)
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    string query = "UPDATE Roles SET IsActive = 0 WHERE RoleId = @RoleId";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@RoleId", roleId);
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حذف الدور: {ex.Message}");
            }
        }

        /// <summary>
        /// التحقق من وجود اسم الدور
        /// </summary>
        /// <param name="roleName">اسم الدور</param>
        /// <param name="excludeRoleId">معرف الدور المستثنى من البحث</param>
        /// <returns>true إذا كان موجود، false إذا لم يكن موجود</returns>
        public static bool IsRoleNameExists(string roleName, int? excludeRoleId = null)
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    string query = "SELECT COUNT(*) FROM Roles WHERE (RoleName = @RoleName OR RoleNameAr = @RoleName)";
                    
                    if (excludeRoleId.HasValue)
                    {
                        query += " AND RoleId != @ExcludeRoleId";
                    }

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@RoleName", roleName);
                        if (excludeRoleId.HasValue)
                        {
                            command.Parameters.AddWithValue("@ExcludeRoleId", excludeRoleId.Value);
                        }

                        return Convert.ToInt32(command.ExecuteScalar()) > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في التحقق من اسم الدور: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على أدوار المستخدم
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>قائمة الأدوار</returns>
        public static List<Role> GetUserRoles(int userId)
        {
            var roles = new List<Role>();

            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT r.RoleId, r.RoleName, r.RoleNameAr, r.Description, r.IsActive, r.CreatedDate
                        FROM Roles r
                        INNER JOIN UserRoles ur ON r.RoleId = ur.RoleId
                        WHERE ur.UserId = @UserId AND ur.IsActive = 1 AND r.IsActive = 1
                        ORDER BY r.RoleNameAr";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@UserId", userId);

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                roles.Add(new Role
                                {
                                    RoleId = Convert.ToInt32(reader["RoleId"]),
                                    RoleName = reader["RoleName"].ToString(),
                                    RoleNameAr = reader["RoleNameAr"].ToString(),
                                    Description = reader["Description"].ToString(),
                                    IsActive = Convert.ToBoolean(reader["IsActive"]),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"])
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب أدوار المستخدم: {ex.Message}");
            }

            return roles;
        }
    }
}
