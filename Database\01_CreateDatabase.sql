-- ===================================================================
-- سكريبت إنشاء قاعدة بيانات نظام إدارة الأوقاف
-- SQL Server 2014 Compatible
-- ===================================================================

USE master;
GO

-- التحقق من وجود قاعدة البيانات وحذفها إذا كانت موجودة
IF EXISTS (SELECT name FROM sys.databases WHERE name = N'AwqafManagement')
BEGIN
    ALTER DATABASE AwqafManagement SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
    DROP DATABASE AwqafManagement;
END
GO

-- إنشاء قاعدة البيانات
CREATE DATABASE AwqafManagement
ON 
( NAME = 'AwqafManagement_Data',
  FILENAME = 'C:\Program Files\Microsoft SQL Server\MSSQL12.MSSQLSERVER\MSSQL\DATA\AwqafManagement.mdf',
  SIZE = 100MB,
  MAXSIZE = 1GB,
  FILEGROWTH = 10MB )
LOG ON 
( NAME = 'AwqafManagement_Log',
  FILENAME = 'C:\Program Files\Microsoft SQL Server\MSSQL12.MSSQLSERVER\MSSQL\DATA\AwqafManagement.ldf',
  SIZE = 10MB,
  MAXSIZE = 100MB,
  FILEGROWTH = 5MB );
GO

-- استخدام قاعدة البيانات الجديدة
USE AwqafManagement;
GO

-- إعداد الترتيب العربي
ALTER DATABASE AwqafManagement COLLATE Arabic_CI_AS;
GO

PRINT N'تم إنشاء قاعدة بيانات نظام إدارة الأوقاف بنجاح';
GO
