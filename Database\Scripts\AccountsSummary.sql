-- ملخص الحسابات المدرجة
-- Summary of Inserted Accounts

USE AwqafManagement;
GO

-- عرض ملخص الحسابات حسب النوع والمستوى
SELECT 
    CASE AccountLevel 
        WHEN 1 THEN N'رئيسي'
        WHEN 2 THEN N'فرعي'
        WHEN 3 THEN N'تفصيلي'
    END as 'مستوى الحساب',
    COUNT(*) as 'عدد الحسابات'
FROM ChartOfAccounts 
GROUP BY AccountLevel
ORDER BY AccountLevel;

-- عرض الحسابات الرئيسية الخمسة
SELECT 
    AccountCode as 'رمز الحساب',
    AccountNameAr as 'اسم الحساب بالعربية',
    AccountName as 'اسم الحساب بالإنجليزية'
FROM ChartOfAccounts 
WHERE AccountLevel = 1
ORDER BY AccountCode;

-- عرض عينة من الحسابات الفرعية والتفصيلية
SELECT TOP 20
    AccountCode as 'رمز الحساب',
    AccountNameAr as 'اسم الحساب',
    CASE AccountLevel 
        WHEN 2 THEN N'فرعي'
        WHEN 3 THEN N'تفصيلي'
    END as 'نوع الحساب',
    CASE 
        WHEN OpeningBalance > 0 THEN FORMAT(OpeningBalance, 'N2') + N' ريال'
        ELSE N'لا يوجد'
    END as 'الرصيد الافتتاحي'
FROM ChartOfAccounts 
WHERE AccountLevel > 1
ORDER BY AccountCode;

-- إجمالي الحسابات
SELECT COUNT(*) as 'إجمالي الحسابات المدرجة' FROM ChartOfAccounts;

PRINT N'تم عرض ملخص شامل للحسابات المحاسبية المدرجة';
