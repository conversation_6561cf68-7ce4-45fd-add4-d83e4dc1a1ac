using System;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Drawing.Text;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace Awqaf_Managment.UI.Helpers
{
    /// <summary>
    /// مساعد التصميم العصري والخطوط العربية
    /// Modern Design and Arabic Fonts Helper
    /// </summary>
    public static class ModernDesignHelper
    {
        #region Color Schemes
        /// <summary>
        /// ألوان التصميم العصري
        /// Modern Design Colors
        /// </summary>
        public static class Colors
        {
            // الألوان الأساسية
            public static readonly Color Primary = Color.FromArgb(52, 152, 219);      // أزرق
            public static readonly Color Secondary = Color.FromArgb(155, 89, 182);    // بنفسجي
            public static readonly Color Success = Color.FromArgb(46, 204, 113);      // أخضر
            public static readonly Color Warning = Color.FromArgb(241, 196, 15);      // أصفر
            public static readonly Color Danger = Color.FromArgb(231, 76, 60);        // أحمر
            public static readonly Color Info = Color.FromArgb(52, 152, 219);         // أزرق فاتح

            // ألوان الخلفية
            public static readonly Color Background = Color.FromArgb(248, 249, 250);
            public static readonly Color Surface = Color.White;
            public static readonly Color Card = Color.FromArgb(255, 255, 255);

            // ألوان النص
            public static readonly Color TextPrimary = Color.FromArgb(33, 37, 41);
            public static readonly Color TextSecondary = Color.FromArgb(108, 117, 125);
            public static readonly Color TextMuted = Color.FromArgb(173, 181, 189);

            // ألوان الحدود
            public static readonly Color Border = Color.FromArgb(222, 226, 230);
            public static readonly Color BorderLight = Color.FromArgb(233, 236, 239);
            public static readonly Color BorderDark = Color.FromArgb(173, 181, 189);

            // ألوان الظلال
            public static readonly Color Shadow = Color.FromArgb(50, 0, 0, 0);
            public static readonly Color ShadowLight = Color.FromArgb(25, 0, 0, 0);
        }
        #endregion

        #region Font Management
        /// <summary>
        /// إدارة الخطوط العربية
        /// Arabic Font Management
        /// </summary>
        public static class Fonts
        {
            private static PrivateFontCollection _privateFonts;
            private static FontFamily _cairoFamily;
            private static FontFamily _tajawalFamily;

            /// <summary>
            /// تهيئة الخطوط العربية
            /// Initialize Arabic fonts
            /// </summary>
            public static void InitializeArabicFonts()
            {
                try
                {
                    _privateFonts = new PrivateFontCollection();
                    
                    // محاولة تحميل خط Cairo
                    LoadCairoFont();
                    
                    // محاولة تحميل خط Tajawal
                    LoadTajawalFont();
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"خطأ في تهيئة الخطوط: {ex.Message}");
                }
            }

            /// <summary>
            /// تحميل خط Cairo
            /// Load Cairo font
            /// </summary>
            private static void LoadCairoFont()
            {
                try
                {
                    // يمكن تحميل الخط من ملف أو من الموارد
                    // هنا نستخدم خط النظام كبديل
                    _cairoFamily = new FontFamily("Segoe UI");
                }
                catch
                {
                    _cairoFamily = SystemFonts.DefaultFont.FontFamily;
                }
            }

            /// <summary>
            /// تحميل خط Tajawal
            /// Load Tajawal font
            /// </summary>
            private static void LoadTajawalFont()
            {
                try
                {
                    // يمكن تحميل الخط من ملف أو من الموارد
                    // هنا نستخدم خط النظام كبديل
                    _tajawalFamily = new FontFamily("Tahoma");
                }
                catch
                {
                    _tajawalFamily = SystemFonts.DefaultFont.FontFamily;
                }
            }

            /// <summary>
            /// الحصول على خط Cairo
            /// Get Cairo font
            /// </summary>
            /// <param name="size">حجم الخط</param>
            /// <param name="style">نمط الخط</param>
            /// <returns>خط Cairo</returns>
            public static Font GetCairoFont(float size = 9f, FontStyle style = FontStyle.Regular)
            {
                try
                {
                    if (_cairoFamily == null)
                        InitializeArabicFonts();
                    
                    return new Font(_cairoFamily ?? SystemFonts.DefaultFont.FontFamily, size, style);
                }
                catch
                {
                    return new Font("Segoe UI", size, style);
                }
            }

            /// <summary>
            /// الحصول على خط Tajawal
            /// Get Tajawal font
            /// </summary>
            /// <param name="size">حجم الخط</param>
            /// <param name="style">نمط الخط</param>
            /// <returns>خط Tajawal</returns>
            public static Font GetTajawalFont(float size = 9f, FontStyle style = FontStyle.Regular)
            {
                try
                {
                    if (_tajawalFamily == null)
                        InitializeArabicFonts();
                    
                    return new Font(_tajawalFamily ?? SystemFonts.DefaultFont.FontFamily, size, style);
                }
                catch
                {
                    return new Font("Tahoma", size, style);
                }
            }

            /// <summary>
            /// الحصول على خط عربي افتراضي
            /// Get default Arabic font
            /// </summary>
            /// <param name="size">حجم الخط</param>
            /// <param name="style">نمط الخط</param>
            /// <returns>خط عربي</returns>
            public static Font GetDefaultArabicFont(float size = 9f, FontStyle style = FontStyle.Regular)
            {
                return GetCairoFont(size, style);
            }
        }
        #endregion

        #region Control Styling
        /// <summary>
        /// تطبيق التصميم العصري على النموذج
        /// Apply modern design to form
        /// </summary>
        /// <param name="form">النموذج</param>
        public static void ApplyModernDesign(Form form)
        {
            try
            {
                // إعدادات النموذج الأساسية
                form.BackColor = Colors.Background;
                form.Font = Fonts.GetDefaultArabicFont(9f);
                
                // تطبيق التصميم على جميع العناصر
                ApplyModernDesignToControls(form);
                
                // إعداد RTL للعربية
                form.RightToLeft = RightToLeft.Yes;
                form.RightToLeftLayout = true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق التصميم العصري: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق التصميم العصري على العناصر
        /// Apply modern design to controls
        /// </summary>
        /// <param name="parent">العنصر الأب</param>
        public static void ApplyModernDesignToControls(Control parent)
        {
            foreach (Control control in parent.Controls)
            {
                ApplyControlStyle(control);
                
                // تطبيق التصميم على العناصر الفرعية
                if (control.HasChildren)
                {
                    ApplyModernDesignToControls(control);
                }
            }
        }

        /// <summary>
        /// تطبيق نمط العنصر
        /// Apply control style
        /// </summary>
        /// <param name="control">العنصر</param>
        private static void ApplyControlStyle(Control control)
        {
            try
            {
                // تطبيق الخط العربي
                control.Font = Fonts.GetDefaultArabicFont(control.Font.Size, control.Font.Style);

                switch (control)
                {
                    case Button button:
                        StyleButton(button);
                        break;
                    case TextBox textBox:
                        StyleTextBox(textBox);
                        break;
                    case ComboBox comboBox:
                        StyleComboBox(comboBox);
                        break;
                    case Panel panel:
                        StylePanel(panel);
                        break;
                    case GroupBox groupBox:
                        StyleGroupBox(groupBox);
                        break;
                    case Label label:
                        StyleLabel(label);
                        break;
                    case TreeView treeView:
                        StyleTreeView(treeView);
                        break;
                    case DataGridView dataGridView:
                        StyleDataGridView(dataGridView);
                        break;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق نمط العنصر: {ex.Message}");
            }
        }

        /// <summary>
        /// تنسيق الأزرار
        /// Style buttons
        /// </summary>
        /// <param name="button">الزر</param>
        private static void StyleButton(Button button)
        {
            button.FlatStyle = FlatStyle.Flat;
            button.FlatAppearance.BorderSize = 1;
            button.FlatAppearance.BorderColor = Colors.Primary;
            button.BackColor = Colors.Primary;
            button.ForeColor = Color.White;
            button.Font = Fonts.GetDefaultArabicFont(9f, FontStyle.Bold);
            button.Cursor = Cursors.Hand;
            
            // إضافة تأثيرات الماوس
            button.MouseEnter += (s, e) => {
                button.BackColor = Color.FromArgb(41, 128, 185);
            };
            button.MouseLeave += (s, e) => {
                button.BackColor = Colors.Primary;
            };
        }

        /// <summary>
        /// تنسيق صناديق النص
        /// Style text boxes
        /// </summary>
        /// <param name="textBox">صندوق النص</param>
        private static void StyleTextBox(TextBox textBox)
        {
            textBox.BorderStyle = BorderStyle.FixedSingle;
            textBox.BackColor = Colors.Surface;
            textBox.ForeColor = Colors.TextPrimary;
            textBox.Font = Fonts.GetDefaultArabicFont(9f);
        }

        /// <summary>
        /// تنسيق القوائم المنسدلة
        /// Style combo boxes
        /// </summary>
        /// <param name="comboBox">القائمة المنسدلة</param>
        private static void StyleComboBox(ComboBox comboBox)
        {
            comboBox.FlatStyle = FlatStyle.Flat;
            comboBox.BackColor = Colors.Surface;
            comboBox.ForeColor = Colors.TextPrimary;
            comboBox.Font = Fonts.GetDefaultArabicFont(9f);
        }

        /// <summary>
        /// تنسيق اللوحات
        /// Style panels
        /// </summary>
        /// <param name="panel">اللوحة</param>
        private static void StylePanel(Panel panel)
        {
            panel.BackColor = Colors.Surface;
            panel.BorderStyle = BorderStyle.None;
        }

        /// <summary>
        /// تنسيق مجموعات العناصر
        /// Style group boxes
        /// </summary>
        /// <param name="groupBox">مجموعة العناصر</param>
        private static void StyleGroupBox(GroupBox groupBox)
        {
            groupBox.BackColor = Colors.Surface;
            groupBox.ForeColor = Colors.TextPrimary;
            groupBox.Font = Fonts.GetDefaultArabicFont(9f, FontStyle.Bold);
        }

        /// <summary>
        /// تنسيق التسميات
        /// Style labels
        /// </summary>
        /// <param name="label">التسمية</param>
        private static void StyleLabel(Label label)
        {
            label.BackColor = Color.Transparent;
            label.ForeColor = Colors.TextPrimary;
            label.Font = Fonts.GetDefaultArabicFont(9f);
        }

        /// <summary>
        /// تنسيق شجرة العرض
        /// Style tree view
        /// </summary>
        /// <param name="treeView">شجرة العرض</param>
        private static void StyleTreeView(TreeView treeView)
        {
            treeView.BackColor = Colors.Surface;
            treeView.ForeColor = Colors.TextPrimary;
            treeView.BorderStyle = BorderStyle.FixedSingle;
            treeView.Font = Fonts.GetDefaultArabicFont(9f);
            treeView.LineColor = Colors.Border;
        }

        /// <summary>
        /// تنسيق شبكة البيانات
        /// Style data grid view
        /// </summary>
        /// <param name="dataGridView">شبكة البيانات</param>
        private static void StyleDataGridView(DataGridView dataGridView)
        {
            dataGridView.BackgroundColor = Colors.Surface;
            dataGridView.GridColor = Colors.Border;
            dataGridView.BorderStyle = BorderStyle.None;
            dataGridView.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dataGridView.DefaultCellStyle.BackColor = Colors.Surface;
            dataGridView.DefaultCellStyle.ForeColor = Colors.TextPrimary;
            dataGridView.DefaultCellStyle.Font = Fonts.GetDefaultArabicFont(9f);
            dataGridView.ColumnHeadersDefaultCellStyle.BackColor = Colors.Primary;
            dataGridView.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dataGridView.ColumnHeadersDefaultCellStyle.Font = Fonts.GetDefaultArabicFont(9f, FontStyle.Bold);
        }
        #endregion

        #region Responsive Design
        /// <summary>
        /// تطبيق التصميم المتجاوب
        /// Apply responsive design
        /// </summary>
        /// <param name="form">النموذج</param>
        public static void ApplyResponsiveDesign(Form form)
        {
            try
            {
                // إعداد الحد الأدنى لحجم النموذج
                form.MinimumSize = new Size(800, 600);
                
                // إعداد أحداث تغيير الحجم
                form.Resize += (s, e) => AdjustControlsForScreenSize(form);
                
                // تطبيق التعديلات الأولية
                AdjustControlsForScreenSize(form);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق التصميم المتجاوب: {ex.Message}");
            }
        }

        /// <summary>
        /// تعديل العناصر حسب حجم الشاشة
        /// Adjust controls for screen size
        /// </summary>
        /// <param name="form">النموذج</param>
        private static void AdjustControlsForScreenSize(Form form)
        {
            try
            {
                var screenSize = Screen.FromControl(form).Bounds.Size;
                var formSize = form.Size;
                
                // تعديل أحجام الخطوط حسب حجم الشاشة
                float fontScale = Math.Min(formSize.Width / 1200f, formSize.Height / 800f);
                fontScale = Math.Max(0.8f, Math.Min(1.2f, fontScale)); // تحديد النطاق
                
                AdjustFontSizes(form, fontScale);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تعديل العناصر: {ex.Message}");
            }
        }

        /// <summary>
        /// تعديل أحجام الخطوط
        /// Adjust font sizes
        /// </summary>
        /// <param name="parent">العنصر الأب</param>
        /// <param name="scale">معامل التكبير</param>
        private static void AdjustFontSizes(Control parent, float scale)
        {
            foreach (Control control in parent.Controls)
            {
                try
                {
                    var newSize = control.Font.Size * scale;
                    control.Font = Fonts.GetDefaultArabicFont(newSize, control.Font.Style);
                    
                    if (control.HasChildren)
                    {
                        AdjustFontSizes(control, scale);
                    }
                }
                catch
                {
                    // تجاهل الأخطاء في تعديل الخطوط
                }
            }
        }
        #endregion

        #region Utility Methods
        /// <summary>
        /// إنشاء ظل للعنصر
        /// Create shadow for control
        /// </summary>
        /// <param name="control">العنصر</param>
        public static void AddShadowEffect(Control control)
        {
            try
            {
                // يمكن تطبيق تأثير الظل باستخدام Panel إضافي
                var shadowPanel = new Panel
                {
                    Size = new Size(control.Width + 4, control.Height + 4),
                    Location = new Point(control.Location.X + 2, control.Location.Y + 2),
                    BackColor = Colors.Shadow
                };
                
                control.Parent?.Controls.Add(shadowPanel);
                shadowPanel.SendToBack();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إضافة تأثير الظل: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق انتقال سلس للألوان
        /// Apply smooth color transition
        /// </summary>
        /// <param name="control">العنصر</param>
        /// <param name="fromColor">اللون الأولي</param>
        /// <param name="toColor">اللون النهائي</param>
        /// <param name="duration">مدة الانتقال بالميلي ثانية</param>
        public static void AnimateColorTransition(Control control, Color fromColor, Color toColor, int duration = 300)
        {
            try
            {
                var timer = new Timer { Interval = 16 }; // ~60 FPS
                var startTime = DateTime.Now;
                
                timer.Tick += (s, e) =>
                {
                    var elapsed = (DateTime.Now - startTime).TotalMilliseconds;
                    var progress = Math.Min(1.0, elapsed / duration);
                    
                    if (progress >= 1.0)
                    {
                        control.BackColor = toColor;
                        timer.Stop();
                        timer.Dispose();
                    }
                    else
                    {
                        var r = (int)(fromColor.R + (toColor.R - fromColor.R) * progress);
                        var g = (int)(fromColor.G + (toColor.G - fromColor.G) * progress);
                        var b = (int)(fromColor.B + (toColor.B - fromColor.B) * progress);
                        control.BackColor = Color.FromArgb(r, g, b);
                    }
                };
                
                timer.Start();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في انتقال الألوان: {ex.Message}");
            }
        }
        #endregion
    }
}
