namespace Awqaf_Managment.UI.Forms.Accounting
{
    partial class CurrencyManagementForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.pnlMain = new System.Windows.Forms.Panel();
            this.pnlData = new System.Windows.Forms.Panel();
            this.dgvCurrencies = new System.Windows.Forms.DataGridView();
            this.pnlForm = new System.Windows.Forms.Panel();
            this.grpCurrencyInfo = new System.Windows.Forms.GroupBox();
            this.chkIsActive = new System.Windows.Forms.CheckBox();
            this.chkIsBaseCurrency = new System.Windows.Forms.CheckBox();
            this.nudDecimalPlaces = new System.Windows.Forms.NumericUpDown();
            this.txtExchangeRate = new System.Windows.Forms.TextBox();
            this.txtSymbol = new System.Windows.Forms.TextBox();
            this.txtCurrencyNameEn = new System.Windows.Forms.TextBox();
            this.txtCurrencyNameAr = new System.Windows.Forms.TextBox();
            this.txtCurrencyCode = new System.Windows.Forms.TextBox();
            this.lblIsActive = new System.Windows.Forms.Label();
            this.lblIsBaseCurrency = new System.Windows.Forms.Label();
            this.lblDecimalPlaces = new System.Windows.Forms.Label();
            this.lblExchangeRate = new System.Windows.Forms.Label();
            this.lblSymbol = new System.Windows.Forms.Label();
            this.lblCurrencyNameEn = new System.Windows.Forms.Label();
            this.lblCurrencyNameAr = new System.Windows.Forms.Label();
            this.lblCurrencyCode = new System.Windows.Forms.Label();
            this.pnlButtons = new System.Windows.Forms.Panel();
            this.btnCancel = new System.Windows.Forms.Button();
            this.btnSave = new System.Windows.Forms.Button();
            this.btnRefresh = new System.Windows.Forms.Button();
            this.btnDelete = new System.Windows.Forms.Button();
            this.btnEdit = new System.Windows.Forms.Button();
            this.btnAdd = new System.Windows.Forms.Button();
            this.pnlTitle = new System.Windows.Forms.Panel();
            this.lblTitle = new System.Windows.Forms.Label();
            this.pnlMain.SuspendLayout();
            this.pnlData.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvCurrencies)).BeginInit();
            this.pnlForm.SuspendLayout();
            this.grpCurrencyInfo.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nudDecimalPlaces)).BeginInit();
            this.pnlButtons.SuspendLayout();
            this.pnlTitle.SuspendLayout();
            this.SuspendLayout();
            //
            // pnlMain
            //
            this.pnlMain.Controls.Add(this.pnlData);
            this.pnlMain.Controls.Add(this.pnlForm);
            this.pnlMain.Controls.Add(this.pnlTitle);
            this.pnlMain.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlMain.Location = new System.Drawing.Point(0, 0);
            this.pnlMain.Name = "pnlMain";
            this.pnlMain.Padding = new System.Windows.Forms.Padding(10);
            this.pnlMain.Size = new System.Drawing.Size(1200, 700);
            this.pnlMain.TabIndex = 0;
            //
            // pnlData
            //
            this.pnlData.Controls.Add(this.dgvCurrencies);
            this.pnlData.Dock = System.Windows.Forms.DockStyle.Fill;
            this.pnlData.Location = new System.Drawing.Point(10, 70);
            this.pnlData.Name = "pnlData";
            this.pnlData.Padding = new System.Windows.Forms.Padding(5);
            this.pnlData.Size = new System.Drawing.Size(780, 620);
            this.pnlData.TabIndex = 2;
            //
            // dgvCurrencies
            //
            this.dgvCurrencies.AllowUserToAddRows = false;
            this.dgvCurrencies.AllowUserToDeleteRows = false;
            this.dgvCurrencies.BackgroundColor = System.Drawing.Color.White;
            this.dgvCurrencies.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.dgvCurrencies.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            this.dgvCurrencies.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dgvCurrencies.Location = new System.Drawing.Point(5, 5);
            this.dgvCurrencies.MultiSelect = false;
            this.dgvCurrencies.Name = "dgvCurrencies";
            this.dgvCurrencies.ReadOnly = true;
            this.dgvCurrencies.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dgvCurrencies.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgvCurrencies.Size = new System.Drawing.Size(770, 610);
            this.dgvCurrencies.TabIndex = 0;
            this.dgvCurrencies.SelectionChanged += new System.EventHandler(this.DgvCurrencies_SelectionChanged);
            //
            // pnlForm
            //
            this.pnlForm.Controls.Add(this.grpCurrencyInfo);
            this.pnlForm.Controls.Add(this.pnlButtons);
            this.pnlForm.Dock = System.Windows.Forms.DockStyle.Right;
            this.pnlForm.Location = new System.Drawing.Point(790, 70);
            this.pnlForm.Name = "pnlForm";
            this.pnlForm.Padding = new System.Windows.Forms.Padding(5);
            this.pnlForm.Size = new System.Drawing.Size(400, 620);
            this.pnlForm.TabIndex = 1;
            //
            // grpCurrencyInfo
            //
            this.grpCurrencyInfo.Controls.Add(this.chkIsActive);
            this.grpCurrencyInfo.Controls.Add(this.chkIsBaseCurrency);
            this.grpCurrencyInfo.Controls.Add(this.nudDecimalPlaces);
            this.grpCurrencyInfo.Controls.Add(this.txtExchangeRate);
            this.grpCurrencyInfo.Controls.Add(this.txtSymbol);
            this.grpCurrencyInfo.Controls.Add(this.txtCurrencyNameEn);
            this.grpCurrencyInfo.Controls.Add(this.txtCurrencyNameAr);
            this.grpCurrencyInfo.Controls.Add(this.txtCurrencyCode);
            this.grpCurrencyInfo.Controls.Add(this.lblIsActive);
            this.grpCurrencyInfo.Controls.Add(this.lblIsBaseCurrency);
            this.grpCurrencyInfo.Controls.Add(this.lblDecimalPlaces);
            this.grpCurrencyInfo.Controls.Add(this.lblExchangeRate);
            this.grpCurrencyInfo.Controls.Add(this.lblSymbol);
            this.grpCurrencyInfo.Controls.Add(this.lblCurrencyNameEn);
            this.grpCurrencyInfo.Controls.Add(this.lblCurrencyNameAr);
            this.grpCurrencyInfo.Controls.Add(this.lblCurrencyCode);
            this.grpCurrencyInfo.Dock = System.Windows.Forms.DockStyle.Fill;
            this.grpCurrencyInfo.Font = new System.Drawing.Font("Tahoma", 10F, System.Drawing.FontStyle.Bold);
            this.grpCurrencyInfo.Location = new System.Drawing.Point(5, 5);
            this.grpCurrencyInfo.Name = "grpCurrencyInfo";
            this.grpCurrencyInfo.Padding = new System.Windows.Forms.Padding(10);
            this.grpCurrencyInfo.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.grpCurrencyInfo.Size = new System.Drawing.Size(390, 540);
            this.grpCurrencyInfo.TabIndex = 1;
            this.grpCurrencyInfo.TabStop = false;
            this.grpCurrencyInfo.Text = "💰 بيانات العملة";
            //
            // chkIsActive
            //
            this.chkIsActive.AutoSize = true;
            this.chkIsActive.Font = new System.Drawing.Font("Tahoma", 10F);
            this.chkIsActive.Location = new System.Drawing.Point(280, 480);
            this.chkIsActive.Name = "chkIsActive";
            this.chkIsActive.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.chkIsActive.Size = new System.Drawing.Size(15, 14);
            this.chkIsActive.TabIndex = 15;
            this.chkIsActive.UseVisualStyleBackColor = true;
            //
            // chkIsBaseCurrency
            //
            this.chkIsBaseCurrency.AutoSize = true;
            this.chkIsBaseCurrency.Font = new System.Drawing.Font("Tahoma", 10F);
            this.chkIsBaseCurrency.Location = new System.Drawing.Point(280, 440);
            this.chkIsBaseCurrency.Name = "chkIsBaseCurrency";
            this.chkIsBaseCurrency.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.chkIsBaseCurrency.Size = new System.Drawing.Size(15, 14);
            this.chkIsBaseCurrency.TabIndex = 14;
            this.chkIsBaseCurrency.UseVisualStyleBackColor = true;
            //
            // nudDecimalPlaces
            //
            this.nudDecimalPlaces.Font = new System.Drawing.Font("Tahoma", 10F);
            this.nudDecimalPlaces.Location = new System.Drawing.Point(20, 400);
            this.nudDecimalPlaces.Maximum = new decimal(new int[] { 6, 0, 0, 0 });
            this.nudDecimalPlaces.Name = "nudDecimalPlaces";
            this.nudDecimalPlaces.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.nudDecimalPlaces.Size = new System.Drawing.Size(275, 24);
            this.nudDecimalPlaces.TabIndex = 13;
            this.nudDecimalPlaces.Value = new decimal(new int[] { 2, 0, 0, 0 });
            //
            // txtExchangeRate
            //
            this.txtExchangeRate.Font = new System.Drawing.Font("Tahoma", 10F);
            this.txtExchangeRate.Location = new System.Drawing.Point(20, 320);
            this.txtExchangeRate.Name = "txtExchangeRate";
            this.txtExchangeRate.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.txtExchangeRate.Size = new System.Drawing.Size(275, 24);
            this.txtExchangeRate.TabIndex = 12;
            this.txtExchangeRate.Text = "1.0000";
            this.txtExchangeRate.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            //
            // txtSymbol
            //
            this.txtSymbol.Font = new System.Drawing.Font("Tahoma", 10F);
            this.txtSymbol.Location = new System.Drawing.Point(20, 240);
            this.txtSymbol.Name = "txtSymbol";
            this.txtSymbol.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.txtSymbol.Size = new System.Drawing.Size(275, 24);
            this.txtSymbol.TabIndex = 11;
            this.txtSymbol.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            //
            // txtCurrencyNameEn
            //
            this.txtCurrencyNameEn.Font = new System.Drawing.Font("Tahoma", 10F);
            this.txtCurrencyNameEn.Location = new System.Drawing.Point(20, 160);
            this.txtCurrencyNameEn.Name = "txtCurrencyNameEn";
            this.txtCurrencyNameEn.Size = new System.Drawing.Size(275, 24);
            this.txtCurrencyNameEn.TabIndex = 10;
            //
            // txtCurrencyNameAr
            //
            this.txtCurrencyNameAr.Font = new System.Drawing.Font("Tahoma", 10F);
            this.txtCurrencyNameAr.Location = new System.Drawing.Point(20, 80);
            this.txtCurrencyNameAr.Name = "txtCurrencyNameAr";
            this.txtCurrencyNameAr.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.txtCurrencyNameAr.Size = new System.Drawing.Size(275, 24);
            this.txtCurrencyNameAr.TabIndex = 9;
            this.txtCurrencyNameAr.TextAlign = System.Windows.Forms.HorizontalAlignment.Right;
            //
            // txtCurrencyCode
            //
            this.txtCurrencyCode.Font = new System.Drawing.Font("Tahoma", 10F, System.Drawing.FontStyle.Bold);
            this.txtCurrencyCode.Location = new System.Drawing.Point(20, 40);
            this.txtCurrencyCode.MaxLength = 3;
            this.txtCurrencyCode.Name = "txtCurrencyCode";
            this.txtCurrencyCode.Size = new System.Drawing.Size(275, 24);
            this.txtCurrencyCode.TabIndex = 8;
            this.txtCurrencyCode.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            //
            // lblIsActive
            //
            this.lblIsActive.AutoSize = true;
            this.lblIsActive.Font = new System.Drawing.Font("Tahoma", 10F);
            this.lblIsActive.Location = new System.Drawing.Point(320, 480);
            this.lblIsActive.Name = "lblIsActive";
            this.lblIsActive.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lblIsActive.Size = new System.Drawing.Size(40, 17);
            this.lblIsActive.TabIndex = 7;
            this.lblIsActive.Text = "نشط:";
            //
            // lblIsBaseCurrency
            //
            this.lblIsBaseCurrency.AutoSize = true;
            this.lblIsBaseCurrency.Font = new System.Drawing.Font("Tahoma", 10F);
            this.lblIsBaseCurrency.Location = new System.Drawing.Point(300, 440);
            this.lblIsBaseCurrency.Name = "lblIsBaseCurrency";
            this.lblIsBaseCurrency.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lblIsBaseCurrency.Size = new System.Drawing.Size(85, 17);
            this.lblIsBaseCurrency.TabIndex = 6;
            this.lblIsBaseCurrency.Text = "عملة أساسية:";
            //
            // lblDecimalPlaces
            //
            this.lblDecimalPlaces.AutoSize = true;
            this.lblDecimalPlaces.Font = new System.Drawing.Font("Tahoma", 10F);
            this.lblDecimalPlaces.Location = new System.Drawing.Point(300, 400);
            this.lblDecimalPlaces.Name = "lblDecimalPlaces";
            this.lblDecimalPlaces.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lblDecimalPlaces.Size = new System.Drawing.Size(85, 17);
            this.lblDecimalPlaces.TabIndex = 5;
            this.lblDecimalPlaces.Text = "عدد العشريات:";
            //
            // lblExchangeRate
            //
            this.lblExchangeRate.AutoSize = true;
            this.lblExchangeRate.Font = new System.Drawing.Font("Tahoma", 10F);
            this.lblExchangeRate.Location = new System.Drawing.Point(300, 320);
            this.lblExchangeRate.Name = "lblExchangeRate";
            this.lblExchangeRate.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lblExchangeRate.Size = new System.Drawing.Size(75, 17);
            this.lblExchangeRate.TabIndex = 4;
            this.lblExchangeRate.Text = "سعر الصرف:";
            //
            // lblSymbol
            //
            this.lblSymbol.AutoSize = true;
            this.lblSymbol.Font = new System.Drawing.Font("Tahoma", 10F);
            this.lblSymbol.Location = new System.Drawing.Point(300, 240);
            this.lblSymbol.Name = "lblSymbol";
            this.lblSymbol.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lblSymbol.Size = new System.Drawing.Size(75, 17);
            this.lblSymbol.TabIndex = 3;
            this.lblSymbol.Text = "رمز العملة:";
            //
            // lblCurrencyNameEn
            //
            this.lblCurrencyNameEn.AutoSize = true;
            this.lblCurrencyNameEn.Font = new System.Drawing.Font("Tahoma", 10F);
            this.lblCurrencyNameEn.Location = new System.Drawing.Point(300, 160);
            this.lblCurrencyNameEn.Name = "lblCurrencyNameEn";
            this.lblCurrencyNameEn.Size = new System.Drawing.Size(85, 17);
            this.lblCurrencyNameEn.TabIndex = 2;
            this.lblCurrencyNameEn.Text = "الاسم بالإنجليزية:";
            //
            // lblCurrencyNameAr
            //
            this.lblCurrencyNameAr.AutoSize = true;
            this.lblCurrencyNameAr.Font = new System.Drawing.Font("Tahoma", 10F);
            this.lblCurrencyNameAr.Location = new System.Drawing.Point(300, 80);
            this.lblCurrencyNameAr.Name = "lblCurrencyNameAr";
            this.lblCurrencyNameAr.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lblCurrencyNameAr.Size = new System.Drawing.Size(85, 17);
            this.lblCurrencyNameAr.TabIndex = 1;
            this.lblCurrencyNameAr.Text = "اسم العملة:";
            //
            // lblCurrencyCode
            //
            this.lblCurrencyCode.AutoSize = true;
            this.lblCurrencyCode.Font = new System.Drawing.Font("Tahoma", 10F);
            this.lblCurrencyCode.Location = new System.Drawing.Point(300, 40);
            this.lblCurrencyCode.Name = "lblCurrencyCode";
            this.lblCurrencyCode.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lblCurrencyCode.Size = new System.Drawing.Size(75, 17);
            this.lblCurrencyCode.TabIndex = 0;
            this.lblCurrencyCode.Text = "رمز العملة:";
            //
            // pnlButtons
            //
            this.pnlButtons.Controls.Add(this.btnCancel);
            this.pnlButtons.Controls.Add(this.btnSave);
            this.pnlButtons.Controls.Add(this.btnRefresh);
            this.pnlButtons.Controls.Add(this.btnDelete);
            this.pnlButtons.Controls.Add(this.btnEdit);
            this.pnlButtons.Controls.Add(this.btnAdd);
            this.pnlButtons.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.pnlButtons.Location = new System.Drawing.Point(5, 545);
            this.pnlButtons.Name = "pnlButtons";
            this.pnlButtons.Size = new System.Drawing.Size(390, 70);
            this.pnlButtons.TabIndex = 0;
            //
            // btnCancel
            //
            this.btnCancel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(231)))), ((int)(((byte)(76)))), ((int)(((byte)(60)))));
            this.btnCancel.FlatAppearance.BorderSize = 0;
            this.btnCancel.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnCancel.Font = new System.Drawing.Font("Tahoma", 10F, System.Drawing.FontStyle.Bold);
            this.btnCancel.ForeColor = System.Drawing.Color.White;
            this.btnCancel.Location = new System.Drawing.Point(10, 35);
            this.btnCancel.Name = "btnCancel";
            this.btnCancel.Size = new System.Drawing.Size(80, 30);
            this.btnCancel.TabIndex = 5;
            this.btnCancel.Text = "❌ إلغاء";
            this.btnCancel.UseVisualStyleBackColor = false;
            this.btnCancel.Click += new System.EventHandler(this.BtnCancel_Click);
            //
            // btnSave
            //
            this.btnSave.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(46)))), ((int)(((byte)(204)))), ((int)(((byte)(113)))));
            this.btnSave.FlatAppearance.BorderSize = 0;
            this.btnSave.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnSave.Font = new System.Drawing.Font("Tahoma", 10F, System.Drawing.FontStyle.Bold);
            this.btnSave.ForeColor = System.Drawing.Color.White;
            this.btnSave.Location = new System.Drawing.Point(100, 35);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(80, 30);
            this.btnSave.TabIndex = 4;
            this.btnSave.Text = "💾 حفظ";
            this.btnSave.UseVisualStyleBackColor = false;
            this.btnSave.Click += new System.EventHandler(this.BtnSave_Click);
            //
            // btnRefresh
            //
            this.btnRefresh.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(52)))), ((int)(((byte)(152)))), ((int)(((byte)(219)))));
            this.btnRefresh.FlatAppearance.BorderSize = 0;
            this.btnRefresh.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnRefresh.Font = new System.Drawing.Font("Tahoma", 10F, System.Drawing.FontStyle.Bold);
            this.btnRefresh.ForeColor = System.Drawing.Color.White;
            this.btnRefresh.Location = new System.Drawing.Point(300, 5);
            this.btnRefresh.Name = "btnRefresh";
            this.btnRefresh.Size = new System.Drawing.Size(80, 30);
            this.btnRefresh.TabIndex = 3;
            this.btnRefresh.Text = "🔄 تحديث";
            this.btnRefresh.UseVisualStyleBackColor = false;
            this.btnRefresh.Click += new System.EventHandler(this.BtnRefresh_Click);
            //
            // btnDelete
            //
            this.btnDelete.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(231)))), ((int)(((byte)(76)))), ((int)(((byte)(60)))));
            this.btnDelete.FlatAppearance.BorderSize = 0;
            this.btnDelete.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnDelete.Font = new System.Drawing.Font("Tahoma", 10F, System.Drawing.FontStyle.Bold);
            this.btnDelete.ForeColor = System.Drawing.Color.White;
            this.btnDelete.Location = new System.Drawing.Point(210, 5);
            this.btnDelete.Name = "btnDelete";
            this.btnDelete.Size = new System.Drawing.Size(80, 30);
            this.btnDelete.TabIndex = 2;
            this.btnDelete.Text = "🗑️ حذف";
            this.btnDelete.UseVisualStyleBackColor = false;
            this.btnDelete.Click += new System.EventHandler(this.BtnDelete_Click);
            //
            // btnEdit
            //
            this.btnEdit.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(241)))), ((int)(((byte)(196)))), ((int)(((byte)(15)))));
            this.btnEdit.FlatAppearance.BorderSize = 0;
            this.btnEdit.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnEdit.Font = new System.Drawing.Font("Tahoma", 10F, System.Drawing.FontStyle.Bold);
            this.btnEdit.ForeColor = System.Drawing.Color.White;
            this.btnEdit.Location = new System.Drawing.Point(120, 5);
            this.btnEdit.Name = "btnEdit";
            this.btnEdit.Size = new System.Drawing.Size(80, 30);
            this.btnEdit.TabIndex = 1;
            this.btnEdit.Text = "✏️ تعديل";
            this.btnEdit.UseVisualStyleBackColor = false;
            this.btnEdit.Click += new System.EventHandler(this.BtnEdit_Click);
            //
            // btnAdd
            //
            this.btnAdd.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(46)))), ((int)(((byte)(204)))), ((int)(((byte)(113)))));
            this.btnAdd.FlatAppearance.BorderSize = 0;
            this.btnAdd.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnAdd.Font = new System.Drawing.Font("Tahoma", 10F, System.Drawing.FontStyle.Bold);
            this.btnAdd.ForeColor = System.Drawing.Color.White;
            this.btnAdd.Location = new System.Drawing.Point(30, 5);
            this.btnAdd.Name = "btnAdd";
            this.btnAdd.Size = new System.Drawing.Size(80, 30);
            this.btnAdd.TabIndex = 0;
            this.btnAdd.Text = "➕ إضافة";
            this.btnAdd.UseVisualStyleBackColor = false;
            this.btnAdd.Click += new System.EventHandler(this.BtnAdd_Click);
            //
            // pnlTitle
            //
            this.pnlTitle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(52)))), ((int)(((byte)(152)))), ((int)(((byte)(219)))));
            this.pnlTitle.Controls.Add(this.lblTitle);
            this.pnlTitle.Dock = System.Windows.Forms.DockStyle.Top;
            this.pnlTitle.Location = new System.Drawing.Point(10, 10);
            this.pnlTitle.Name = "pnlTitle";
            this.pnlTitle.Size = new System.Drawing.Size(1180, 60);
            this.pnlTitle.TabIndex = 0;
            //
            // lblTitle
            //
            this.lblTitle.Dock = System.Windows.Forms.DockStyle.Fill;
            this.lblTitle.Font = new System.Drawing.Font("Tahoma", 16F, System.Drawing.FontStyle.Bold);
            this.lblTitle.ForeColor = System.Drawing.Color.White;
            this.lblTitle.Location = new System.Drawing.Point(0, 0);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lblTitle.Size = new System.Drawing.Size(1180, 60);
            this.lblTitle.TabIndex = 0;
            this.lblTitle.Text = "💰 إدارة العملات - Currency Management";
            this.lblTitle.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.pnlMain.ResumeLayout(false);
            this.pnlData.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgvCurrencies)).EndInit();
            this.pnlForm.ResumeLayout(false);
            this.grpCurrencyInfo.ResumeLayout(false);
            this.grpCurrencyInfo.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.nudDecimalPlaces)).EndInit();
            this.pnlButtons.ResumeLayout(false);
            this.pnlTitle.ResumeLayout(false);
            this.ResumeLayout(false);
            //
            // CurrencyManagementForm
            //
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(236)))), ((int)(((byte)(240)))), ((int)(((byte)(241)))));
            this.ClientSize = new System.Drawing.Size(1200, 700);
            this.Controls.Add(this.pnlMain);
            this.Font = new System.Drawing.Font("Tahoma", 8.25F);
            this.MinimumSize = new System.Drawing.Size(1200, 700);
            this.Name = "CurrencyManagementForm";
            this.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "إدارة العملات - Currency Management";
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
        }

        #endregion

        private System.Windows.Forms.Panel pnlMain;
        private System.Windows.Forms.Panel pnlData;
        private System.Windows.Forms.DataGridView dgvCurrencies;
        private System.Windows.Forms.Panel pnlForm;
        private System.Windows.Forms.GroupBox grpCurrencyInfo;
        private System.Windows.Forms.CheckBox chkIsActive;
        private System.Windows.Forms.CheckBox chkIsBaseCurrency;
        private System.Windows.Forms.NumericUpDown nudDecimalPlaces;
        private System.Windows.Forms.TextBox txtExchangeRate;
        private System.Windows.Forms.TextBox txtSymbol;
        private System.Windows.Forms.TextBox txtCurrencyNameEn;
        private System.Windows.Forms.TextBox txtCurrencyNameAr;
        private System.Windows.Forms.TextBox txtCurrencyCode;
        private System.Windows.Forms.Label lblIsActive;
        private System.Windows.Forms.Label lblIsBaseCurrency;
        private System.Windows.Forms.Label lblDecimalPlaces;
        private System.Windows.Forms.Label lblExchangeRate;
        private System.Windows.Forms.Label lblSymbol;
        private System.Windows.Forms.Label lblCurrencyNameEn;
        private System.Windows.Forms.Label lblCurrencyNameAr;
        private System.Windows.Forms.Label lblCurrencyCode;
        private System.Windows.Forms.Panel pnlButtons;
        private System.Windows.Forms.Button btnCancel;
        private System.Windows.Forms.Button btnSave;
        private System.Windows.Forms.Button btnRefresh;
        private System.Windows.Forms.Button btnDelete;
        private System.Windows.Forms.Button btnEdit;
        private System.Windows.Forms.Button btnAdd;
        private System.Windows.Forms.Panel pnlTitle;
        private System.Windows.Forms.Label lblTitle;
    }
}