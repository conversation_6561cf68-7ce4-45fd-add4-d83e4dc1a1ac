using System;
using System.Drawing;
using System.Windows.Forms;
using FontAwesome.Sharp;

namespace Awqaf_Managment.Common.Helpers
{
    /// <summary>
    /// مساعد واجهة المستخدم
    /// </summary>
    public static class UIHelper
    {
        /// <summary>
        /// تطبيق دعم RTL على النموذج
        /// </summary>
        public static void ApplyRTLSupport(Form form)
        {
            form.RightToLeft = RightToLeft.Yes;
            form.RightToLeftLayout = true;
        }

        /// <summary>
        /// تطبيق الخط العربي على عنصر التحكم وجميع عناصره الفرعية
        /// </summary>
        public static void ApplyArabicFont(Control control)
        {
            try
            {
                // استخدام خط يدعم العربية بشكل أفضل
                if (control is TreeView || control is ListBox || control is ComboBox ||
                    control is TextBox || control is Label)
                {
                    control.Font = Constants.ArabicFont;
                }
                else if (control is Button)
                {
                    control.Font = Constants.ButtonFont;
                }
                else
                {
                    control.Font = Constants.DefaultFont;
                }

                foreach (Control child in control.Controls)
                {
                    ApplyArabicFont(child);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق الخط العربي: {ex.Message}");
                // في حالة الخطأ، استخدام الخط الافتراضي
                control.Font = new Font("Arial", 12F, FontStyle.Regular);
            }
        }

        /// <summary>
        /// إنشاء زر مع أيقونة FontAwesome
        /// </summary>
        public static IconButton CreateIconButton(string text, string iconChar, Color backColor, Color foreColor)
        {
            var button = new IconButton
            {
                Text = text,
                IconChar = (IconChar)iconChar[0],
                IconColor = foreColor,
                BackColor = backColor,
                ForeColor = foreColor,
                Font = Constants.ButtonFont,
                FlatStyle = FlatStyle.Flat,
                Size = new Size(200, Constants.ButtonHeight),
                TextAlign = ContentAlignment.MiddleRight,
                ImageAlign = ContentAlignment.MiddleLeft,
                Padding = new Padding(10, 0, 20, 0),
                Cursor = Cursors.Hand
            };

            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.MouseOverBackColor = ControlPaint.Light(backColor, 0.1f);
            button.FlatAppearance.MouseDownBackColor = ControlPaint.Dark(backColor, 0.1f);

            return button;
        }

        /// <summary>
        /// إنشاء لوحة مع حدود مستديرة
        /// </summary>
        public static Panel CreateRoundedPanel(Color backColor, int borderRadius = 10)
        {
            var panel = new Panel
            {
                BackColor = backColor,
                Padding = new Padding(Constants.FormPadding)
            };

            // يمكن إضافة كود الحدود المستديرة هنا إذا لزم الأمر
            return panel;
        }

        /// <summary>
        /// عرض رسالة خطأ
        /// </summary>
        public static void ShowError(string message, string title = null)
        {
            MessageBox.Show(message, title ?? Constants.ErrorTitle, 
                MessageBoxButtons.OK, MessageBoxIcon.Error, 
                MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
        }

        /// <summary>
        /// عرض رسالة نجاح
        /// </summary>
        public static void ShowSuccess(string message, string title = null)
        {
            MessageBox.Show(message, title ?? Constants.SuccessTitle,
                MessageBoxButtons.OK, MessageBoxIcon.Information,
                MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
        }

        /// <summary>
        /// عرض رسالة تحذير
        /// </summary>
        public static void ShowWarning(string message, string title = null)
        {
            MessageBox.Show(message, title ?? Constants.WarningTitle,
                MessageBoxButtons.OK, MessageBoxIcon.Warning,
                MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
        }

        /// <summary>
        /// عرض رسالة معلومات
        /// </summary>
        public static void ShowInfo(string message, string title = null)
        {
            MessageBox.Show(message, title ?? "معلومات",
                MessageBoxButtons.OK, MessageBoxIcon.Information,
                MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
        }

        /// <summary>
        /// عرض رسالة تأكيد
        /// </summary>
        public static DialogResult ShowConfirm(string message, string title = null)
        {
            return MessageBox.Show(message, title ?? Constants.ConfirmTitle, 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question, 
                MessageBoxDefaultButton.Button2, MessageBoxOptions.RtlReading);
        }

        /// <summary>
        /// تطبيق تأثير الظل على عنصر التحكم
        /// </summary>
        public static void ApplyShadowEffect(Control control)
        {
            // يمكن تطبيق تأثير الظل هنا إذا لزم الأمر
            control.BackColor = Constants.WhiteColor;
        }

        /// <summary>
        /// تنسيق DataGridView للعربية
        /// </summary>
        public static void FormatDataGridViewForArabic(DataGridView dgv)
        {
            dgv.RightToLeft = RightToLeft.Yes;
            dgv.Font = Constants.DefaultFont;
            dgv.BackgroundColor = Constants.WhiteColor;
            dgv.BorderStyle = BorderStyle.None;
            dgv.CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal;
            dgv.DefaultCellStyle.SelectionBackColor = Constants.PrimaryColor;
            dgv.DefaultCellStyle.SelectionForeColor = Constants.WhiteColor;
            dgv.ColumnHeadersDefaultCellStyle.BackColor = Constants.SidebarColor;
            dgv.ColumnHeadersDefaultCellStyle.ForeColor = Constants.WhiteColor;
            dgv.ColumnHeadersDefaultCellStyle.Font = Constants.HeaderFont;
            dgv.EnableHeadersVisualStyles = false;
            dgv.AllowUserToAddRows = false;
            dgv.AllowUserToDeleteRows = false;
            dgv.ReadOnly = true;
            dgv.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dgv.MultiSelect = false;
        }


    }
}
