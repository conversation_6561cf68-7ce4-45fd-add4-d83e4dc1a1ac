@echo off
echo Building Awqaf Management Project...

REM Try to find MSBuild
set MSBUILD_PATH=""

REM Check for Visual Studio 2022
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
    goto :build
)

REM Check for Visual Studio 2022 Professional
if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"
    goto :build
)

REM Check for Visual Studio 2019
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
    goto :build
)

REM Check for Build Tools
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe"
    goto :build
)

echo MSBuild not found. Please install Visual Studio or Build Tools.
pause
exit /b 1

:build
echo Found MSBuild at: %MSBUILD_PATH%

REM Clean and rebuild
echo Cleaning project...
%MSBUILD_PATH% Awqaf_Managment.csproj /t:Clean /p:Configuration=Debug

echo Building project...
%MSBUILD_PATH% Awqaf_Managment.csproj /t:Build /p:Configuration=Debug

if %ERRORLEVEL% EQU 0 (
    echo Build successful!
    echo Running application...
    start "" "bin\Debug\Awqaf_Managment.exe"
) else (
    echo Build failed with error code %ERRORLEVEL%
    pause
)
