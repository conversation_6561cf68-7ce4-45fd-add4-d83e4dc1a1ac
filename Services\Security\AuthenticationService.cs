using System;
using System.Collections.Generic;
using System.Linq;
using Awqaf_Managment.DataAccess.Security;
using Awqaf_Managment.Models.Security;

namespace Awqaf_Managment.Services.Security
{
    /// <summary>
    /// خدمة المصادقة والتحقق من الهوية
    /// </summary>
    public class AuthenticationService
    {
        private static User _currentUser;
        private static List<Permission> _currentUserPermissions;
        private static DateTime _lastActivity;

        /// <summary>
        /// المستخدم الحالي
        /// </summary>
        public static User CurrentUser
        {
            get { return _currentUser; }
        }

        /// <summary>
        /// صلاحيات المستخدم الحالي
        /// </summary>
        public static List<Permission> CurrentUserPermissions
        {
            get { return _currentUserPermissions ?? new List<Permission>(); }
        }

        /// <summary>
        /// تسجيل دخول المستخدم
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <param name="password">كلمة المرور</param>
        /// <returns>نتيجة تسجيل الدخول</returns>
        public static LoginResult Login(string username, string password)
        {
            try
            {
                // التحقق من صحة البيانات المدخلة
                if (string.IsNullOrWhiteSpace(username) || string.IsNullOrWhiteSpace(password))
                {
                    return new LoginResult
                    {
                        IsSuccess = false,
                        Message = "يرجى إدخال اسم المستخدم وكلمة المرور"
                    };
                }

                // محاولة تسجيل الدخول
                var user = UserDataAccess.AuthenticateUser(username, password);

                if (user != null)
                {
                    // تحميل صلاحيات المستخدم
                    var permissions = UserDataAccess.GetUserPermissions(user.UserId);

                    // حفظ بيانات الجلسة
                    _currentUser = user;
                    _currentUserPermissions = permissions;
                    _lastActivity = DateTime.Now;

                    return new LoginResult
                    {
                        IsSuccess = true,
                        Message = "تم تسجيل الدخول بنجاح",
                        User = user
                    };
                }
                else
                {
                    return new LoginResult
                    {
                        IsSuccess = false,
                        Message = "اسم المستخدم أو كلمة المرور غير صحيحة"
                    };
                }
            }
            catch (Exception ex)
            {
                return new LoginResult
                {
                    IsSuccess = false,
                    Message = $"خطأ في تسجيل الدخول: {ex.Message}"
                };
            }
        }

        /// <summary>
        /// تسجيل خروج المستخدم
        /// </summary>
        public static void Logout()
        {
            _currentUser = null;
            _currentUserPermissions = null;
            _lastActivity = DateTime.MinValue;
        }

        /// <summary>
        /// التحقق من صحة الجلسة
        /// </summary>
        /// <returns>true إذا كانت الجلسة صالحة</returns>
        public static bool IsSessionValid()
        {
            if (_currentUser == null)
                return false;

            // التحقق من انتهاء مدة الجلسة (30 دقيقة افتراضياً)
            var sessionTimeout = TimeSpan.FromMinutes(30);
            if (DateTime.Now - _lastActivity > sessionTimeout)
            {
                Logout();
                return false;
            }

            return true;
        }

        /// <summary>
        /// تحديث وقت آخر نشاط
        /// </summary>
        public static void UpdateLastActivity()
        {
            _lastActivity = DateTime.Now;
        }

        /// <summary>
        /// التحقق من وجود صلاحية معينة للمستخدم الحالي
        /// </summary>
        /// <param name="permissionName">اسم الصلاحية</param>
        /// <param name="action">نوع العملية (View, Add, Edit, Delete, Print, Export)</param>
        /// <returns>true إذا كان المستخدم يملك الصلاحية</returns>
        public static bool HasPermission(string permissionName, PermissionAction action = PermissionAction.View)
        {
            if (!IsSessionValid())
                return false;

            var permission = _currentUserPermissions?.FirstOrDefault(p => p.PermissionName == permissionName);
            if (permission == null)
                return false;

            switch (action)
            {
                case PermissionAction.View:
                    return permission.CanView;
                case PermissionAction.Add:
                    return permission.CanAdd;
                case PermissionAction.Edit:
                    return permission.CanEdit;
                case PermissionAction.Delete:
                    return permission.CanDelete;
                case PermissionAction.Print:
                    return permission.CanPrint;
                case PermissionAction.Export:
                    return permission.CanExport;
                default:
                    return false;
            }
        }

        /// <summary>
        /// التحقق من وجود صلاحية على وحدة معينة
        /// </summary>
        /// <param name="moduleName">اسم الوحدة</param>
        /// <param name="action">نوع العملية</param>
        /// <returns>true إذا كان المستخدم يملك صلاحية على الوحدة</returns>
        public static bool HasModulePermission(string moduleName, PermissionAction action = PermissionAction.View)
        {
            if (!IsSessionValid())
                return false;

            var modulePermissions = _currentUserPermissions?.Where(p => p.ModuleName == moduleName);
            if (modulePermissions == null || !modulePermissions.Any())
                return false;

            return modulePermissions.Any(permission =>
            {
                switch (action)
                {
                    case PermissionAction.View:
                        return permission.CanView;
                    case PermissionAction.Add:
                        return permission.CanAdd;
                    case PermissionAction.Edit:
                        return permission.CanEdit;
                    case PermissionAction.Delete:
                        return permission.CanDelete;
                    case PermissionAction.Print:
                        return permission.CanPrint;
                    case PermissionAction.Export:
                        return permission.CanExport;
                    default:
                        return false;
                }
            });
        }

        /// <summary>
        /// الحصول على الوحدات المتاحة للمستخدم الحالي
        /// </summary>
        /// <returns>قائمة أسماء الوحدات</returns>
        public static List<string> GetAvailableModules()
        {
            if (!IsSessionValid())
                return new List<string>();

            return _currentUserPermissions?
                .Where(p => p.CanView)
                .Select(p => p.ModuleName)
                .Distinct()
                .ToList() ?? new List<string>();
        }
    }

    /// <summary>
    /// نتيجة عملية تسجيل الدخول
    /// </summary>
    public class LoginResult
    {
        public bool IsSuccess { get; set; }
        public string Message { get; set; }
        public User User { get; set; }
    }
}
