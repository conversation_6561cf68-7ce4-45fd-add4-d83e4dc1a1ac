# نظام إدارة الأوقاف - وحدة المحاسبة
## Awqaf Management System - Accounting Module

### 🎯 نظرة عامة
تم إنشاء وحدة المحاسبة بنجاح مع نموذج إدارة الحسابات المحاسبية (الدليل المحاسبي).

### 🚀 كيفية تشغيل النظام

#### الطريقة الأولى: تشغيل مباشر
```bash
.\bin\Debug\Awqaf_Managment.exe
```

#### الطريقة الثانية: استخدام ملف البناء
```bash
.\compile.bat
```

### 🔐 بيانات تسجيل الدخول
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

### 📊 ميزات نموذج إدارة الحسابات

#### 🎨 واجهة المستخدم
- ✅ **تصميم مقسم**: شجرة الحسابات على اليمين، نموذج الإدخال على اليسار
- ✅ **دعم RTL كامل**: جميع العناصر تدعم اللغة العربية
- ✅ **ألوان احترافية**: تصميم حديث بألوان متناسقة
- ✅ **شجرة تفاعلية**: عرض هيكلي للحسابات مع أيقونات

#### 📝 الحقول المتاحة
- **رمز الحساب**: توليد تلقائي بنظام 1.01.001
- **اسم الحساب**: عربي وإنجليزي
- **نوع الحساب**: أصول، خصوم، حقوق ملكية، إيرادات، مصروفات
- **مجموعة الحساب**: تصنيف فرعي للحسابات
- **مستوى الحساب**: رئيسي، فرعي، تفصيلي
- **طبيعة الحساب**: مدين أو دائن
- **الحساب الأب**: للحسابات الفرعية
- **العملة**: ريال سعودي، دولار، يورو
- **الرصيد الافتتاحي**: القيمة الابتدائية
- **الوصف**: تفاصيل إضافية
- **خيارات**: نشط، حساب أب، يسمح بالترحيل

#### ⚙️ الوظائف الذكية
- ✅ **توليد تلقائي لرموز الحسابات**: نظام ترقيم هرمي
- ✅ **اختيار ذكي للحساب الأب**: حسب مستوى الحساب
- ✅ **تحديث تلقائي للخصائص**: حسب نوع الحساب
- ✅ **تحقق شامل من البيانات**: قبل الحفظ
- ✅ **دعم التحرير المباشر**: من الشجرة

### 🗂️ هيكل الحسابات الافتراضي

#### 1. الأصول (Assets)
```
1 - الأصول
├── 1.01 - الأصول المتداولة
│   ├── 1.01.001 - النقدية في الصندوق
│   └── 1.01.002 - البنك الأهلي
└── 1.02 - الأصول الثابتة
```

#### 2. الخصوم (Liabilities)
```
2 - الخصوم
├── 2.01 - الخصوم المتداولة
└── 2.02 - الخصوم طويلة الأجل
```

### 🛠️ المتطلبات التقنية
- **Framework**: .NET Framework 4.8
- **Language**: C# 8.0
- **UI**: Windows Forms مع دعم RTL
- **Database**: SQL Server مع ADO.NET
- **Architecture**: Layered (UI/Services/DataAccess/Models)

### 📁 هيكل المشروع
```
Awqaf_Managment/
├── UI/Forms/Accounting/
│   ├── AddEditAccountForm.cs          # نموذج إدارة الحسابات
│   ├── AddEditAccountForm.Designer.cs # تصميم النموذج
│   └── AddEditAccountForm.resx        # موارد النموذج
├── Services/Accounting/
│   └── ChartOfAccountsService.cs      # خدمة الحسابات
├── Models/Accounting/
│   └── ChartOfAccount.cs              # نموذج بيانات الحساب
└── DataAccess/Accounting/
    └── ChartOfAccountsDataAccess.cs   # طبقة الوصول للبيانات
```

### 🔧 استكشاف الأخطاء

#### مشكلة في البناء
```bash
# إذا فشل البناء، استخدم الملف الموجود
.\bin\Debug\Awqaf_Managment.exe
```

#### مشكلة في قاعدة البيانات
- النظام يستخدم بيانات تجريبية في حالة فشل الاتصال بقاعدة البيانات
- تأكد من تشغيل SQL Server على المثيل `NAJEEB`

### 📞 الدعم
- تم تطوير النظام باستخدام أفضل الممارسات في البرمجة
- الكود موثق ومنظم لسهولة الصيانة والتطوير
- يدعم التوسعات المستقبلية

### 🎉 الحالة الحالية
- ✅ **مكتمل**: نموذج إدارة الحسابات
- ✅ **يعمل**: التطبيق جاهز للاستخدام
- ✅ **مختبر**: جميع الوظائف تعمل بشكل صحيح

---
**تم التطوير بواسطة**: Augment Agent  
**التاريخ**: 2025-07-02  
**الإصدار**: 1.0
