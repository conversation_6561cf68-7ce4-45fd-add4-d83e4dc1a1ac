using System;

namespace Awqaf_Managment.Models.Accounting
{
    /// <summary>
    /// نموذج بيانات نوع الحساب
    /// Account Type Data Model
    /// </summary>
    public class AccountType
    {
        #region Properties
        /// <summary>
        /// معرف نوع الحساب
        /// Account Type ID
        /// </summary>
        public int AccountTypeId { get; set; }

        /// <summary>
        /// اسم نوع الحساب بالعربية
        /// Account Type Name in Arabic
        /// </summary>
        public string TypeNameAr { get; set; } = string.Empty;

        /// <summary>
        /// اسم نوع الحساب بالإنجليزية
        /// Account Type Name in English
        /// </summary>
        public string TypeNameEn { get; set; } = string.Empty;

        /// <summary>
        /// رمز نوع الحساب
        /// Account Type Code
        /// </summary>
        public string TypeCode { get; set; } = string.Empty;

        /// <summary>
        /// الوصف
        /// Description
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// طبيعة الحساب الافتراضية
        /// Default Account Nature
        /// </summary>
        public AccountNature DefaultNature { get; set; } = AccountNature.Debit;

        /// <summary>
        /// حالة النشاط
        /// Active Status
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// ترتيب العرض
        /// Display Order
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// تاريخ الإنشاء
        /// Creation Date
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تعديل
        /// Last Modified Date
        /// </summary>
        public DateTime? ModifiedDate { get; set; }

        /// <summary>
        /// معرف المستخدم المنشئ
        /// Created By User ID
        /// </summary>
        public int CreatedBy { get; set; }

        /// <summary>
        /// معرف المستخدم المعدل
        /// Modified By User ID
        /// </summary>
        public int? ModifiedBy { get; set; }

        /// <summary>
        /// رمز نوع الحساب (للتوافق مع الإصدارات القديمة)
        /// Account Type Code (for backward compatibility)
        /// </summary>
        public string AccountTypeCode { get; set; } = string.Empty;

        /// <summary>
        /// اسم نوع الحساب (للتوافق مع الإصدارات القديمة)
        /// Account Type Name (for backward compatibility)
        /// </summary>
        public string AccountTypeName { get; set; } = string.Empty;

        /// <summary>
        /// اسم نوع الحساب بالعربية (للتوافق مع الإصدارات القديمة)
        /// Account Type Name in Arabic (for backward compatibility)
        /// </summary>
        public string AccountTypeNameAr { get; set; } = string.Empty;

        /// <summary>
        /// اسم نوع الحساب بالإنجليزية (للتوافق مع الإصدارات القديمة)
        /// Account Type Name in English (for backward compatibility)
        /// </summary>
        public string AccountTypeNameEn { get; set; } = string.Empty;

        /// <summary>
        /// طبيعة الحساب (للتوافق مع الإصدارات القديمة)
        /// Account Nature (for backward compatibility)
        /// </summary>
        public AccountNature Nature => DefaultNature;
        #endregion

        #region Constructor
        /// <summary>
        /// منشئ افتراضي
        /// Default Constructor
        /// </summary>
        public AccountType()
        {
            CreatedDate = DateTime.Now;
            IsActive = true;
            DefaultNature = AccountNature.Debit;
        }

        /// <summary>
        /// منشئ بمعاملات
        /// Parameterized Constructor
        /// </summary>
        /// <param name="typeNameAr">اسم النوع بالعربية</param>
        /// <param name="typeCode">رمز النوع</param>
        /// <param name="defaultNature">الطبيعة الافتراضية</param>
        public AccountType(string typeNameAr, string typeCode, AccountNature defaultNature = AccountNature.Debit)
        {
            TypeNameAr = typeNameAr;
            TypeCode = typeCode;
            DefaultNature = defaultNature;
            CreatedDate = DateTime.Now;
            IsActive = true;
        }
        #endregion

        #region Methods
        /// <summary>
        /// التحقق من صحة بيانات نوع الحساب
        /// Validate account type data
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(TypeNameAr) &&
                   !string.IsNullOrWhiteSpace(TypeCode);
        }

        /// <summary>
        /// إرجاع تمثيل نصي لنوع الحساب
        /// Return string representation of account type
        /// </summary>
        /// <returns>النص الممثل لنوع الحساب</returns>
        public override string ToString()
        {
            return $"{TypeCode} - {TypeNameAr}";
        }

        /// <summary>
        /// مقارنة أنواع الحسابات
        /// Compare account types
        /// </summary>
        /// <param name="obj">الكائن للمقارنة</param>
        /// <returns>true إذا كانت الأنواع متطابقة</returns>
        public override bool Equals(object obj)
        {
            AccountType other = obj as AccountType;
            if (other != null)
            {
                return AccountTypeId == other.AccountTypeId ||
                       (AccountTypeId == 0 && other.AccountTypeId == 0 &&
                        TypeCode.Equals(other.TypeCode, StringComparison.OrdinalIgnoreCase));
            }
            return false;
        }

        /// <summary>
        /// الحصول على رمز التجمع
        /// Get hash code
        /// </summary>
        /// <returns>رمز التجمع</returns>
        public override int GetHashCode()
        {
            return AccountTypeId != 0 ? AccountTypeId.GetHashCode() : TypeCode.GetHashCode();
        }
        #endregion
    }
}
