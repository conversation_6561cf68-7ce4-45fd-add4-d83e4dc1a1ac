# نظام إدارة الأوقاف - Awqaf Management System

نظام شامل لإدارة الأوقاف يدمج إدارة العقارات (البيع والإيجار) مع المحاسبة المالية الشاملة، مع دعم اللغة العربية والاتجاه من اليمين إلى اليسار.

## المتطلبات التقنية

### البرمجيات المطلوبة
- **Visual Studio 2019** أو أحدث
- **.NET Framework 4.7.2** أو أحدث
- **SQL Server 2014** أو أحدث
- **Windows 10** أو أحدث

### المكتبات المستخدمة
- **FontAwesome.Sharp** - للأيقونات الحديثة
- **System.Data.SqlClient** - للاتصال بقاعدة البيانات

## الهيكل المعماري

النظام مبني على **هيكل الطبقات (Layered Architecture)**:

```
UI Layer (طبقة الواجهة)
├── Forms/ - النماذج الرئيسية
├── UserControls/ - عناصر التحكم المخصصة
└── Common/ - المساعدات والثوابت

Services Layer (طبقة الخدمات)
├── Security/ - خدمات الأمان والمصادقة
├── Business/ - منطق العمل
└── Validation/ - التحقق من صحة البيانات

DataAccess Layer (طبقة الوصول للبيانات)
├── Security/ - وصول بيانات الأمان
├── Properties/ - وصول بيانات العقارات
└── Common/ - الاتصال بقاعدة البيانات

Models Layer (طبقة النماذج)
├── Security/ - نماذج الأمان
├── Properties/ - نماذج العقارات
└── Common/ - النماذج المشتركة
```

## الوحدات الرئيسية

### 1. وحدة الأمان والمصادقة ✅
- **تسجيل الدخول والخروج**
- **إدارة المستخدمين**
- **إدارة الأدوار والصلاحيات**
- **نظام صلاحيات متقدم (RBAC)**
- **تشفير كلمات المرور**
- **سجل تسجيل الدخول**

### 2. إدارة العقارات 🔄
- إدارة بيانات العقارات
- تصنيف العقارات (سكني، تجاري، أراضي)
- إدارة الوحدات السكنية
- متابعة حالة العقارات

### 3. إدارة العملاء 🔄
- بيانات العملاء الشخصية
- تصنيف العملاء (مشترين، مستأجرين)
- سجل التعاملات
- معلومات الاتصال

### 4. إدارة العقود 🔄
- عقود البيع
- عقود الإيجار
- الشروط والأحكام
- متابعة انتهاء العقود

### 5. إدارة المدفوعات 🔄
- تسجيل المدفوعات
- إصدار الإيصالات
- متابعة المستحقات
- تقارير المدفوعات

### 6. المحاسبة المالية 🔄
- دليل الحسابات
- القيود المحاسبية
- الميزانية العمومية
- قائمة الدخل

### 7. إدارة المخازن 🔄
- إدارة المواد والأصناف
- حركات المخزون
- جرد المخازن
- تقارير المخزون

### 8. إدارة الموظفين 🔄
- بيانات الموظفين
- الرواتب والمكافآت
- الإجازات والغياب
- تقييم الأداء

### 9. إدارة الأصول 🔄
- الأصول الثابتة
- الاستهلاك
- الصيانة
- التأمين

### 10. التقارير 🔄
- تقارير العقارات
- التقارير المالية
- تقارير العملاء
- تقارير مخصصة

### 11. الإعدادات 🔄
- إعدادات النظام العامة
- إعدادات قاعدة البيانات
- إعدادات التقارير
- النسخ الاحتياطي

## إعداد قاعدة البيانات

### 1. تشغيل السكريبت الشامل
```sql
-- تشغيل الملف التالي في SQL Server Management Studio
Database/RunAllScripts.sql
```

### 2. أو تشغيل السكريبتات منفصلة
```sql
-- 1. إنشاء قاعدة البيانات
Database/01_CreateDatabase.sql

-- 2. إنشاء جداول الأمان
Database/02_SecurityTables.sql

-- 3. إدراج البيانات الأساسية
Database/03_InitialData.sql

-- 4. إنشاء الإجراءات المخزنة
Database/04_StoredProcedures.sql
```

## بيانات تسجيل الدخول الافتراضية

```
اسم المستخدم: admin
كلمة المرور: admin123
```

## الميزات التقنية

### دعم اللغة العربية
- **واجهة مستخدم RTL** (من اليمين إلى اليسار)
- **خط Tahoma 12pt** لوضوح النص العربي
- **ترتيب عربي** في قاعدة البيانات (Arabic_CI_AS)

### الأمان
- **تشفير كلمات المرور** باستخدام SHA256 + Salt
- **نظام صلاحيات متقدم** مع تحكم دقيق
- **قفل الحساب** بعد محاولات فاشلة
- **سجل تسجيل الدخول** لمراقبة النشاط

### الواجهة
- **تصميم حديث** مع ألوان احترافية
- **أيقونات FontAwesome** للوضوح
- **تنقل سهل** بين الوحدات
- **رسائل تأكيد** باللغة العربية

## التطوير والصيانة

### إضافة وحدة جديدة
1. إنشاء النماذج في `Models/`
2. إنشاء طبقة الوصول للبيانات في `DataAccess/`
3. إنشاء طبقة الخدمات في `Services/`
4. إنشاء الواجهات في `UI/Forms/`
5. تحديث الصلاحيات في قاعدة البيانات

### إضافة صلاحية جديدة
1. إضافة الصلاحية في جدول `Permissions`
2. ربطها بالأدوار المناسبة في `RolePermissions`
3. التحقق منها في الكود باستخدام `AuthenticationService.HasPermission()`

## الدعم والمساعدة

### المشاكل الشائعة

**خطأ في الاتصال بقاعدة البيانات:**
- تأكد من تشغيل SQL Server
- تحقق من مسار قاعدة البيانات في `App.config`
- تأكد من صلاحيات المستخدم

**مشاكل في عرض النص العربي:**
- تأكد من تثبيت خط Tahoma
- تحقق من إعدادات RTL في النماذج

**مشاكل في تسجيل الدخول:**
- تأكد من تشغيل سكريبت البيانات الأساسية
- تحقق من بيانات المستخدم الافتراضي

## الحالة الحالية

- ✅ **مكتمل**: وحدة الأمان والمصادقة
- ✅ **مكتمل**: الهيكل الأساسي للمشروع
- ✅ **مكتمل**: قاعدة البيانات الأساسية
- 🔄 **قيد التطوير**: باقي الوحدات

## المطورون

تم تطوير هذا النظام باستخدام:
- **C# WinForms** للواجهة
- **SQL Server** لقاعدة البيانات
- **ADO.NET** للوصول للبيانات
- **FontAwesome.Sharp** للأيقونات

---

**ملاحظة**: هذا النظام مصمم خصيصاً لإدارة الأوقاف مع مراعاة المتطلبات الشرعية والقانونية.
