using System.Drawing;

namespace Awqaf_Managment.Common
{
    /// <summary>
    /// ثوابت النظام العامة
    /// </summary>
    public static class Constants
    {
        #region ألوان النظام
        public static readonly Color PrimaryColor = Color.FromArgb(33, 150, 243);      // #2196F3
        public static readonly Color SecondaryColor = Color.FromArgb(255, 193, 7);     // #FFC107
        public static readonly Color BackgroundColor = Color.FromArgb(245, 245, 245);  // #F5F5F5
        public static readonly Color SidebarColor = Color.FromArgb(52, 73, 94);        // #34495E
        public static readonly Color TextColor = Color.FromArgb(44, 62, 80);           // #2C3E50
        public static readonly Color SuccessColor = Color.FromArgb(76, 175, 80);       // #4CAF50
        public static readonly Color WarningColor = Color.FromArgb(255, 152, 0);       // #FF9800
        public static readonly Color ErrorColor = Color.FromArgb(244, 67, 54);         // #F44336
        public static readonly Color WhiteColor = Color.White;
        #endregion

        #region خطوط النظام
        public static readonly Font DefaultFont = new Font("Segoe UI", 12F, FontStyle.Regular);
        public static readonly Font HeaderFont = new Font("Segoe UI", 16F, FontStyle.Bold);
        public static readonly Font ButtonFont = new Font("Segoe UI", 11F, FontStyle.Regular);
        public static readonly Font TitleFont = new Font("Segoe UI", 14F, FontStyle.Bold);
        public static readonly Font ArabicFont = new Font("Arial Unicode MS", 12F, FontStyle.Regular);
        public static readonly Font ArabicHeaderFont = new Font("Arial Unicode MS", 16F, FontStyle.Bold);
        #endregion

        #region أحجام النظام
        public const int SidebarWidth = 250;
        public const int HeaderHeight = 80;
        public const int ButtonHeight = 45;
        public const int FormPadding = 20;
        public const int ControlSpacing = 15;
        #endregion

        #region رسائل النظام
        public const string AppTitle = "نظام إدارة الأوقاف";
        public const string LoginTitle = "تسجيل الدخول";
        public const string WelcomeMessage = "مرحباً بك في نظام إدارة الأوقاف";
        public const string ErrorTitle = "خطأ";
        public const string SuccessTitle = "نجح";
        public const string WarningTitle = "تحذير";
        public const string ConfirmTitle = "تأكيد";
        #endregion

        #region أيقونات النظام (FontAwesome)
        public const string IconHome = "\uf015";           // 🏠
        public const string IconBuilding = "\uf1ad";       // 🏢
        public const string IconUsers = "\uf0c0";          // 👥
        public const string IconFileContract = "\uf56c";   // 📄
        public const string IconMoneyBill = "\uf0d6";      // 💰
        public const string IconCalculator = "\uf1ec";     // 🧾
        public const string IconBoxes = "\uf468";          // 📦
        public const string IconUserTie = "\uf508";        // 🧑‍💼
        public const string IconLock = "\uf023";           // 🔒
        public const string IconChartLine = "\uf201";      // 📈
        public const string IconCog = "\uf013";            // ⚙️
        public const string IconSignOut = "\uf08b";        // 🚪
        public const string IconPlus = "\uf067";           // ➕
        public const string IconEdit = "\uf044";           // ✏️
        public const string IconTrash = "\uf1f8";          // 🗑️
        public const string IconSave = "\uf0c7";           // 💾
        public const string IconPrint = "\uf02f";          // 🖨️
        public const string IconSearch = "\uf002";         // 🔍
        #endregion

        #region أدوار المستخدمين
        public const string RoleAdmin = "مدير عام";
        public const string RoleAccountant = "محاسب";
        public const string RolePropertyManager = "مدير عقار";
        public const string RoleEmployee = "موظف";
        #endregion
    }
}
