@echo off
echo Building Awqaf Management System...

REM Try to find MSBuild
set MSBUILD_PATH=""

REM Check for Visual Studio 2022 BuildTools
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\Bin\MSBuild.exe"
    goto :build
)

REM Check for Visual Studio 2022 Professional/Community
if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"
    goto :build
)

if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
    goto :build
)

REM Check for Visual Studio 2019
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe"
    goto :build
)

REM Check for .NET Framework MSBuild
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe"
    goto :build
)

echo MSBuild not found. Please install Visual Studio or Build Tools.
echo You can download Build Tools from: https://visualstudio.microsoft.com/downloads/#build-tools-for-visual-studio-2022
pause
exit /b 1

:build
echo Found MSBuild at: %MSBUILD_PATH%
echo.

REM Restore NuGet packages first
echo Restoring NuGet packages...
nuget restore packages.config -PackagesDirectory packages
if errorlevel 1 (
    echo Warning: NuGet restore failed. Continuing with build...
)

echo.
echo Building project...
%MSBUILD_PATH% Awqaf_Managment.csproj /p:Configuration=Debug /p:Platform="Any CPU" /verbosity:minimal

if errorlevel 1 (
    echo.
    echo Build failed! Please check the errors above.
    pause
    exit /b 1
) else (
    echo.
    echo Build successful!
    echo You can now run the application from: bin\Debug\Awqaf_Managment.exe
    echo.
    echo To run the application now, press any key...
    pause >nul
    start bin\Debug\Awqaf_Managment.exe
)
