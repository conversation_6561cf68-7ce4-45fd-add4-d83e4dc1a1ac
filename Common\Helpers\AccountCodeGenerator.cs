using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using Awqaf_Managment.Models.Accounting;

namespace Awqaf_Managment.Common.Helpers
{
    /// <summary>
    /// مولد أرقام الحسابات
    /// Account Code Generator
    /// </summary>
    public static class AccountCodeGenerator
    {
        /// <summary>
        /// توليد رمز حساب جديد بناءً على النوع والمستوى والحساب الأب
        /// Generate new account code based on type, level, and parent account
        /// </summary>
        /// <param name="accountTypeId">معرف نوع الحساب</param>
        /// <param name="levelType">مستوى الحساب</param>
        /// <param name="parentAccountId">معرف الحساب الأب (اختياري)</param>
        /// <param name="existingAccounts">قائمة الحسابات الموجودة</param>
        /// <returns>رمز الحساب الجديد</returns>
        public static string GenerateAccountCode(int accountTypeId, AccountLevelType levelType,
            int? parentAccountId, List<ChartOfAccount> existingAccounts)
        {
            try
            {
                switch (levelType)
                {
                    case AccountLevelType.Main:
                        return GenerateMainAccountCode(accountTypeId, existingAccounts);

                    case AccountLevelType.Sub:
                        return GenerateSubAccountCode(parentAccountId, existingAccounts);

                    case AccountLevelType.Detail:
                        return GenerateDetailAccountCode(parentAccountId, existingAccounts);

                    default:
                        throw new ArgumentException($"مستوى الحساب غير مدعوم: {levelType}");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في توليد رمز الحساب: {ex.Message}");
            }
        }

        /// <summary>
        /// توليد رمز حساب رئيسي (نمط: X.00.000)
        /// Generate main account code (pattern: X.00.000)
        /// </summary>
        private static string GenerateMainAccountCode(int accountTypeId, List<ChartOfAccount> existingAccounts)
        {
            // الحصول على أكبر رقم رئيسي موجود لنفس النوع
            var maxMainCode = existingAccounts
                .Where(a => a.LevelType == AccountLevelType.Main && a.AccountTypeId == accountTypeId)
                .Select(a => GetMainCodeNumber(a.AccountCode))
                .DefaultIfEmpty(0)
                .Max();

            var newMainCode = maxMainCode + 1;
            return $"{newMainCode}.00.000";
        }

        /// <summary>
        /// توليد رمز حساب فرعي (نمط: X.YY.000)
        /// Generate sub account code (pattern: X.YY.000)
        /// </summary>
        private static string GenerateSubAccountCode(int? parentAccountId, List<ChartOfAccount> existingAccounts)
        {
            if (!parentAccountId.HasValue || parentAccountId.Value == 0)
                throw new ArgumentException("يجب تحديد الحساب الأب للحساب الفرعي");

            var parentAccount = existingAccounts.FirstOrDefault(a => a.AccountId == parentAccountId.Value);
            if (parentAccount == null)
                throw new ArgumentException("الحساب الأب غير موجود");

            if (parentAccount.LevelType != AccountLevelType.Main)
                throw new ArgumentException("الحساب الأب يجب أن يكون حساباً رئيسياً");

            var parentCodeParts = parentAccount.AccountCode.Split('.');
            if (parentCodeParts.Length != 3)
                throw new ArgumentException("تنسيق رمز الحساب الأب غير صحيح");

            var mainCode = parentCodeParts[0];

            // الحصول على أكبر رقم فرعي موجود تحت نفس الحساب الرئيسي
            var maxSubCode = existingAccounts
                .Where(a => a.LevelType == AccountLevelType.Sub &&
                           a.ParentAccountId == parentAccountId.Value)
                .Select(a => GetSubCodeNumber(a.AccountCode))
                .DefaultIfEmpty(0)
                .Max();

            var newSubCode = maxSubCode + 1;
            return $"{mainCode}.{newSubCode:00}.000";
        }

        /// <summary>
        /// توليد رمز حساب تفصيلي (نمط: X.YY.ZZZ)
        /// Generate detail account code (pattern: X.YY.ZZZ)
        /// </summary>
        private static string GenerateDetailAccountCode(int? parentAccountId, List<ChartOfAccount> existingAccounts)
        {
            if (!parentAccountId.HasValue || parentAccountId.Value == 0)
                throw new ArgumentException("يجب تحديد الحساب الأب للحساب التفصيلي");

            var parentAccount = existingAccounts.FirstOrDefault(a => a.AccountId == parentAccountId.Value);
            if (parentAccount == null)
                throw new ArgumentException("الحساب الأب غير موجود");

            if (parentAccount.LevelType != AccountLevelType.Sub && parentAccount.LevelType != AccountLevelType.Main)
                throw new ArgumentException("الحساب الأب يجب أن يكون حساباً رئيسياً أو فرعياً");

            var parentCodeParts = parentAccount.AccountCode.Split('.');
            if (parentCodeParts.Length != 3)
                throw new ArgumentException("تنسيق رمز الحساب الأب غير صحيح");

            var mainCode = parentCodeParts[0];
            var subCode = parentCodeParts[1];

            // الحصول على أكبر رقم تفصيلي موجود تحت نفس الحساب الأب
            var maxDetailCode = existingAccounts
                .Where(a => a.LevelType == AccountLevelType.Detail &&
                           a.ParentAccountId == parentAccountId.Value)
                .Select(a => GetDetailCodeNumber(a.AccountCode))
                .DefaultIfEmpty(0)
                .Max();

            var newDetailCode = maxDetailCode + 1;
            return $"{mainCode}.{subCode}.{newDetailCode:000}";
        }

        /// <summary>
        /// استخراج رقم الحساب الرئيسي من رمز الحساب
        /// Extract main account number from account code
        /// </summary>
        private static int GetMainCodeNumber(string accountCode)
        {
            if (string.IsNullOrEmpty(accountCode))
                return 0;

            var parts = accountCode.Split('.');
            if (parts.Length >= 1 && int.TryParse(parts[0], out int mainCode))
                return mainCode;

            return 0;
        }

        /// <summary>
        /// استخراج رقم الحساب الفرعي من رمز الحساب
        /// Extract sub account number from account code
        /// </summary>
        private static int GetSubCodeNumber(string accountCode)
        {
            if (string.IsNullOrEmpty(accountCode))
                return 0;

            var parts = accountCode.Split('.');
            if (parts.Length >= 2 && int.TryParse(parts[1], out int subCode))
                return subCode;

            return 0;
        }

        /// <summary>
        /// استخراج رقم الحساب التفصيلي من رمز الحساب
        /// Extract detail account number from account code
        /// </summary>
        private static int GetDetailCodeNumber(string accountCode)
        {
            if (string.IsNullOrEmpty(accountCode))
                return 0;

            var parts = accountCode.Split('.');
            if (parts.Length >= 3 && int.TryParse(parts[2], out int detailCode))
                return detailCode;

            return 0;
        }

        /// <summary>
        /// توليد الرقم التالي للحساب
        /// Generate next account code
        /// </summary>
        /// <param name="parentCode">رقم الحساب الأب</param>
        /// <param name="existingCodes">الأرقام الموجودة</param>
        /// <returns>الرقم الجديد</returns>
        public static string GenerateNextCode(string parentCode, List<string> existingCodes)
        {
            try
            {
                if (string.IsNullOrEmpty(parentCode))
                {
                    // حساب رئيسي - ابدأ من 1
                    return GenerateMainAccountCode(existingCodes);
                }
                else
                {
                    // حساب فرعي - أضف إلى الحساب الأب
                    return GenerateSubAccountCode(parentCode, existingCodes);
                }
            }
            catch (Exception)
            {
                // في حالة الخطأ، أرجع رقم افتراضي
                return "1.01.001";
            }
        }

        /// <summary>
        /// توليد رقم حساب رئيسي
        /// Generate main account code
        /// </summary>
        /// <param name="existingCodes">الأرقام الموجودة</param>
        /// <returns>رقم الحساب الرئيسي</returns>
        private static string GenerateMainAccountCode(List<string> existingCodes)
        {
            var mainCodes = existingCodes
                .Where(code => IsMainAccountCode(code))
                .Select(code => ExtractMainNumber(code))
                .Where(num => num > 0)
                .OrderBy(num => num)
                .ToList();

            int nextNumber = 1;
            if (mainCodes.Any())
            {
                nextNumber = mainCodes.Max() + 1;
            }

            return $"{nextNumber}.01.001";
        }

        /// <summary>
        /// توليد رقم حساب فرعي
        /// Generate sub account code
        /// </summary>
        /// <param name="parentCode">رقم الحساب الأب</param>
        /// <param name="existingCodes">الأرقام الموجودة</param>
        /// <returns>رقم الحساب الفرعي</returns>
        private static string GenerateSubAccountCode(string parentCode, List<string> existingCodes)
        {
            var parts = parentCode.Split('.');
            if (parts.Length != 3)
            {
                throw new ArgumentException("رقم الحساب الأب غير صحيح");
            }

            var mainPart = parts[0];
            var subPart = parts[1];
            var detailPart = parts[2];

            // البحث عن الأرقام الفرعية الموجودة
            var pattern = $@"^{Regex.Escape(mainPart)}\.{Regex.Escape(subPart)}\.(\d+)$";
            var subCodes = existingCodes
                .Where(code => Regex.IsMatch(code, pattern))
                .Select(code => ExtractDetailNumber(code))
                .Where(num => num > 0)
                .OrderBy(num => num)
                .ToList();

            int nextNumber = 1;
            if (subCodes.Any())
            {
                nextNumber = subCodes.Max() + 1;
            }

            return $"{mainPart}.{subPart}.{nextNumber:D3}";
        }

        /// <summary>
        /// التحقق من صحة رقم الحساب
        /// Validate account code
        /// </summary>
        /// <param name="code">رقم الحساب</param>
        /// <param name="parentCode">رقم الحساب الأب</param>
        /// <returns>true إذا كان الرقم صحيح</returns>
        public static bool ValidateAccountCode(string code, string parentCode = "")
        {
            if (string.IsNullOrWhiteSpace(code))
                return false;

            // التحقق من النمط العام: X.XX.XXX
            var pattern = @"^\d{1,2}\.\d{2}\.\d{3}$";
            if (!Regex.IsMatch(code, pattern))
                return false;

            // إذا كان هناك حساب أب، تحقق من التطابق
            if (!string.IsNullOrEmpty(parentCode))
            {
                var codeParts = code.Split('.');
                var parentParts = parentCode.Split('.');

                if (codeParts.Length != 3 || parentParts.Length != 3)
                    return false;

                // يجب أن يبدأ الرقم بنفس الجزء الرئيسي والفرعي للحساب الأب
                return codeParts[0] == parentParts[0] && codeParts[1] == parentParts[1];
            }

            return true;
        }

        /// <summary>
        /// التحقق من كون الرقم حساب رئيسي
        /// Check if code is main account
        /// </summary>
        /// <param name="code">رقم الحساب</param>
        /// <returns>true إذا كان حساب رئيسي</returns>
        private static bool IsMainAccountCode(string code)
        {
            var pattern = @"^\d{1,2}\.01\.001$";
            return Regex.IsMatch(code, pattern);
        }

        /// <summary>
        /// استخراج الرقم الرئيسي
        /// Extract main number
        /// </summary>
        /// <param name="code">رقم الحساب</param>
        /// <returns>الرقم الرئيسي</returns>
        private static int ExtractMainNumber(string code)
        {
            var parts = code.Split('.');
            if (parts.Length >= 1 && int.TryParse(parts[0], out int number))
            {
                return number;
            }
            return 0;
        }

        /// <summary>
        /// استخراج الرقم التفصيلي
        /// Extract detail number
        /// </summary>
        /// <param name="code">رقم الحساب</param>
        /// <returns>الرقم التفصيلي</returns>
        private static int ExtractDetailNumber(string code)
        {
            var parts = code.Split('.');
            if (parts.Length >= 3 && int.TryParse(parts[2], out int number))
            {
                return number;
            }
            return 0;
        }

        /// <summary>
        /// الحصول على مستوى الحساب من الرقم
        /// Get account level from code
        /// </summary>
        /// <param name="code">رقم الحساب</param>
        /// <returns>مستوى الحساب</returns>
        public static int GetAccountLevel(string code)
        {
            if (string.IsNullOrEmpty(code))
                return 0;

            var parts = code.Split('.');
            if (parts.Length != 3)
                return 0;

            // إذا كان الجزء الثاني والثالث "01" و "001" فهو حساب رئيسي (مستوى 1)
            if (parts[1] == "01" && parts[2] == "001")
                return 1;

            // إذا كان الجزء الثالث "001" فهو حساب فرعي (مستوى 2)
            if (parts[2] == "001")
                return 2;

            // وإلا فهو حساب تفصيلي (مستوى 3)
            return 3;
        }

        /// <summary>
        /// الحصول على رقم الحساب الأب
        /// Get parent account code
        /// </summary>
        /// <param name="code">رقم الحساب</param>
        /// <returns>رقم الحساب الأب</returns>
        public static string GetParentAccountCode(string code)
        {
            if (string.IsNullOrEmpty(code))
                return string.Empty;

            var parts = code.Split('.');
            if (parts.Length != 3)
                return string.Empty;

            var level = GetAccountLevel(code);
            switch (level)
            {
                case 1: // حساب رئيسي - لا يوجد أب
                    return string.Empty;
                case 2: // حساب فرعي - الأب هو الحساب الرئيسي
                    return $"{parts[0]}.01.001";
                case 3: // حساب تفصيلي - الأب هو الحساب الفرعي
                    return $"{parts[0]}.{parts[1]}.001";
                default:
                    return string.Empty;
            }
        }
    }
}
