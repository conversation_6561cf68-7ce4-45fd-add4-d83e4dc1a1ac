using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using Awqaf_Managment.Models.Accounting;
using Services.Accounting;

namespace DataAccess.Accounting
{
    public static class JournalEntryDataAccess
    {
        private static readonly string ConnectionString = "Server=NAJEEB;Database=AwqafManagement;Integrated Security=true;Charset=UTF8;";

        #region Get Methods

        /// <summary>
        /// الحصول على جميع القيود اليومية
        /// Get all journal entries
        /// </summary>
        /// <returns>قائمة القيود اليومية</returns>
        public static List<JournalEntry> GetAllJournalEntries()
        {
            var journalEntries = new List<JournalEntry>();

            try
            {
                using (var connection = new SqlConnection(ConnectionString))
                {
                    connection.Open();
                    var query = @"
                        SELECT JournalEntryId, JournalNumber, JournalDate, GeneralDescription,
                               TotalDebit, TotalCredit, Status, PostedDate, PostedBy,
                               CreatedDate, CreatedBy, ModifiedDate, ModifiedBy
                        FROM JournalEntries
                        ORDER BY JournalDate DESC, JournalNumber DESC";

                    using (var command = new SqlCommand(query, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var journalEntry = new JournalEntry
                            {
                                JournalEntryId = Convert.ToInt32(reader["JournalEntryId"]),
                                JournalNumber = reader["JournalNumber"]?.ToString() ?? "",
                                JournalDate = Convert.ToDateTime(reader["JournalDate"]),
                                Description = reader["GeneralDescription"]?.ToString() ?? "",
                                TotalDebit = reader["TotalDebit"] == DBNull.Value ? 0 : Convert.ToDecimal(reader["TotalDebit"]),
                                TotalCredit = reader["TotalCredit"] == DBNull.Value ? 0 : Convert.ToDecimal(reader["TotalCredit"]),
                                Status = reader["Status"] == DBNull.Value ? JournalStatus.Draft : (JournalStatus)Convert.ToInt32(reader["Status"]),
                                CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                                CreatedBy = reader["CreatedBy"]?.ToString() ?? "",
                                ModifiedDate = reader["ModifiedDate"] == DBNull.Value ? null : (DateTime?)Convert.ToDateTime(reader["ModifiedDate"]),
                                ModifiedBy = reader["ModifiedBy"]?.ToString() ?? ""
                            };

                            journalEntries.Add(journalEntry);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على القيود اليومية: {ex.Message}", ex);
            }

            return journalEntries;
        }

        /// <summary>
        /// الحصول على قيد يومي بالمعرف
        /// Get journal entry by ID
        /// </summary>
        /// <param name="journalEntryId">معرف القيد</param>
        /// <returns>القيد اليومي</returns>
        public static JournalEntry GetJournalEntryById(int journalEntryId)
        {
            try
            {
                using (var connection = new SqlConnection(ConnectionString))
                {
                    connection.Open();
                    var query = @"
                        SELECT JournalEntryId, JournalNumber, JournalDate, GeneralDescription,
                               TotalDebit, TotalCredit, Status, PostedDate, PostedBy,
                               CreatedDate, CreatedBy, ModifiedDate, ModifiedBy
                        FROM JournalEntries
                        WHERE JournalEntryId = @JournalEntryId";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@JournalEntryId", journalEntryId);

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return new JournalEntry
                                {
                                    JournalEntryId = Convert.ToInt32(reader["JournalEntryId"]),
                                    JournalNumber = reader["JournalNumber"]?.ToString() ?? "",
                                    JournalDate = Convert.ToDateTime(reader["JournalDate"]),
                                    Description = reader["GeneralDescription"]?.ToString() ?? "",
                                    TotalDebit = reader["TotalDebit"] == DBNull.Value ? 0 : Convert.ToDecimal(reader["TotalDebit"]),
                                    TotalCredit = reader["TotalCredit"] == DBNull.Value ? 0 : Convert.ToDecimal(reader["TotalCredit"]),
                                    Status = reader["Status"] == DBNull.Value ? JournalStatus.Draft : (JournalStatus)Convert.ToInt32(reader["Status"]),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                                    CreatedBy = reader["CreatedBy"]?.ToString() ?? "",
                                    ModifiedDate = reader["ModifiedDate"] == DBNull.Value ? null : (DateTime?)Convert.ToDateTime(reader["ModifiedDate"]),
                                    ModifiedBy = reader["ModifiedBy"]?.ToString() ?? ""
                                };
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على القيد اليومي: {ex.Message}", ex);
            }

            return null;
        }

        /// <summary>
        /// الحصول على تفاصيل القيد اليومي
        /// Get journal entry details
        /// </summary>
        /// <param name="journalEntryId">معرف القيد</param>
        /// <returns>قائمة تفاصيل القيد</returns>
        public static List<JournalEntryDetail> GetJournalEntryDetails(int journalEntryId)
        {
            var details = new List<JournalEntryDetail>();

            try
            {
                using (var connection = new SqlConnection(ConnectionString))
                {
                    connection.Open();
                    var query = @"
                        SELECT jed.JournalEntryDetailId, jed.JournalEntryId, jed.AccountId, 
                               jed.LineNumber, jed.DebitAmount, jed.CreditAmount, 
                               jed.Description, jed.Reference, jed.CostCenterId,
                               jed.CreatedDate, jed.CreatedBy,
                               coa.AccountCode, coa.AccountNameAr
                        FROM JournalEntryDetails jed
                        INNER JOIN ChartOfAccounts coa ON jed.AccountId = coa.AccountId
                        WHERE jed.JournalEntryId = @JournalEntryId
                        ORDER BY jed.LineNumber";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@JournalEntryId", journalEntryId);

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var detail = new JournalEntryDetail
                                {
                                    JournalEntryDetailId = Convert.ToInt32(reader["JournalEntryDetailId"]),
                                    JournalEntryId = Convert.ToInt32(reader["JournalEntryId"]),
                                    AccountId = Convert.ToInt32(reader["AccountId"]),
                                    LineNumber = Convert.ToInt32(reader["LineNumber"]),
                                    DebitAmount = reader["DebitAmount"] == DBNull.Value ? 0 : Convert.ToDecimal(reader["DebitAmount"]),
                                    CreditAmount = reader["CreditAmount"] == DBNull.Value ? 0 : Convert.ToDecimal(reader["CreditAmount"]),
                                    Description = reader["Description"]?.ToString() ?? "",
                                    Reference = reader["Reference"]?.ToString() ?? "",
                                    CostCenterId = reader["CostCenterId"] == DBNull.Value ? null : (int?)Convert.ToInt32(reader["CostCenterId"]),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                                    CreatedBy = reader["CreatedBy"]?.ToString() ?? "",
                                    ChartOfAccount = new ChartOfAccount
                                    {
                                        AccountId = Convert.ToInt32(reader["AccountId"]),
                                        AccountCode = reader["AccountCode"]?.ToString() ?? "",
                                        AccountNameAr = reader["AccountNameAr"]?.ToString() ?? ""
                                    }
                                };

                                details.Add(detail);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على تفاصيل القيد: {ex.Message}", ex);
            }

            return details;
        }

        /// <summary>
        /// الحصول على رقم القيد التالي
        /// Get next journal entry number
        /// </summary>
        /// <returns>رقم القيد التالي</returns>
        public static string GetNextJournalEntryNumber()
        {
            try
            {
                using (var connection = new SqlConnection(ConnectionString))
                {
                    connection.Open();
                    var query = @"
                        SELECT ISNULL(MAX(CAST(SUBSTRING(JournalNumber, 3, LEN(JournalNumber) - 2) AS INT)), 0) + 1 AS NextNumber
                        FROM JournalEntries
                        WHERE JournalNumber LIKE 'JE%' AND ISNUMERIC(SUBSTRING(JournalNumber, 3, LEN(JournalNumber) - 2)) = 1";

                    using (var command = new SqlCommand(query, connection))
                    {
                        var result = command.ExecuteScalar();
                        var nextNumber = Convert.ToInt32(result);
                        return $"JE{nextNumber:D6}";
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على رقم القيد التالي: {ex.Message}", ex);
            }
        }

        #endregion

        #region Insert Methods

        /// <summary>
        /// إدراج قيد يومي جديد
        /// Insert new journal entry
        /// </summary>
        /// <param name="journalEntry">القيد اليومي</param>
        /// <param name="details">تفاصيل القيد</param>
        /// <returns>معرف القيد المدرج</returns>
        public static int InsertJournalEntry(JournalEntry journalEntry, List<JournalEntryDetail> details)
        {
            using (var connection = new SqlConnection(ConnectionString))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        // إدراج القيد الرئيسي
                        var insertJournalEntryQuery = @"
                            INSERT INTO JournalEntries (JournalNumber, JournalDate, GeneralDescription, TotalDebit,
                                                      TotalCredit, JournalType, Status, CreatedDate, CreatedBy)
                            VALUES (@JournalNumber, @JournalDate, @GeneralDescription, @TotalDebit,
                                   @TotalCredit, @JournalType, @Status, @CreatedDate, @CreatedBy);
                            SELECT SCOPE_IDENTITY();";

                        int journalEntryId;
                        using (var command = new SqlCommand(insertJournalEntryQuery, connection, transaction))
                        {
                            command.Parameters.AddWithValue("@JournalNumber", journalEntry.JournalNumber);
                            command.Parameters.AddWithValue("@JournalDate", journalEntry.JournalDate);
                            command.Parameters.AddWithValue("@GeneralDescription", journalEntry.Description);
                            command.Parameters.AddWithValue("@TotalDebit", details.Sum(d => d.DebitAmount));
                            command.Parameters.AddWithValue("@TotalCredit", details.Sum(d => d.CreditAmount));
                            command.Parameters.AddWithValue("@JournalType", (int)journalEntry.JournalType);
                            command.Parameters.AddWithValue("@Status", (int)journalEntry.Status);
                            command.Parameters.AddWithValue("@CreatedDate", DateTime.Now);
                            command.Parameters.AddWithValue("@CreatedBy", journalEntry.CreatedBy);

                            journalEntryId = Convert.ToInt32(command.ExecuteScalar());
                        }

                        // إدراج تفاصيل القيد
                        var insertDetailQuery = @"
                            INSERT INTO JournalEntryDetails (JournalEntryId, AccountId, LineNumber,
                                                           DebitAmount, CreditAmount, Description, Reference)
                            VALUES (@JournalEntryId, @AccountId, @LineNumber,
                                   @DebitAmount, @CreditAmount, @Description, @Reference)";

                        for (int i = 0; i < details.Count; i++)
                        {
                            var detail = details[i];
                            using (var command = new SqlCommand(insertDetailQuery, connection, transaction))
                            {
                                command.Parameters.AddWithValue("@JournalEntryId", journalEntryId);
                                command.Parameters.AddWithValue("@AccountId", detail.AccountId);
                                command.Parameters.AddWithValue("@LineNumber", i + 1);
                                command.Parameters.AddWithValue("@DebitAmount", detail.DebitAmount);
                                command.Parameters.AddWithValue("@CreditAmount", detail.CreditAmount);
                                command.Parameters.AddWithValue("@Description", detail.Description ?? "");
                                command.Parameters.AddWithValue("@Reference", detail.Reference ?? "");

                                command.ExecuteNonQuery();
                            }
                        }

                        transaction.Commit();
                        return journalEntryId;
                    }
                    catch
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }

        #endregion

        #region Update Methods

        /// <summary>
        /// تحديث قيد يومي
        /// Update journal entry
        /// </summary>
        /// <param name="journalEntry">القيد اليومي</param>
        /// <param name="details">تفاصيل القيد</param>
        public static void UpdateJournalEntry(JournalEntry journalEntry, List<JournalEntryDetail> details)
        {
            using (var connection = new SqlConnection(ConnectionString))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        // تحديث القيد الرئيسي
                        var updateJournalEntryQuery = @"
                            UPDATE JournalEntries
                            SET JournalDate = @JournalDate, GeneralDescription = @GeneralDescription,
                                TotalDebit = @TotalDebit, TotalCredit = @TotalCredit,
                                ModifiedDate = @ModifiedDate, ModifiedBy = @ModifiedBy
                            WHERE JournalEntryId = @JournalEntryId";

                        using (var command = new SqlCommand(updateJournalEntryQuery, connection, transaction))
                        {
                            command.Parameters.AddWithValue("@JournalEntryId", journalEntry.JournalEntryId);
                            command.Parameters.AddWithValue("@JournalDate", journalEntry.JournalDate);
                            command.Parameters.AddWithValue("@GeneralDescription", journalEntry.Description);
                            command.Parameters.AddWithValue("@TotalDebit", details.Sum(d => d.DebitAmount));
                            command.Parameters.AddWithValue("@TotalCredit", details.Sum(d => d.CreditAmount));
                            command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);
                            command.Parameters.AddWithValue("@ModifiedBy", journalEntry.ModifiedBy);

                            command.ExecuteNonQuery();
                        }

                        // حذف التفاصيل القديمة
                        var deleteDetailsQuery = "DELETE FROM JournalEntryDetails WHERE JournalEntryId = @JournalEntryId";
                        using (var command = new SqlCommand(deleteDetailsQuery, connection, transaction))
                        {
                            command.Parameters.AddWithValue("@JournalEntryId", journalEntry.JournalEntryId);
                            command.ExecuteNonQuery();
                        }

                        // إدراج التفاصيل الجديدة
                        var insertDetailQuery = @"
                            INSERT INTO JournalEntryDetails (JournalEntryId, AccountId, LineNumber,
                                                           DebitAmount, CreditAmount, Description, Reference)
                            VALUES (@JournalEntryId, @AccountId, @LineNumber,
                                   @DebitAmount, @CreditAmount, @Description, @Reference)";

                        for (int i = 0; i < details.Count; i++)
                        {
                            var detail = details[i];
                            using (var command = new SqlCommand(insertDetailQuery, connection, transaction))
                            {
                                command.Parameters.AddWithValue("@JournalEntryId", journalEntry.JournalEntryId);
                                command.Parameters.AddWithValue("@AccountId", detail.AccountId);
                                command.Parameters.AddWithValue("@LineNumber", i + 1);
                                command.Parameters.AddWithValue("@DebitAmount", detail.DebitAmount);
                                command.Parameters.AddWithValue("@CreditAmount", detail.CreditAmount);
                                command.Parameters.AddWithValue("@Description", detail.Description ?? "");
                                command.Parameters.AddWithValue("@Reference", detail.Reference ?? "");

                                command.ExecuteNonQuery();
                            }
                        }

                        transaction.Commit();
                    }
                    catch
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }

        #endregion

        #region Delete Methods

        /// <summary>
        /// حذف قيد يومي
        /// Delete journal entry
        /// </summary>
        /// <param name="journalEntryId">معرف القيد</param>
        public static void DeleteJournalEntry(int journalEntryId)
        {
            using (var connection = new SqlConnection(ConnectionString))
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        // حذف التفاصيل أولاً
                        var deleteDetailsQuery = "DELETE FROM JournalEntryDetails WHERE JournalEntryId = @JournalEntryId";
                        using (var command = new SqlCommand(deleteDetailsQuery, connection, transaction))
                        {
                            command.Parameters.AddWithValue("@JournalEntryId", journalEntryId);
                            command.ExecuteNonQuery();
                        }

                        // حذف القيد الرئيسي
                        var deleteJournalEntryQuery = "DELETE FROM JournalEntries WHERE JournalEntryId = @JournalEntryId";
                        using (var command = new SqlCommand(deleteJournalEntryQuery, connection, transaction))
                        {
                            command.Parameters.AddWithValue("@JournalEntryId", journalEntryId);
                            command.ExecuteNonQuery();
                        }

                        transaction.Commit();
                    }
                    catch
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }

        #endregion

        #region Posting Methods

        /// <summary>
        /// ترحيل قيد يومي
        /// Post journal entry
        /// </summary>
        /// <param name="journalEntryId">معرف القيد</param>
        /// <param name="postedBy">المستخدم المرحل</param>
        public static void PostJournalEntry(int journalEntryId, string postedBy)
        {
            using (var connection = new SqlConnection(ConnectionString))
            {
                connection.Open();
                var query = @"
                    UPDATE JournalEntries
                    SET Status = 2, PostedDate = @PostedDate, PostedBy = @PostedBy
                    WHERE JournalEntryId = @JournalEntryId";

                using (var command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@JournalEntryId", journalEntryId);
                    command.Parameters.AddWithValue("@PostedDate", DateTime.Now);
                    command.Parameters.AddWithValue("@PostedBy", postedBy);

                    command.ExecuteNonQuery();
                }
            }
        }

        /// <summary>
        /// إلغاء ترحيل قيد يومي
        /// Unpost journal entry
        /// </summary>
        /// <param name="journalEntryId">معرف القيد</param>
        /// <param name="unpostedBy">المستخدم الملغي للترحيل</param>
        public static void UnpostJournalEntry(int journalEntryId, string unpostedBy)
        {
            using (var connection = new SqlConnection(ConnectionString))
            {
                connection.Open();
                var query = @"
                    UPDATE JournalEntries
                    SET Status = 1, PostedDate = NULL, PostedBy = NULL,
                        ModifiedDate = @ModifiedDate, ModifiedBy = @ModifiedBy
                    WHERE JournalEntryId = @JournalEntryId";

                using (var command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@JournalEntryId", journalEntryId);
                    command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);
                    command.Parameters.AddWithValue("@ModifiedBy", unpostedBy);

                    command.ExecuteNonQuery();
                }
            }
        }

        #endregion

        #region Search Methods

        /// <summary>
        /// البحث في القيود اليومية
        /// Search journal entries
        /// </summary>
        /// <param name="searchCriteria">معايير البحث</param>
        /// <returns>قائمة القيود المطابقة</returns>
        public static List<JournalEntry> SearchJournalEntries(JournalEntrySearchCriteria searchCriteria)
        {
            var journalEntries = new List<JournalEntry>();

            try
            {
                using (var connection = new SqlConnection(ConnectionString))
                {
                    connection.Open();
                    
                    var whereConditions = new List<string>();
                    var parameters = new List<SqlParameter>();

                    if (!string.IsNullOrWhiteSpace(searchCriteria.EntryNumber))
                    {
                        whereConditions.Add("JournalNumber LIKE @EntryNumber");
                        parameters.Add(new SqlParameter("@EntryNumber", $"%{searchCriteria.EntryNumber}%"));
                    }

                    if (searchCriteria.FromDate.HasValue)
                    {
                        whereConditions.Add("JournalDate >= @FromDate");
                        parameters.Add(new SqlParameter("@FromDate", searchCriteria.FromDate.Value));
                    }

                    if (searchCriteria.ToDate.HasValue)
                    {
                        whereConditions.Add("JournalDate <= @ToDate");
                        parameters.Add(new SqlParameter("@ToDate", searchCriteria.ToDate.Value));
                    }

                    if (!string.IsNullOrWhiteSpace(searchCriteria.Description))
                    {
                        whereConditions.Add("GeneralDescription LIKE @Description");
                        parameters.Add(new SqlParameter("@Description", $"%{searchCriteria.Description}%"));
                    }

                    if (searchCriteria.IsPosted.HasValue)
                    {
                        whereConditions.Add("Status = @Status");
                        parameters.Add(new SqlParameter("@Status", searchCriteria.IsPosted.Value ? 2 : 1));
                    }

                    if (!string.IsNullOrWhiteSpace(searchCriteria.CreatedBy))
                    {
                        whereConditions.Add("CreatedBy LIKE @CreatedBy");
                        parameters.Add(new SqlParameter("@CreatedBy", $"%{searchCriteria.CreatedBy}%"));
                    }

                    var whereClause = whereConditions.Any() ? "WHERE " + string.Join(" AND ", whereConditions) : "";

                    var query = $@"
                        SELECT JournalEntryId, JournalNumber, JournalDate, GeneralDescription,
                               TotalDebit, TotalCredit, Status, PostedDate, PostedBy,
                               CreatedDate, CreatedBy, ModifiedDate, ModifiedBy
                        FROM JournalEntries
                        {whereClause}
                        ORDER BY JournalDate DESC, JournalNumber DESC";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddRange(parameters.ToArray());

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var journalEntry = new JournalEntry
                                {
                                    JournalEntryId = Convert.ToInt32(reader["JournalEntryId"]),
                                    JournalNumber = reader["JournalNumber"]?.ToString() ?? "",
                                    JournalDate = Convert.ToDateTime(reader["JournalDate"]),
                                    Description = reader["GeneralDescription"]?.ToString() ?? "",
                                    TotalDebit = reader["TotalDebit"] == DBNull.Value ? 0 : Convert.ToDecimal(reader["TotalDebit"]),
                                    TotalCredit = reader["TotalCredit"] == DBNull.Value ? 0 : Convert.ToDecimal(reader["TotalCredit"]),
                                    Status = reader["Status"] == DBNull.Value ? JournalStatus.Draft : (JournalStatus)Convert.ToInt32(reader["Status"]),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                                    CreatedBy = reader["CreatedBy"]?.ToString() ?? "",
                                    ModifiedDate = reader["ModifiedDate"] == DBNull.Value ? null : (DateTime?)Convert.ToDateTime(reader["ModifiedDate"]),
                                    ModifiedBy = reader["ModifiedBy"]?.ToString() ?? ""
                                };

                                journalEntries.Add(journalEntry);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في البحث في القيود اليومية: {ex.Message}", ex);
            }

            return journalEntries;
        }

        #endregion
    }
}
