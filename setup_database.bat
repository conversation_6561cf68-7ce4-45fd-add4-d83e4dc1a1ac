@echo off
echo Setting up Awqaf Management Database...
echo.

REM Database setup script for Awqaf Management System
REM This script will create the database and all required tables

set DB_PATH="C:\Program Files\Microsoft SQL Server\MSSQL12.MSSQLSERVER\MSSQL\DATA"
set SERVER_NAME="NAJEEB"
set DB_NAME="AwqafManagement"

echo Database Path: %DB_PATH%
echo Server Name: %SERVER_NAME%
echo Database Name: %DB_NAME%
echo.

REM Check if SQL Server is running
echo Checking SQL Server service...
sc query "MSSQLSERVER" | find "RUNNING" >nul
if errorlevel 1 (
    echo SQL Server is not running. Please start SQL Server service.
    echo You can start it by running: net start "MSSQLSERVER"
    pause
    exit /b 1
)

echo SQL Server is running.
echo.

REM Try to connect and run the database setup script
echo Creating database and tables...
echo.

sqlcmd -S %SERVER_NAME% -E -i "Database\RunAllScripts.sql"

if errorlevel 1 (
    echo.
    echo Error: Failed to create database. Please check:
    echo 1. SQL Server Express is installed and running
    echo 2. Windows Authentication is enabled
    echo 3. You have sufficient permissions
    echo.
    echo Alternative: You can run the SQL scripts manually in SQL Server Management Studio
    echo Scripts location: Database\RunAllScripts.sql
    echo.
    pause
    exit /b 1
) else (
    echo.
    echo Database setup completed successfully!
    echo.
    echo Default login credentials:
    echo Username: admin
    echo Password: admin123
    echo.
    echo You can now run the application using: bin\Debug\Awqaf_Managment.exe
    echo.
)

pause
