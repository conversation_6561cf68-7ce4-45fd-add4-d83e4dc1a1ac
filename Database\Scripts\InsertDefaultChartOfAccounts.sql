-- إدراج البيانات الافتراضية للدليل المحاسبي
-- Default Chart of Accounts Data

USE AwqafManagement;
GO

-- إدراج أنواع الحسابات
INSERT INTO AccountTypes (AccountTypeCode, AccountTypeName, AccountTypeNameAr, Description, DisplayOrder, IsActive, CreatedDate)
VALUES
    ('AST', 'Assets', N'الأصول', N'جميع الأصول المملوكة للمؤسسة', 1, 1, GETDATE()),
    ('LIB', 'Liabilities', N'الخصوم', N'جميع الالتزامات والديون', 2, 1, GETDATE()),
    ('EQT', 'Equity', N'حقوق الملكية', N'حقوق أصحاب المؤسسة', 3, 1, GETDATE()),
    ('REV', 'Revenue', N'الإيرادات', N'جميع الإيرادات والدخل', 4, 1, GETDATE()),
    ('EXP', 'Expenses', N'المصروفات', N'جميع المصروفات والتكاليف', 5, 1, GETDATE());

-- إدراج مجموعات الحسابات
INSERT INTO AccountGroups (GroupCode, GroupName, GroupNameAr, AccountTypeId, Description, DisplayOrder, IsActive, CreatedDate)
VALUES
    ('CA', 'Current Assets', N'أصول متداولة', 1, N'الأصول التي يمكن تحويلها لنقد خلال سنة', 1, 1, GETDATE()),
    ('FA', 'Fixed Assets', N'أصول ثابتة', 1, N'الأصول طويلة الأجل', 2, 1, GETDATE()),
    ('CL', 'Current Liabilities', N'خصوم متداولة', 2, N'الالتزامات المستحقة خلال سنة', 1, 1, GETDATE()),
    ('LL', 'Long-term Liabilities', N'خصوم طويلة الأجل', 2, N'الالتزامات طويلة الأجل', 2, 1, GETDATE()),
    ('EQ', 'Equity', N'حقوق الملكية', 3, N'حقوق أصحاب المؤسسة', 1, 1, GETDATE()),
    ('OR', 'Operating Revenue', N'إيرادات التشغيل', 4, N'الإيرادات من الأنشطة الرئيسية', 1, 1, GETDATE()),
    ('OE', 'Operating Expenses', N'مصروفات التشغيل', 5, N'المصروفات التشغيلية', 1, 1, GETDATE());

-- إدراج العملات
INSERT INTO Currencies (CurrencyCode, CurrencyNameAr, CurrencyNameEn, Symbol, ExchangeRate, IsBaseCurrency, IsActive, CreatedDate)
VALUES 
    ('SAR', N'ريال سعودي', 'Saudi Riyal', N'ر.س', 1.0, 1, 1, GETDATE()),
    ('USD', N'دولار أمريكي', 'US Dollar', '$', 3.75, 0, 1, GETDATE()),
    ('EUR', N'يورو', 'Euro', '€', 4.10, 0, 1, GETDATE());

-- إدراج الحسابات الرئيسية والفرعية
INSERT INTO ChartOfAccounts (
    AccountCode, AccountName, AccountNameAr, AccountTypeId, AccountGroupId,
    ParentAccountId, AccountLevel, IsParent, IsActive, AllowPosting,
    CurrencyCode, OpeningBalance, Description, CreatedDate
)
VALUES
    -- الأصول الرئيسية
    ('1.00.000', 'Assets', N'الأصول', 1, 1, NULL, 1, 1, 1, 0, 'SAR', 0, N'جميع أصول المؤسسة', GETDATE()),

    -- الأصول المتداولة
    ('1.01.000', 'Current Assets', N'الأصول المتداولة', 1, 1, 1, 2, 1, 1, 0, 'SAR', 0, N'الأصول قصيرة الأجل', GETDATE()),
    ('1.01.001', 'Cash in Hand', N'النقدية في الصندوق', 1, 1, 2, 3, 0, 1, 1, 'SAR', 50000, N'النقد المتوفر في الصندوق', GETDATE()),
    ('1.01.002', 'Bank - Current Account', N'البنك - الحساب الجاري', 1, 1, 2, 3, 0, 1, 1, 'SAR', 250000, N'الحساب الجاري في البنك', GETDATE()),
    ('1.01.003', 'Accounts Receivable', N'العملاء والمدينون', 1, 1, 2, 3, 0, 1, 1, 'SAR', 75000, N'المبالغ المستحقة من العملاء', GETDATE()),
    ('1.01.004', 'Inventory', N'المخزون', 1, 1, 2, 3, 0, 1, 1, 'SAR', 120000, N'قيمة البضائع المخزنة', GETDATE()),

    -- الأصول الثابتة
    ('1.02.000', 'Fixed Assets', N'الأصول الثابتة', 1, 2, 1, 2, 1, 1, 0, 'SAR', 0, N'الأصول طويلة الأجل', GETDATE()),
    ('1.02.001', 'Land and Buildings', N'الأراضي والمباني', 1, 2, 7, 3, 0, 1, 1, 'SAR', 500000, N'قيمة الأراضي والمباني', GETDATE()),
    ('1.02.002', 'Furniture and Equipment', N'الأثاث والمعدات', 1, 2, 7, 3, 0, 1, 1, 'SAR', 80000, N'قيمة الأثاث والمعدات', GETDATE()),

    -- الخصوم الرئيسية
    ('2.00.000', 'Liabilities', N'الخصوم', 2, 3, NULL, 1, 1, 1, 0, 'SAR', 0, N'جميع التزامات المؤسسة', GETDATE()),

    -- الخصوم المتداولة
    ('2.01.000', 'Current Liabilities', N'الخصوم المتداولة', 2, 3, 10, 2, 1, 1, 0, 'SAR', 0, N'الالتزامات قصيرة الأجل', GETDATE()),
    ('2.01.001', 'Accounts Payable', N'الموردون والدائنون', 2, 3, 11, 3, 0, 1, 1, 'SAR', 45000, N'المبالغ المستحقة للموردين', GETDATE()),
    ('2.01.002', 'Accrued Expenses', N'مصروفات مستحقة', 2, 3, 11, 3, 0, 1, 1, 'SAR', 15000, N'المصروفات المستحقة غير المدفوعة', GETDATE()),

    -- حقوق الملكية
    ('3.00.000', 'Equity', N'حقوق الملكية', 3, 5, NULL, 1, 1, 1, 0, 'SAR', 0, N'حقوق أصحاب المؤسسة', GETDATE()),
    ('3.01.000', 'Capital', N'رأس المال', 3, 5, 14, 2, 1, 1, 0, 'SAR', 0, N'رأس مال المؤسسة', GETDATE()),
    ('3.01.001', 'Paid-up Capital', N'رأس المال المدفوع', 3, 5, 15, 3, 0, 1, 1, 'SAR', 1000000, N'رأس المال المدفوع فعلياً', GETDATE()),
    ('3.01.002', 'Retained Earnings', N'الأرباح المحتجزة', 3, 5, 15, 3, 0, 1, 1, 'SAR', 35000, N'الأرباح المحتجزة من السنوات السابقة', GETDATE()),

    -- الإيرادات
    ('4.00.000', 'Revenue', N'الإيرادات', 4, 6, NULL, 1, 1, 1, 0, 'SAR', 0, N'جميع إيرادات المؤسسة', GETDATE()),
    ('4.01.000', 'Operating Revenue', N'إيرادات التشغيل', 4, 6, 18, 2, 1, 1, 0, 'SAR', 0, N'الإيرادات من الأنشطة الرئيسية', GETDATE()),
    ('4.01.001', 'Sales Revenue', N'إيرادات المبيعات', 4, 6, 19, 3, 0, 1, 1, 'SAR', 0, N'إيرادات من بيع البضائع', GETDATE()),
    ('4.01.002', 'Service Revenue', N'إيرادات الخدمات', 4, 6, 19, 3, 0, 1, 1, 'SAR', 0, N'إيرادات من تقديم الخدمات', GETDATE()),

    -- المصروفات
    ('5.00.000', 'Expenses', N'المصروفات', 5, 7, NULL, 1, 1, 1, 0, 'SAR', 0, N'جميع مصروفات المؤسسة', GETDATE()),
    ('5.01.000', 'Operating Expenses', N'مصروفات التشغيل', 5, 7, 22, 2, 1, 1, 0, 'SAR', 0, N'المصروفات التشغيلية', GETDATE()),
    ('5.01.001', 'Salaries and Wages', N'رواتب وأجور', 5, 7, 23, 3, 0, 1, 1, 'SAR', 0, N'رواتب وأجور الموظفين', GETDATE()),
    ('5.01.002', 'Rent Expense', N'إيجارات', 5, 7, 23, 3, 0, 1, 1, 'SAR', 0, N'مصروفات الإيجار', GETDATE()),
    ('5.01.003', 'Utilities Expense', N'مصروفات كهرباء وماء', 5, 7, 23, 3, 0, 1, 1, 'SAR', 0, N'فواتير الكهرباء والماء', GETDATE()),
    ('5.01.004', 'Administrative Expenses', N'مصروفات إدارية', 5, 7, 23, 3, 0, 1, 1, 'SAR', 0, N'المصروفات الإدارية العامة', GETDATE());

PRINT N'تم إدراج البيانات الافتراضية للدليل المحاسبي بنجاح';
PRINT 'Default Chart of Accounts data inserted successfully';
