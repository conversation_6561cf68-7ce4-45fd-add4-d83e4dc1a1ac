# 🧪 دليل الاختبار الشامل للنظام
# 🧪 Comprehensive System Testing Guide

## ✅ نتائج الاختبار التلقائي
## ✅ Automated Test Results

### 🔗 قاعدة البيانات - Database
- ✅ **الاتصال**: ناجح
- ✅ **Connection**: Successful
- ✅ **أنواع الحسابات**: 5 أنواع
- ✅ **Account Types**: 5 types
- ✅ **مجموعات الحسابات**: 6 مجموعات  
- ✅ **Account Groups**: 6 groups
- ✅ **الحسابات**: 31 حساب
- ✅ **Accounts**: 31 accounts
- ✅ **جدول القيود**: موجود وجاهز
- ✅ **Journal Table**: Available and ready

### 🖥️ التطبيق - Application
- ✅ **الملف**: موجود
- ✅ **File**: Available
- ✅ **التشغيل**: يعمل بنجاح
- ✅ **Launch**: Working successfully

---

## 📋 خطوات الاختبار اليدوي
## 📋 Manual Testing Steps

### 1️⃣ اختبار شاشة الدليل المحاسبي
### 1️⃣ Chart of Accounts Screen Testing

#### أ. فتح الشاشة
#### a. Opening the Screen
1. شغل التطبيق
2. اضغط على زر "المحاسبة"
3. اختر "الدليل المحاسبي"
4. تأكد من ظهور الشاشة بشكل صحيح

#### ب. اختبار عرض البيانات
#### b. Data Display Testing
- ✅ **شجرة الحسابات**: يجب أن تظهر 31 حساب
- ✅ **Account Tree**: Should show 31 accounts
- ✅ **النصوص العربية**: يجب أن تظهر بوضوح
- ✅ **Arabic Text**: Should display clearly
- ✅ **الترقيم**: يجب أن يظهر بنمط 1.01.001
- ✅ **Numbering**: Should show pattern 1.01.001

#### ج. اختبار الإضافة
#### c. Add Testing
1. اضغط زر "إضافة"
2. املأ البيانات:
   - **الاسم العربي**: حساب تجريبي
   - **النوع**: اختر من القائمة
   - **المجموعة**: اختر من القائمة
   - **العملة**: ريال سعودي
3. اضغط "حفظ"
4. تأكد من إضافة الحساب للشجرة

#### د. اختبار التعديل
#### d. Edit Testing
1. اختر حساب من الشجرة
2. اضغط زر "تعديل"
3. غير الاسم العربي
4. اضغط "حفظ"
5. تأكد من حفظ التغييرات

#### هـ. اختبار البحث
#### e. Search Testing
1. اكتب في مربع البحث: "صندوق"
2. تأكد من ظهور النتائج المطابقة فقط
3. امسح البحث وتأكد من عودة جميع الحسابات

### 2️⃣ اختبار شاشة القيود اليومية
### 2️⃣ Journal Entries Screen Testing

#### أ. فتح الشاشة
#### a. Opening the Screen
1. من قائمة المحاسبة
2. اختر "القيود اليومية"
3. تأكد من ظهور الشاشة بالتبويبات

#### ب. اختبار إنشاء قيد جديد
#### b. New Entry Testing
1. اضغط زر "جديد"
2. املأ البيانات الأساسية:
   - **التاريخ**: اليوم
   - **المرجع**: REF001
   - **البيان**: قيد تجريبي
   - **مركز التكلفة**: اختر من القائمة
3. في تفاصيل القيد:
   - اضغط "إضافة سطر"
   - اختر حساب من البحث
   - أدخل مبلغ مدين: 1000
   - اضغط "إضافة سطر" مرة أخرى
   - اختر حساب آخر
   - أدخل مبلغ دائن: 1000
4. تأكد من توازن القيد (مدين = دائن)
5. اضغط "حفظ"

#### ج. اختبار البحث عن الحسابات
#### c. Account Lookup Testing
1. في تفاصيل القيد، اضغط زر البحث
2. تأكد من ظهور نافذة البحث
3. اكتب في البحث: "صندوق"
4. تأكد من ظهور النتائج المطابقة
5. اختر حساب واضغط "اختيار"
6. تأكد من إدراج الحساب في القيد

### 3️⃣ اختبار شاشة البحث عن الحسابات
### 3️⃣ Account Lookup Screen Testing

#### أ. الوصول للشاشة
#### a. Accessing the Screen
1. من شاشة القيود اليومية
2. اضغط زر البحث بجانب حقل الحساب
3. تأكد من ظهور نافذة البحث

#### ب. اختبار البحث
#### b. Search Testing
1. **البحث الفارغ**: يجب أن تظهر جميع الحسابات
2. **البحث بالكود**: اكتب "1.01" وتأكد من النتائج
3. **البحث بالاسم**: اكتب "صندوق" وتأكد من النتائج
4. **البحث المختلط**: اكتب "1.01 صندوق"

#### ج. اختبار الاختيار
#### c. Selection Testing
1. انقر نقرة مزدوجة على حساب
2. تأكد من إغلاق النافذة واختيار الحساب
3. أو اختر حساب واضغط "اختيار"
4. اضغط "إلغاء" وتأكد من عدم اختيار أي حساب

### 4️⃣ اختبار الواجهة العربية
### 4️⃣ Arabic Interface Testing

#### أ. عرض النصوص
#### a. Text Display
- ✅ **الخطوط**: يجب أن تكون واضحة ومقروءة
- ✅ **Fonts**: Should be clear and readable
- ✅ **الاتجاه**: من اليمين لليسار
- ✅ **Direction**: Right to left
- ✅ **الأزرار**: النصوص العربية واضحة
- ✅ **Buttons**: Arabic text clear

#### ب. إدخال البيانات
#### b. Data Input
1. اكتب نصوص عربية في الحقول
2. تأكد من ظهورها بشكل صحيح
3. احفظ البيانات وتأكد من حفظها بالعربية

### 5️⃣ اختبار العمليات المتقدمة
### 5️⃣ Advanced Operations Testing

#### أ. التحقق من التوازن
#### a. Balance Validation
1. في القيود اليومية، أدخل مبالغ غير متوازنة
2. حاول الحفظ
3. تأكد من ظهور رسالة خطأ

#### ب. التحقق من البيانات المطلوبة
#### b. Required Data Validation
1. اترك حقول مطلوبة فارغة
2. حاول الحفظ
3. تأكد من ظهور رسائل التحقق

#### ج. اختبار الحذف
#### c. Delete Testing
1. اختر حساب أو قيد
2. اضغط زر "حذف"
3. تأكد من ظهور رسالة تأكيد
4. أكد الحذف وتأكد من الحذف الفعلي

---

## 🎯 معايير النجاح
## 🎯 Success Criteria

### ✅ الوظائف الأساسية
### ✅ Core Functions
- [ ] إضافة الحسابات
- [ ] تعديل الحسابات  
- [ ] حذف الحسابات
- [ ] البحث في الحسابات
- [ ] إنشاء القيود اليومية
- [ ] حفظ القيود
- [ ] البحث عن الحسابات في القيود

### ✅ الواجهة
### ✅ Interface
- [ ] عرض النصوص العربية بوضوح
- [ ] اتجاه النص من اليمين لليسار
- [ ] الخطوط واضحة ومقروءة
- [ ] الأزرار تعمل بشكل صحيح
- [ ] الألوان والتصميم احترافي

### ✅ الأداء
### ✅ Performance
- [ ] سرعة تحميل البيانات
- [ ] استجابة سريعة للبحث
- [ ] عدم وجود أخطاء أو توقف

---

## 🐛 تسجيل الأخطاء
## 🐛 Bug Reporting

إذا وجدت أي مشاكل، سجلها بالتفصيل:
If you find any issues, record them in detail:

1. **الخطوات**: كيف حدث الخطأ
2. **النتيجة المتوقعة**: ما كان يجب أن يحدث
3. **النتيجة الفعلية**: ما حدث فعلاً
4. **لقطة الشاشة**: إن أمكن

---

## 📞 الدعم
## 📞 Support

عند الانتهاء من الاختبار، أرسل النتائج مع:
After testing, send results with:

- ✅ قائمة الوظائف التي تعمل
- ❌ قائمة المشاكل المكتشفة
- 📸 لقطات الشاشة للمشاكل
- 💡 اقتراحات للتحسين
