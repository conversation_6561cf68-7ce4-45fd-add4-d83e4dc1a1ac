-- ===================================================================
-- البيانات الأساسية لنظام الأمان
-- ===================================================================

USE AwqafManagement;
GO

-- إدراج الأدوار الأساسية
INSERT INTO Roles (RoleName, RoleNameAr, Description) VALUES
('SuperAdmin', N'مدير النظام الرئيسي', N'صلاحيات كاملة على جميع أجزاء النظام'),
('Admin', N'مدير النظام', N'صلاحيات إدارية على معظم أجزاء النظام'),
('PropertyManager', N'مدير العقارات', N'إدارة العقارات والعقود والإيجارات'),
('AccountManager', N'مدير المحاسبة', N'إدارة الحسابات والمعاملات المالية'),
('DataEntry', N'مدخل بيانات', N'إدخال وتعديل البيانات الأساسية'),
('Viewer', N'مستعرض', N'عرض البيانات والتقارير فقط');

-- إدراج الصلاحيات الأساسية
INSERT INTO Permissions (PermissionName, PermissionNameAr, ModuleName, ModuleNameAr, Description) VALUES
-- صلاحيات إدارة المستخدمين
('UserManagement', N'إدارة المستخدمين', 'Security', N'الأمان', N'إدارة المستخدمين والأدوار والصلاحيات'),
('RoleManagement', N'إدارة الأدوار', 'Security', N'الأمان', N'إدارة الأدوار والصلاحيات'),

-- صلاحيات إدارة العقارات
('PropertyManagement', N'إدارة العقارات', 'Properties', N'العقارات', N'إدارة بيانات العقارات'),
('PropertyTypes', N'أنواع العقارات', 'Properties', N'العقارات', N'إدارة أنواع وتصنيفات العقارات'),

-- صلاحيات إدارة العملاء
('CustomerManagement', N'إدارة العملاء', 'Customers', N'العملاء', N'إدارة بيانات العملاء'),
('CustomerTypes', N'أنواع العملاء', 'Customers', N'العملاء', N'إدارة أنواع وتصنيفات العملاء'),

-- صلاحيات إدارة العقود
('ContractManagement', N'إدارة العقود', 'Contracts', N'العقود', N'إدارة عقود البيع والإيجار'),
('ContractTemplates', N'قوالب العقود', 'Contracts', N'العقود', N'إدارة قوالب ونماذج العقود'),

-- صلاحيات إدارة المدفوعات
('PaymentManagement', N'إدارة المدفوعات', 'Payments', N'المدفوعات', N'إدارة المدفوعات والإيصالات'),
('PaymentMethods', N'طرق الدفع', 'Payments', N'المدفوعات', N'إدارة طرق ووسائل الدفع'),

-- صلاحيات المحاسبة
('AccountingManagement', N'إدارة المحاسبة', 'Accounting', N'المحاسبة', N'إدارة الحسابات والقيود المحاسبية'),
('ChartOfAccounts', N'دليل الحسابات', 'Accounting', N'المحاسبة', N'إدارة دليل الحسابات'),
('JournalEntries', N'القيود اليومية', 'Accounting', N'المحاسبة', N'إدارة القيود اليومية'),
('FinancialReports', N'التقارير المالية', 'Accounting', N'المحاسبة', N'عرض وطباعة التقارير المالية'),

-- صلاحيات إدارة المخازن
('InventoryManagement', N'إدارة المخازن', 'Inventory', N'المخازن', N'إدارة المخازن والمواد'),
('StockMovements', N'حركة المخزون', 'Inventory', N'المخازن', N'إدارة حركة دخول وخروج المواد'),

-- صلاحيات إدارة الموظفين
('EmployeeManagement', N'إدارة الموظفين', 'Employees', N'الموظفين', N'إدارة بيانات الموظفين'),
('Payroll', N'كشوف المرتبات', 'Employees', N'الموظفين', N'إدارة المرتبات والحوافز'),

-- صلاحيات إدارة الأصول
('AssetManagement', N'إدارة الأصول', 'Assets', N'الأصول', N'إدارة الأصول الثابتة'),
('AssetDepreciation', N'إهلاك الأصول', 'Assets', N'الأصول', N'حساب وإدارة إهلاك الأصول'),

-- صلاحيات التقارير
('ReportsManagement', N'إدارة التقارير', 'Reports', N'التقارير', N'عرض وطباعة التقارير'),
('CustomReports', N'التقارير المخصصة', 'Reports', N'التقارير', N'إنشاء وتخصيص التقارير'),

-- صلاحيات الإعدادات
('SystemSettings', N'إعدادات النظام', 'Settings', N'الإعدادات', N'إدارة إعدادات النظام العامة'),
('BackupRestore', N'النسخ الاحتياطي', 'Settings', N'الإعدادات', N'إنشاء واستعادة النسخ الاحتياطية');

-- إنشاء المستخدم الافتراضي (admin/admin123)
-- كلمة المرور: admin123 مع Salt
DECLARE @Salt NVARCHAR(50) = NEWID();
DECLARE @PasswordHash NVARCHAR(255) = CONVERT(NVARCHAR(255), HASHBYTES('SHA2_256', 'admin123' + @Salt), 2);

INSERT INTO Users (Username, PasswordHash, Salt, FullName, Email, IsActive, CreatedBy) VALUES
('admin', @PasswordHash, @Salt, N'مدير النظام الرئيسي', '<EMAIL>', 1, 1);

-- ربط المستخدم الافتراضي بدور مدير النظام الرئيسي
INSERT INTO UserRoles (UserId, RoleId, AssignedBy) VALUES
(1, 1, 1);

-- ربط دور مدير النظام الرئيسي بجميع الصلاحيات
INSERT INTO RolePermissions (RoleId, PermissionId, CanView, CanAdd, CanEdit, CanDelete, CanPrint, CanExport, CreatedBy)
SELECT 1, PermissionId, 1, 1, 1, 1, 1, 1, 1
FROM Permissions;

-- ربط دور مدير النظام بمعظم الصلاحيات (عدا إدارة المستخدمين)
INSERT INTO RolePermissions (RoleId, PermissionId, CanView, CanAdd, CanEdit, CanDelete, CanPrint, CanExport, CreatedBy)
SELECT 2, PermissionId, 1, 1, 1, 0, 1, 1, 1
FROM Permissions
WHERE PermissionName NOT IN ('UserManagement', 'RoleManagement');

-- ربط دور مدير العقارات بصلاحيات العقارات والعملاء والعقود
INSERT INTO RolePermissions (RoleId, PermissionId, CanView, CanAdd, CanEdit, CanDelete, CanPrint, CanExport, CreatedBy)
SELECT 3, PermissionId, 1, 1, 1, 1, 1, 1, 1
FROM Permissions
WHERE ModuleName IN ('Properties', 'Customers', 'Contracts', 'Payments');

-- ربط دور مدير المحاسبة بصلاحيات المحاسبة والتقارير المالية
INSERT INTO RolePermissions (RoleId, PermissionId, CanView, CanAdd, CanEdit, CanDelete, CanPrint, CanExport, CreatedBy)
SELECT 4, PermissionId, 1, 1, 1, 1, 1, 1, 1
FROM Permissions
WHERE ModuleName IN ('Accounting', 'Reports', 'Payments');

-- ربط دور مدخل البيانات بصلاحيات الإضافة والتعديل فقط
INSERT INTO RolePermissions (RoleId, PermissionId, CanView, CanAdd, CanEdit, CanDelete, CanPrint, CanExport, CreatedBy)
SELECT 5, PermissionId, 1, 1, 1, 0, 1, 0, 1
FROM Permissions
WHERE ModuleName NOT IN ('Security', 'Settings');

-- ربط دور المستعرض بصلاحيات العرض والطباعة فقط
INSERT INTO RolePermissions (RoleId, PermissionId, CanView, CanAdd, CanEdit, CanDelete, CanPrint, CanExport, CreatedBy)
SELECT 6, PermissionId, 1, 0, 0, 0, 1, 1, 1
FROM Permissions;

PRINT N'تم إدراج البيانات الأساسية والمستخدم الافتراضي بنجاح';
PRINT N'اسم المستخدم: admin';
PRINT N'كلمة المرور: admin123';
GO
