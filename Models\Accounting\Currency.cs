using System;

namespace Awqaf_Managment.Models.Accounting
{
    /// <summary>
    /// نموذج بيانات العملة
    /// Currency Data Model
    /// </summary>
    public class Currency
    {
        #region Properties
        /// <summary>
        /// معرف العملة
        /// Currency ID
        /// </summary>
        public int CurrencyId { get; set; }

        /// <summary>
        /// رمز العملة (USD, EUR, SAR, etc.)
        /// Currency Code
        /// </summary>
        public string CurrencyCode { get; set; } = string.Empty;

        /// <summary>
        /// اسم العملة بالعربية
        /// Currency Name in Arabic
        /// </summary>
        public string CurrencyNameAr { get; set; } = string.Empty;

        /// <summary>
        /// اسم العملة بالإنجليزية
        /// Currency Name in English
        /// </summary>
        public string CurrencyNameEn { get; set; } = string.Empty;

        /// <summary>
        /// رمز العملة ($, €, ﷼, etc.)
        /// Currency Symbol
        /// </summary>
        public string Symbol { get; set; } = string.Empty;

        /// <summary>
        /// سعر الصرف مقابل العملة الأساسية
        /// Exchange Rate against Base Currency
        /// </summary>
        public decimal ExchangeRate { get; set; } = 1.0m;

        /// <summary>
        /// عدد المنازل العشرية
        /// Number of Decimal Places
        /// </summary>
        public int DecimalPlaces { get; set; } = 2;

        /// <summary>
        /// هل هي العملة الأساسية
        /// Is Base Currency
        /// </summary>
        public bool IsBaseCurrency { get; set; } = false;

        /// <summary>
        /// حالة النشاط
        /// Active Status
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// تاريخ الإنشاء
        /// Creation Date
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تعديل
        /// Last Modified Date
        /// </summary>
        public DateTime? ModifiedDate { get; set; }

        /// <summary>
        /// معرف المستخدم المنشئ
        /// Created By User Name
        /// </summary>
        public string CreatedBy { get; set; } = string.Empty;

        /// <summary>
        /// معرف المستخدم المعدل
        /// Modified By User Name
        /// </summary>
        public string ModifiedBy { get; set; } = string.Empty;

        /// <summary>
        /// اسم العملة (للتوافق مع الإصدارات القديمة)
        /// Currency Name (for backward compatibility)
        /// </summary>
        public string CurrencyName => CurrencyNameAr;

        /// <summary>
        /// رمز العملة (للتوافق مع الإصدارات القديمة)
        /// Currency Symbol (for backward compatibility)
        /// </summary>
        public string CurrencySymbol => Symbol;
        #endregion

        #region Constructor
        /// <summary>
        /// منشئ افتراضي
        /// Default Constructor
        /// </summary>
        public Currency()
        {
            CreatedDate = DateTime.Now;
            IsActive = true;
            ExchangeRate = 1.0m;
            DecimalPlaces = 2;
        }

        /// <summary>
        /// منشئ بمعاملات
        /// Parameterized Constructor
        /// </summary>
        /// <param name="currencyCode">رمز العملة</param>
        /// <param name="currencyNameAr">اسم العملة بالعربية</param>
        /// <param name="symbol">رمز العملة</param>
        /// <param name="exchangeRate">سعر الصرف</param>
        public Currency(string currencyCode, string currencyNameAr, string symbol, decimal exchangeRate = 1.0m)
        {
            CurrencyCode = currencyCode;
            CurrencyNameAr = currencyNameAr;
            Symbol = symbol;
            ExchangeRate = exchangeRate;
            CreatedDate = DateTime.Now;
            IsActive = true;
            DecimalPlaces = 2;
        }
        #endregion

        #region Methods
        /// <summary>
        /// تحويل مبلغ من هذه العملة إلى العملة الأساسية
        /// Convert amount from this currency to base currency
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <returns>المبلغ بالعملة الأساسية</returns>
        public decimal ConvertToBaseCurrency(decimal amount)
        {
            if (IsBaseCurrency)
                return amount;
            
            return amount / ExchangeRate;
        }

        /// <summary>
        /// تحويل مبلغ من العملة الأساسية إلى هذه العملة
        /// Convert amount from base currency to this currency
        /// </summary>
        /// <param name="baseAmount">المبلغ بالعملة الأساسية</param>
        /// <returns>المبلغ بهذه العملة</returns>
        public decimal ConvertFromBaseCurrency(decimal baseAmount)
        {
            if (IsBaseCurrency)
                return baseAmount;
            
            return baseAmount * ExchangeRate;
        }

        /// <summary>
        /// تنسيق المبلغ حسب عدد المنازل العشرية للعملة
        /// Format amount according to currency decimal places
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <returns>المبلغ منسق</returns>
        public string FormatAmount(decimal amount)
        {
            return amount.ToString($"N{DecimalPlaces}");
        }

        /// <summary>
        /// تنسيق المبلغ مع رمز العملة
        /// Format amount with currency symbol
        /// </summary>
        /// <param name="amount">المبلغ</param>
        /// <returns>المبلغ منسق مع الرمز</returns>
        public string FormatAmountWithSymbol(decimal amount)
        {
            return $"{FormatAmount(amount)} {Symbol}";
        }

        /// <summary>
        /// التحقق من صحة بيانات العملة
        /// Validate currency data
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(CurrencyCode) &&
                   !string.IsNullOrWhiteSpace(CurrencyNameAr) &&
                   !string.IsNullOrWhiteSpace(Symbol) &&
                   ExchangeRate > 0 &&
                   DecimalPlaces >= 0 &&
                   DecimalPlaces <= 6;
        }

        /// <summary>
        /// إرجاع تمثيل نصي للعملة
        /// Return string representation of currency
        /// </summary>
        /// <returns>النص الممثل للعملة</returns>
        public override string ToString()
        {
            return $"{CurrencyCode} - {CurrencyNameAr} ({Symbol})";
        }

        /// <summary>
        /// مقارنة العملات
        /// Compare currencies
        /// </summary>
        /// <param name="obj">الكائن للمقارنة</param>
        /// <returns>true إذا كانت العملات متطابقة</returns>
        public override bool Equals(object obj)
        {
            Currency other = obj as Currency;
            if (other != null)
            {
                return CurrencyId == other.CurrencyId ||
                       (CurrencyId == 0 && other.CurrencyId == 0 &&
                        CurrencyCode.Equals(other.CurrencyCode, StringComparison.OrdinalIgnoreCase));
            }
            return false;
        }

        /// <summary>
        /// الحصول على رمز التجمع
        /// Get hash code
        /// </summary>
        /// <returns>رمز التجمع</returns>
        public override int GetHashCode()
        {
            return CurrencyId != 0 ? CurrencyId.GetHashCode() : CurrencyCode.GetHashCode();
        }
        #endregion
    }
}
