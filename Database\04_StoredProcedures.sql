-- ===================================================================
-- الإجراءات المخزنة لنظام الأمان والمستخدمين
-- ===================================================================

USE AwqafManagement;
GO

-- إجراء تسجيل الدخول
CREATE PROCEDURE sp_AuthenticateUser
    @Username NVARCHAR(50),
    @Password NVARCHAR(255)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @UserId INT;
    DECLARE @StoredHash NVARCHAR(255);
    DECLARE @Salt NVARCHAR(50);
    DECLARE @IsActive BIT;
    DECLARE @IsLocked BIT;
    DECLARE @FailedAttempts INT;
    DECLARE @ComputedHash NVARCHAR(255);
    
    -- البحث عن المستخدم
    SELECT @UserId = UserId, @StoredHash = PasswordHash, @Salt = Salt, 
           @IsActive = IsActive, @IsLocked = IsLocked, @FailedAttempts = FailedLoginAttempts
    FROM Users 
    WHERE Username = @Username;
    
    -- التحقق من وجود المستخدم
    IF @UserId IS NULL
    BEGIN
        -- تسجيل محاولة دخول فاشلة
        INSERT INTO LoginHistory (Username, IsSuccessful, FailureReason, IPAddress)
        VALUES (@Username, 0, N'اسم المستخدم غير موجود', '127.0.0.1');
        
        SELECT 0 AS IsAuthenticated, N'اسم المستخدم أو كلمة المرور غير صحيحة' AS Message;
        RETURN;
    END
    
    -- التحقق من حالة المستخدم
    IF @IsActive = 0
    BEGIN
        INSERT INTO LoginHistory (UserId, Username, IsSuccessful, FailureReason, IPAddress)
        VALUES (@UserId, @Username, 0, N'الحساب غير مفعل', '127.0.0.1');
        
        SELECT 0 AS IsAuthenticated, N'الحساب غير مفعل' AS Message;
        RETURN;
    END
    
    IF @IsLocked = 1
    BEGIN
        INSERT INTO LoginHistory (UserId, Username, IsSuccessful, FailureReason, IPAddress)
        VALUES (@UserId, @Username, 0, N'الحساب مقفل', '127.0.0.1');
        
        SELECT 0 AS IsAuthenticated, N'الحساب مقفل، يرجى الاتصال بالمدير' AS Message;
        RETURN;
    END
    
    -- حساب hash كلمة المرور
    SET @ComputedHash = CONVERT(NVARCHAR(255), HASHBYTES('SHA2_256', @Password + @Salt), 2);
    
    -- التحقق من كلمة المرور
    IF @ComputedHash = @StoredHash
    BEGIN
        -- تسجيل دخول ناجح
        INSERT INTO LoginHistory (UserId, Username, IsSuccessful, IPAddress)
        VALUES (@UserId, @Username, 1, '127.0.0.1');
        
        -- تحديث تاريخ آخر دخول وإعادة تعيين محاولات الفشل
        UPDATE Users 
        SET LastLoginDate = GETDATE(), FailedLoginAttempts = 0
        WHERE UserId = @UserId;
        
        -- إرجاع بيانات المستخدم
        SELECT 1 AS IsAuthenticated, N'تم تسجيل الدخول بنجاح' AS Message,
               u.UserId, u.Username, u.FullName, u.Email,
               STRING_AGG(r.RoleNameAr, ', ') AS Roles
        FROM Users u
        LEFT JOIN UserRoles ur ON u.UserId = ur.UserId AND ur.IsActive = 1
        LEFT JOIN Roles r ON ur.RoleId = r.RoleId AND r.IsActive = 1
        WHERE u.UserId = @UserId
        GROUP BY u.UserId, u.Username, u.FullName, u.Email;
    END
    ELSE
    BEGIN
        -- تسجيل محاولة دخول فاشلة
        INSERT INTO LoginHistory (UserId, Username, IsSuccessful, FailureReason, IPAddress)
        VALUES (@UserId, @Username, 0, N'كلمة مرور خاطئة', '127.0.0.1');
        
        -- زيادة عدد محاولات الفشل
        UPDATE Users 
        SET FailedLoginAttempts = FailedLoginAttempts + 1,
            IsLocked = CASE WHEN FailedLoginAttempts >= 4 THEN 1 ELSE 0 END
        WHERE UserId = @UserId;
        
        SELECT 0 AS IsAuthenticated, N'اسم المستخدم أو كلمة المرور غير صحيحة' AS Message;
    END
END
GO

-- إجراء الحصول على صلاحيات المستخدم
CREATE PROCEDURE sp_GetUserPermissions
    @UserId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT DISTINCT p.PermissionName, p.PermissionNameAr, p.ModuleName, p.ModuleNameAr,
           MAX(CAST(rp.CanView AS INT)) AS CanView,
           MAX(CAST(rp.CanAdd AS INT)) AS CanAdd,
           MAX(CAST(rp.CanEdit AS INT)) AS CanEdit,
           MAX(CAST(rp.CanDelete AS INT)) AS CanDelete,
           MAX(CAST(rp.CanPrint AS INT)) AS CanPrint,
           MAX(CAST(rp.CanExport AS INT)) AS CanExport
    FROM Users u
    INNER JOIN UserRoles ur ON u.UserId = ur.UserId AND ur.IsActive = 1
    INNER JOIN Roles r ON ur.RoleId = r.RoleId AND r.IsActive = 1
    INNER JOIN RolePermissions rp ON r.RoleId = rp.RoleId
    INNER JOIN Permissions p ON rp.PermissionId = p.PermissionId AND p.IsActive = 1
    WHERE u.UserId = @UserId AND u.IsActive = 1
    GROUP BY p.PermissionName, p.PermissionNameAr, p.ModuleName, p.ModuleNameAr
    ORDER BY p.ModuleName, p.PermissionName;
END
GO

PRINT N'تم إنشاء الإجراءات المخزنة بنجاح';
GO
