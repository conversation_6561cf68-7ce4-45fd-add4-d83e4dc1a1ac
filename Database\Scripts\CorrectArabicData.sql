-- سكريبت صحيح لإدراج بيانات عربية تجريبية
-- Correct Arabic Sample Data Script

USE AwqafManagement;
GO

-- حذف البيانات الموجودة
DELETE FROM ChartOfAccounts;
DBCC CHECKIDENT ('ChartOfAccounts', RESEED, 0);
GO

-- إدراج الحسابات مع الأعمدة الصحيحة
SET IDENTITY_INSERT ChartOfAccounts ON;

-- الحسابات الرئيسية والفرعية
INSERT INTO ChartOfAccounts (AccountId, AccountCode, AccountNameAr, AccountNameEn, ParentAccountId, LevelType, Nature, AccountGroupId, Status, Currency, Description, AllowPosting, AllowDirectEntry, OpeningBalance, OpeningBalanceNature, AccountLevel, IsParent, CreatedDate)
VALUES 
-- 1. الأصول (Assets)
(1, N'1.00.000', N'الأصول', N'Assets', NULL, 1, 1, 1, 1, 1, N'مجموعة الأصول الرئيسية', 0, 0, 0.00, 1, 1, 1, GETDATE()),
(2, N'1.01.000', N'الأصول المتداولة', N'Current Assets', 1, 2, 1, 1, 1, 1, N'الأصول قصيرة الأجل', 0, 0, 0.00, 1, 2, 1, GETDATE()),
(3, N'1.01.001', N'الصندوق الرئيسي', N'Main Cash', 2, 3, 1, 1, 1, 1, N'صندوق النقدية الرئيسي', 1, 1, 50000.00, 1, 3, 0, GETDATE()),
(4, N'1.01.002', N'البنك الأهلي التجاري', N'National Commercial Bank', 2, 3, 1, 1, 1, 1, N'حساب البنك الأهلي التجاري', 1, 1, 250000.00, 1, 3, 0, GETDATE()),
(5, N'1.01.003', N'بنك الراجحي', N'Al Rajhi Bank', 2, 3, 1, 1, 1, 1, N'حساب بنك الراجحي', 1, 1, 180000.00, 1, 3, 0, GETDATE()),
(6, N'1.01.004', N'بنك الرياض', N'Riyad Bank', 2, 3, 1, 1, 1, 1, N'حساب بنك الرياض', 1, 1, 120000.00, 1, 3, 0, GETDATE()),
(7, N'1.01.005', N'العملاء والذمم المدينة', N'Accounts Receivable', 2, 3, 1, 1, 1, 1, N'مجموعة العملاء والذمم المدينة', 0, 0, 0.00, 1, 3, 1, GETDATE()),
(8, N'1.01.006', N'عميل - شركة الأوقاف العقارية', N'Client - Real Estate Endowments Co.', 7, 3, 1, 1, 1, 1, N'شركة الأوقاف العقارية', 1, 1, 75000.00, 1, 3, 0, GETDATE()),
(9, N'1.01.007', N'عميل - مؤسسة الخير الإسلامية', N'Client - Islamic Charity Foundation', 7, 3, 1, 1, 1, 1, N'مؤسسة الخير الإسلامية', 1, 1, 45000.00, 1, 3, 0, GETDATE()),

-- الأصول الثابتة
(10, N'1.02.000', N'الأصول الثابتة', N'Fixed Assets', 1, 2, 1, 1, 1, 1, N'الأصول طويلة الأجل', 0, 0, 0.00, 1, 2, 1, GETDATE()),
(11, N'1.02.001', N'الأراضي والعقارات', N'Land and Real Estate', 10, 3, 1, 1, 1, 1, N'الأراضي والعقارات الوقفية', 1, 1, 2500000.00, 1, 3, 0, GETDATE()),
(12, N'1.02.002', N'المباني والإنشاءات', N'Buildings and Constructions', 10, 3, 1, 1, 1, 1, N'المباني والإنشاءات الوقفية', 1, 1, 1800000.00, 1, 3, 0, GETDATE()),
(13, N'1.02.003', N'الأثاث والمعدات', N'Furniture and Equipment', 10, 3, 1, 1, 1, 1, N'الأثاث والمعدات المكتبية', 1, 1, 150000.00, 1, 3, 0, GETDATE()),

-- 2. الخصوم (Liabilities)
(14, N'2.00.000', N'الخصوم', N'Liabilities', NULL, 1, 2, 2, 1, 1, N'مجموعة الخصوم الرئيسية', 0, 0, 0.00, 2, 1, 1, GETDATE()),
(15, N'2.01.000', N'الخصوم المتداولة', N'Current Liabilities', 14, 2, 2, 2, 1, 1, N'الخصوم قصيرة الأجل', 0, 0, 0.00, 2, 2, 1, GETDATE()),
(16, N'2.01.001', N'الموردون والذمم الدائنة', N'Accounts Payable', 15, 3, 2, 2, 1, 1, N'الموردون والذمم الدائنة', 1, 1, 25000.00, 2, 3, 0, GETDATE()),
(17, N'2.01.002', N'المصروفات المستحقة', N'Accrued Expenses', 15, 3, 2, 2, 1, 1, N'المصروفات المستحقة الدفع', 1, 1, 15000.00, 2, 3, 0, GETDATE()),

-- 3. حقوق الملكية (Equity)
(18, N'3.00.000', N'حقوق الملكية', N'Equity', NULL, 1, 2, 3, 1, 1, N'مجموعة حقوق الملكية', 0, 0, 0.00, 2, 1, 1, GETDATE()),
(19, N'3.01.001', N'رأس المال الوقفي', N'Endowment Capital', 18, 3, 2, 3, 1, 1, N'رأس المال الوقفي الأساسي', 1, 1, 5000000.00, 2, 3, 0, GETDATE()),
(20, N'3.01.002', N'الأرباح المحتجزة', N'Retained Earnings', 18, 3, 2, 3, 1, 1, N'الأرباح المحتجزة من السنوات السابقة', 1, 1, 500000.00, 2, 3, 0, GETDATE()),

-- 4. الإيرادات (Revenues)
(21, N'4.00.000', N'الإيرادات', N'Revenues', NULL, 1, 2, 4, 1, 1, N'مجموعة الإيرادات الرئيسية', 0, 0, 0.00, 2, 1, 1, GETDATE()),
(22, N'4.01.001', N'إيرادات الإيجارات', N'Rental Income', 21, 3, 2, 4, 1, 1, N'إيرادات إيجار العقارات الوقفية', 1, 1, 0.00, 2, 3, 0, GETDATE()),
(23, N'4.01.002', N'إيرادات الاستثمارات', N'Investment Income', 21, 3, 2, 4, 1, 1, N'إيرادات الاستثمارات المالية', 1, 1, 0.00, 2, 3, 0, GETDATE()),
(24, N'4.01.003', N'التبرعات والهبات', N'Donations and Gifts', 21, 3, 2, 4, 1, 1, N'التبرعات والهبات الواردة', 1, 1, 0.00, 2, 3, 0, GETDATE()),

-- 5. المصروفات (Expenses)
(25, N'5.00.000', N'المصروفات', N'Expenses', NULL, 1, 1, 5, 1, 1, N'مجموعة المصروفات الرئيسية', 0, 0, 0.00, 1, 1, 1, GETDATE()),
(26, N'5.01.001', N'مصروفات الصيانة', N'Maintenance Expenses', 25, 3, 1, 5, 1, 1, N'مصروفات صيانة العقارات', 1, 1, 0.00, 1, 3, 0, GETDATE()),
(27, N'5.01.002', N'الرواتب والأجور', N'Salaries and Wages', 25, 3, 1, 5, 1, 1, N'رواتب وأجور الموظفين', 1, 1, 0.00, 1, 3, 0, GETDATE()),
(28, N'5.01.003', N'المصروفات الإدارية', N'Administrative Expenses', 25, 3, 1, 5, 1, 1, N'المصروفات الإدارية العامة', 1, 1, 0.00, 1, 3, 0, GETDATE()),
(29, N'5.01.004', N'مصروفات الكهرباء والماء', N'Utilities Expenses', 25, 3, 1, 5, 1, 1, N'مصروفات الكهرباء والماء والهاتف', 1, 1, 0.00, 1, 3, 0, GETDATE()),
(30, N'5.01.005', N'مصروفات التأمين', N'Insurance Expenses', 25, 3, 1, 5, 1, 1, N'مصروفات التأمين على العقارات', 1, 1, 0.00, 1, 3, 0, GETDATE());

SET IDENTITY_INSERT ChartOfAccounts OFF;
GO

-- عرض النتائج
SELECT 
    AccountCode as 'رمز الحساب',
    AccountNameAr as 'اسم الحساب بالعربية',
    AccountNameEn as 'اسم الحساب بالإنجليزية',
    CASE LevelType 
        WHEN 1 THEN N'رئيسي'
        WHEN 2 THEN N'فرعي'
        WHEN 3 THEN N'تفصيلي'
    END as 'مستوى الحساب',
    CASE Nature 
        WHEN 1 THEN N'مدين'
        WHEN 2 THEN N'دائن'
    END as 'طبيعة الحساب',
    CASE IsParent 
        WHEN 1 THEN N'نعم'
        WHEN 0 THEN N'لا'
    END as 'حساب أب',
    OpeningBalance as 'الرصيد الافتتاحي'
FROM ChartOfAccounts 
ORDER BY AccountCode;

PRINT N'تم إدراج 30 حساب محاسبي بنجاح مع ترميز عربي محسن';
