# دليل تشغيل البيانات الافتراضية للدليل المحاسبي
# Chart of Accounts Default Data Setup Guide

## 📋 نظرة عامة | Overview

هذا الدليل يوضح كيفية إعداد البيانات الافتراضية للدليل المحاسبي في نظام إدارة الأوقاف.

This guide explains how to setup default Chart of Accounts data for the Awqaf Management System.

## 🗃️ الملفات المتوفرة | Available Files

### 1. `InsertDefaultChartOfAccounts.sql`
- **الغرض**: إدراج البيانات الافتراضية الكاملة للدليل المحاسبي
- **يشمل**: أنواع الحسابات، مجموعات الحسابات، العملات، والحسابات (27 حساب)
- **Purpose**: Insert complete default Chart of Accounts data
- **Includes**: Account types, account groups, currencies, and accounts (27 accounts)

### 2. `ClearChartOfAccounts.sql`
- **الغرض**: حذف جميع بيانات الدليل المحاسبي وإعادة تعيين النظام
- **تحذير**: سيحذف جميع البيانات الموجودة!
- **Purpose**: Clear all Chart of Accounts data and reset the system
- **Warning**: Will delete all existing data!

## 🚀 خطوات التشغيل | Setup Steps

### الطريقة الأولى: استخدام SQL Server Management Studio

1. **فتح SQL Server Management Studio**
2. **الاتصال بالخادم**: `NAJEEB`
3. **التأكد من وجود قاعدة البيانات**: `AwqafManagement`
4. **تشغيل الملفات بالترتيب**:

```sql
-- إذا كنت تريد البدء من جديد (اختياري)
-- If you want to start fresh (optional)
-- تشغيل: ClearChartOfAccounts.sql

-- إدراج البيانات الافتراضية (مطلوب)
-- Insert default data (required)
-- تشغيل: InsertDefaultChartOfAccounts.sql
```

### الطريقة الثانية: استخدام سطر الأوامر

```cmd
# الانتقال إلى مجلد المشروع
cd "C:\Users\<USER>\Desktop\Awqaf_Managment\Database\Scripts"

# تشغيل البيانات الافتراضية
sqlcmd -S NAJEEB -E -i InsertDefaultChartOfAccounts.sql

# أو لحذف البيانات أولاً ثم الإدراج
sqlcmd -S NAJEEB -E -i ClearChartOfAccounts.sql
sqlcmd -S NAJEEB -E -i InsertDefaultChartOfAccounts.sql
```

## 📊 البيانات المُدرجة | Inserted Data

### أنواع الحسابات | Account Types
- الأصول (Assets)
- الخصوم (Liabilities) 
- حقوق الملكية (Equity)
- الإيرادات (Revenue)
- المصروفات (Expenses)

### العملات | Currencies
- ريال سعودي (SAR) - العملة الأساسية
- دولار أمريكي (USD)
- يورو (EUR)

### الحسابات | Accounts (27 حساب)

#### الأصول | Assets (1.xx.xxx)
- النقدية في الصندوق: 50,000 ر.س
- البنك - الحساب الجاري: 250,000 ر.س
- العملاء والمدينون: 75,000 ر.س
- المخزون: 120,000 ر.س
- الأراضي والمباني: 500,000 ر.س
- الأثاث والمعدات: 80,000 ر.س

#### الخصوم | Liabilities (2.xx.xxx)
- الموردون والدائنون: 45,000 ر.س
- مصروفات مستحقة: 15,000 ر.س

#### حقوق الملكية | Equity (3.xx.xxx)
- رأس المال المدفوع: 1,000,000 ر.س
- الأرباح المحتجزة: 35,000 ر.س

#### الإيرادات والمصروفات | Revenue & Expenses (4.xx.xxx & 5.xx.xxx)
- حسابات إيرادات ومصروفات جاهزة للاستخدام

## ✅ التحقق من النجاح | Verification

بعد تشغيل الملفات، يمكنك التحقق من نجاح العملية:

```sql
-- التحقق من عدد الحسابات
SELECT COUNT(*) as 'عدد الحسابات' FROM ChartOfAccounts;

-- عرض الحسابات الرئيسية
SELECT AccountCode, AccountNameAr, AccountNameEn 
FROM ChartOfAccounts 
WHERE LevelType = 'Main'
ORDER BY AccountCode;

-- التحقق من توازن الميزانية
SELECT 
    SUM(CASE WHEN Nature = 'Debit' THEN OpeningBalance ELSE -OpeningBalance END) as 'الرصيد الإجمالي'
FROM ChartOfAccounts;
```

## 🔧 استكشاف الأخطاء | Troubleshooting

### خطأ: قاعدة البيانات غير موجودة
```sql
-- تأكد من إنشاء قاعدة البيانات أولاً
-- Run the main database creation scripts first
```

### خطأ: انتهاك المفتاح الخارجي
```sql
-- تشغيل ملف الحذف أولاً
-- Run ClearChartOfAccounts.sql first
```

### خطأ: البيانات موجودة مسبقاً
```sql
-- استخدم ملف الحذف لإعادة التعيين
-- Use ClearChartOfAccounts.sql to reset
```

## 📞 الدعم | Support

في حالة وجود مشاكل:
1. تأكد من صحة اسم الخادم: `NAJEEB`
2. تأكد من وجود قاعدة البيانات: `AwqafManagement`
3. تأكد من صلاحيات المستخدم
4. راجع رسائل الخطأ في SQL Server

---
**تم إنشاؤه بواسطة**: نظام إدارة الأوقاف  
**التاريخ**: 2025-07-02  
**الإصدار**: 1.0
