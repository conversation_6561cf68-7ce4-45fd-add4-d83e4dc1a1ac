using System;
using System.ComponentModel.DataAnnotations;

namespace Awqaf_Managment.Models.Accounting
{
    /// <summary>
    /// نموذج تفاصيل القيد اليومي
    /// Journal Entry Detail Model
    /// </summary>
    public class JournalEntryDetail
    {
        #region Properties

        /// <summary>
        /// معرف تفصيل القيد
        /// Journal Entry Detail ID
        /// </summary>
        public int JournalEntryDetailId { get; set; }

        /// <summary>
        /// معرف القيد اليومي
        /// Journal Entry ID
        /// </summary>
        [Required]
        public int JournalEntryId { get; set; }

        /// <summary>
        /// معرف الحساب
        /// Account ID
        /// </summary>
        [Required]
        public int AccountId { get; set; }

        /// <summary>
        /// رقم السطر
        /// Line Number
        /// </summary>
        public int LineNumber { get; set; }

        /// <summary>
        /// المبلغ المدين
        /// Debit Amount
        /// </summary>
        public decimal DebitAmount { get; set; } = 0;

        /// <summary>
        /// المبلغ الدائن
        /// Credit Amount
        /// </summary>
        public decimal CreditAmount { get; set; } = 0;

        /// <summary>
        /// البيان
        /// Description
        /// </summary>
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// المرجع
        /// Reference
        /// </summary>
        [StringLength(100)]
        public string Reference { get; set; } = string.Empty;

        /// <summary>
        /// معرف مركز التكلفة
        /// Cost Center ID
        /// </summary>
        public int? CostCenterId { get; set; }

        /// <summary>
        /// تاريخ الإنشاء
        /// Created Date
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// اسم المستخدم المنشئ
        /// Created By User Name
        /// </summary>
        public string CreatedBy { get; set; } = string.Empty;

        #endregion

        #region Navigation Properties

        /// <summary>
        /// القيد اليومي
        /// Journal Entry
        /// </summary>
        public virtual JournalEntry JournalEntry { get; set; }

        /// <summary>
        /// الحساب
        /// Chart of Account
        /// </summary>
        public virtual ChartOfAccount ChartOfAccount { get; set; }

        /// <summary>
        /// مركز التكلفة
        /// Cost Center
        /// </summary>
        public virtual CostCenter CostCenter { get; set; }

        #endregion

        #region Constructors

        /// <summary>
        /// منشئ افتراضي
        /// Default Constructor
        /// </summary>
        public JournalEntryDetail()
        {
            CreatedDate = DateTime.Now;
            DebitAmount = 0;
            CreditAmount = 0;
        }

        #endregion

        #region Methods

        /// <summary>
        /// التحقق من صحة التفصيل
        /// Validate detail
        /// </summary>
        /// <returns>رسالة الخطأ أو null إذا كان صحيح</returns>
        public string Validate()
        {
            if (AccountId <= 0)
                return "يجب اختيار حساب";

            if (DebitAmount < 0 || CreditAmount < 0)
                return "المبالغ يجب أن تكون موجبة";

            if (DebitAmount > 0 && CreditAmount > 0)
                return "لا يمكن أن يكون للسطر مبلغ مدين ودائن في نفس الوقت";

            if (DebitAmount == 0 && CreditAmount == 0)
                return "يجب إدخال مبلغ مدين أو دائن";

            return null;
        }

        /// <summary>
        /// الحصول على المبلغ
        /// Get amount
        /// </summary>
        /// <returns>المبلغ</returns>
        public decimal GetAmount()
        {
            return DebitAmount > 0 ? DebitAmount : CreditAmount;
        }

        /// <summary>
        /// الحصول على نوع المبلغ
        /// Get amount type
        /// </summary>
        /// <returns>نوع المبلغ</returns>
        public string GetAmountType()
        {
            return DebitAmount > 0 ? "مدين" : "دائن";
        }

        /// <summary>
        /// تمثيل نصي للتفصيل
        /// String representation
        /// </summary>
        /// <returns>النص الممثل للتفصيل</returns>
        public override string ToString()
        {
            var amount = GetAmount();
            var type = GetAmountType();
            return $"السطر {LineNumber}: {amount:N2} {type} - {Description}";
        }

        #endregion
    }
}
