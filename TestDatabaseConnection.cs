using System;
using System.Data.SqlClient;
using System.Text;
using System.Windows.Forms;
using Awqaf_Managment.DataAccess;

namespace Awqaf_Managment
{
    /// <summary>
    /// أداة اختبار الاتصال بقاعدة البيانات
    /// </summary>
    public static class TestDatabaseConnection
    {
        public static void TestConnection()
        {
            var result = new StringBuilder();

            try
            {
                result.AppendLine("اختبار الاتصال بقاعدة البيانات...");
                result.AppendLine($"سلسلة الاتصال: {DatabaseConnection.ConnectionString}");
                result.AppendLine();

                using (var connection = new SqlConnection(DatabaseConnection.ConnectionString))
                {
                    connection.Open();
                    result.AppendLine("✅ تم الاتصال بقاعدة البيانات بنجاح");

                    // اختبار وجود جدول العملات
                    using (var command = new SqlCommand("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Currencies'", connection))
                    {
                        var tableExists = Convert.ToInt32(command.ExecuteScalar()) > 0;
                        result.AppendLine($"جدول العملات: {(tableExists ? "موجود ✅" : "غير موجود ❌")}");

                        if (tableExists)
                        {
                            // اختبار عدد العملات
                            using (var countCommand = new SqlCommand("SELECT COUNT(*) FROM Currencies", connection))
                            {
                                var currencyCount = Convert.ToInt32(countCommand.ExecuteScalar());
                                result.AppendLine($"عدد العملات في قاعدة البيانات: {currencyCount}");
                            }
                        }
                    }

                    result.AppendLine();
                    result.AppendLine("الإجراءات المخزنة:");

                    // اختبار وجود الإجراءات المخزنة
                    string[] procedures = { "SP_GetAllCurrencies", "SP_AddCurrency", "SP_UpdateCurrency", "SP_DeleteCurrency" };
                    foreach (var proc in procedures)
                    {
                        using (var command = new SqlCommand($"SELECT COUNT(*) FROM INFORMATION_SCHEMA.ROUTINES WHERE ROUTINE_NAME = '{proc}'", connection))
                        {
                            var procExists = Convert.ToInt32(command.ExecuteScalar()) > 0;
                            result.AppendLine($"  {proc}: {(procExists ? "موجود ✅" : "غير موجود ❌")}");
                        }
                    }
                }

                MessageBox.Show(result.ToString(), "نتائج اختبار قاعدة البيانات", MessageBoxButtons.OK, MessageBoxIcon.Information, MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
            catch (Exception ex)
            {
                result.AppendLine($"❌ خطأ في الاتصال: {ex.Message}");
                if (ex.InnerException != null)
                {
                    result.AppendLine($"التفاصيل: {ex.InnerException.Message}");
                }

                MessageBox.Show(result.ToString(), "خطأ في اختبار قاعدة البيانات", MessageBoxButtons.OK, MessageBoxIcon.Error, MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
        }
    }
}
