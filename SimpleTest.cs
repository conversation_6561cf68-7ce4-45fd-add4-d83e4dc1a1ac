using System;
using System.Collections.Generic;
using System.Linq;
using System.Data.SqlClient;

namespace Awqaf_Managment.Test
{
    /// <summary>
    /// اختبار بسيط لتحميل البيانات
    /// Simple Data Loading Test
    /// </summary>
    public class SimpleDataTest
    {
        private static string connectionString = "Server=NAJEEB;Database=AwqafManagement;Integrated Security=true;TrustServerCertificate=true;";

        public static void Main(string[] args)
        {
            Console.OutputEncoding = System.Text.Encoding.UTF8;
            Console.WriteLine("=== اختبار تحميل البيانات ===");
            Console.WriteLine("=== Data Loading Test ===");
            Console.WriteLine();

            try
            {
                TestDatabaseConnection();
                TestAccountsData();
                TestAccountTypesData();
                TestAccountGroupsData();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ عام: {ex.Message}");
                Console.WriteLine($"General Error: {ex.Message}");
            }

            Console.WriteLine();
            Console.WriteLine("اضغط أي مفتاح للخروج...");
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }

        private static void TestDatabaseConnection()
        {
            Console.WriteLine("1. اختبار الاتصال بقاعدة البيانات...");
            Console.WriteLine("1. Testing database connection...");

            try
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    Console.WriteLine("✓ تم الاتصال بقاعدة البيانات بنجاح");
                    Console.WriteLine("✓ Database connection successful");
                    
                    var command = new SqlCommand("SELECT DB_NAME()", connection);
                    var dbName = command.ExecuteScalar().ToString();
                    Console.WriteLine($"   اسم قاعدة البيانات: {dbName}");
                    Console.WriteLine($"   Database name: {dbName}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ فشل الاتصال بقاعدة البيانات: {ex.Message}");
                Console.WriteLine($"❌ Database connection failed: {ex.Message}");
                throw;
            }

            Console.WriteLine();
        }

        private static void TestAccountsData()
        {
            Console.WriteLine("2. اختبار بيانات الحسابات...");
            Console.WriteLine("2. Testing accounts data...");

            try
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    
                    var query = @"
                        SELECT 
                            coa.AccountId,
                            coa.AccountCode,
                            coa.AccountName,
                            coa.AccountNameAr,
                            coa.AccountLevel,
                            coa.ParentAccountId,
                            coa.IsParent,
                            coa.IsActive,
                            coa.OpeningBalance
                        FROM ChartOfAccounts coa
                        WHERE coa.IsActive = 1
                        ORDER BY coa.AccountCode";

                    var command = new SqlCommand(query, connection);
                    var reader = command.ExecuteReader();

                    var accounts = new List<dynamic>();
                    while (reader.Read())
                    {
                        accounts.Add(new
                        {
                            AccountId = reader.GetInt32("AccountId"),
                            AccountCode = reader.GetString("AccountCode"),
                            AccountName = reader.GetString("AccountName"),
                            AccountNameAr = reader.GetString("AccountNameAr"),
                            AccountLevel = reader.GetInt32("AccountLevel"),
                            ParentAccountId = reader.IsDBNull("ParentAccountId") ? (int?)null : reader.GetInt32("ParentAccountId"),
                            IsParent = reader.GetBoolean("IsParent"),
                            IsActive = reader.GetBoolean("IsActive"),
                            OpeningBalance = reader.GetDecimal("OpeningBalance")
                        });
                    }

                    Console.WriteLine($"   عدد الحسابات المحملة: {accounts.Count}");
                    Console.WriteLine($"   Number of accounts loaded: {accounts.Count}");

                    if (accounts.Count > 0)
                    {
                        Console.WriteLine("   أول 5 حسابات:");
                        Console.WriteLine("   First 5 accounts:");
                        
                        foreach (var account in accounts.Take(5))
                        {
                            Console.WriteLine($"     {account.AccountCode} - {account.AccountNameAr} (المستوى: {account.AccountLevel})");
                        }

                        var mainAccounts = accounts.Where(a => a.ParentAccountId == null).Count();
                        var subAccounts = accounts.Where(a => a.ParentAccountId != null).Count();
                        
                        Console.WriteLine($"   الحسابات الرئيسية: {mainAccounts}");
                        Console.WriteLine($"   الحسابات الفرعية: {subAccounts}");
                        Console.WriteLine($"   Main accounts: {mainAccounts}");
                        Console.WriteLine($"   Sub accounts: {subAccounts}");
                    }
                    else
                    {
                        Console.WriteLine("❌ لا توجد حسابات في قاعدة البيانات");
                        Console.WriteLine("❌ No accounts found in database");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في تحميل بيانات الحسابات: {ex.Message}");
                Console.WriteLine($"❌ Error loading accounts data: {ex.Message}");
            }

            Console.WriteLine();
        }

        private static void TestAccountTypesData()
        {
            Console.WriteLine("3. اختبار بيانات أنواع الحسابات...");
            Console.WriteLine("3. Testing account types data...");

            try
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    
                    var query = "SELECT COUNT(*) FROM AccountTypes WHERE IsActive = 1";
                    var command = new SqlCommand(query, connection);
                    var count = (int)command.ExecuteScalar();

                    Console.WriteLine($"   عدد أنواع الحسابات: {count}");
                    Console.WriteLine($"   Number of account types: {count}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في تحميل أنواع الحسابات: {ex.Message}");
                Console.WriteLine($"❌ Error loading account types: {ex.Message}");
            }

            Console.WriteLine();
        }

        private static void TestAccountGroupsData()
        {
            Console.WriteLine("4. اختبار بيانات مجموعات الحسابات...");
            Console.WriteLine("4. Testing account groups data...");

            try
            {
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    
                    var query = "SELECT COUNT(*) FROM AccountGroups WHERE IsActive = 1";
                    var command = new SqlCommand(query, connection);
                    var count = (int)command.ExecuteScalar();

                    Console.WriteLine($"   عدد مجموعات الحسابات: {count}");
                    Console.WriteLine($"   Number of account groups: {count}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في تحميل مجموعات الحسابات: {ex.Message}");
                Console.WriteLine($"❌ Error loading account groups: {ex.Message}");
            }

            Console.WriteLine();
        }
    }
}
