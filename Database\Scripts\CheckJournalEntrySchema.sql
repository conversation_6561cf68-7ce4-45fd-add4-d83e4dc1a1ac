-- فحص مخطط جدول القيود اليومية
-- Check Journal Entry Schema
USE AwqafManagement
GO

PRINT 'فحص مخطط جدول القيود اليومية...'

-- فحص الأعمدة في جدول JournalEntries
SELECT 
    c.name AS ColumnName,
    t.name AS DataType,
    c.max_length,
    c.is_nullable
FROM sys.columns c
INNER JOIN sys.types t ON c.system_type_id = t.system_type_id
WHERE c.object_id = OBJECT_ID('JournalEntries')
AND c.name IN ('CreatedBy', 'ModifiedBy', 'ApprovedBy', 'PostedBy')
ORDER BY c.name

PRINT 'فحص الأعمدة في جدول JournalEntryDetails...'

-- فحص الأعمدة في جدول JournalEntryDetails
SELECT 
    c.name AS ColumnName,
    t.name AS DataType,
    c.max_length,
    c.is_nullable
FROM sys.columns c
INNER JOIN sys.types t ON c.system_type_id = t.system_type_id
WHERE c.object_id = OBJECT_ID('JournalEntryDetails')
AND c.name = 'CreatedBy'

GO
