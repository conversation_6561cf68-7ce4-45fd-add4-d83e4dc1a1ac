using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using Awqaf_Managment.Common.Helpers;
using Awqaf_Managment.DataAccess.Security;
using Awqaf_Managment.Models.Security;

namespace Awqaf_Managment.UI.Forms.Security
{
    public partial class AddEditUserForm : Form
    {
        private User _currentUser;
        private bool _isEditMode;
        private List<Role> _availableRoles;

        public AddEditUserForm()
        {
            InitializeComponent();
            InitializeForm();
        }

        public AddEditUserForm(User user) : this()
        {
            _currentUser = user;
            _isEditMode = true;
            LoadUserData();
        }

        private void InitializeForm()
        {
            // تطبيق دعم RTL
            UIHelper.ApplyRTLSupport(this);

            // تحميل الأدوار المتاحة
            LoadRoles();
            
            // إعداد أحداث الأزرار
            btnSave.Click += BtnSave_Click;
            btnCancel.Click += BtnCancel_Click;
            
            // إعداد التحقق من صحة البيانات
            txtUsername.Leave += TxtUsername_Leave;
            txtEmail.Leave += TxtEmail_Leave;
            txtPassword.Leave += TxtPassword_Leave;
            txtConfirmPassword.Leave += TxtConfirmPassword_Leave;
            
            // تحديث عنوان النافذة
            UpdateTitle();
        }

        private void UpdateTitle()
        {
            if (_isEditMode)
            {
                lblTitle.Text = "تعديل بيانات المستخدم";
                this.Text = "تعديل مستخدم";
                
                // إخفاء حقول كلمة المرور في وضع التعديل
                lblPassword.Text = "كلمة المرور الجديدة (اتركها فارغة للاحتفاظ بالحالية):";
                lblConfirmPassword.Text = "تأكيد كلمة المرور الجديدة:";
            }
            else
            {
                lblTitle.Text = "إضافة مستخدم جديد";
                this.Text = "إضافة مستخدم";
            }
        }

        private void LoadRoles()
        {
            try
            {
                _availableRoles = RoleDataAccess.GetAllRoles();
                clbRoles.Items.Clear();
                
                foreach (var role in _availableRoles)
                {
                    // التأكد من عرض النص العربي بشكل صحيح
                    string displayText = !string.IsNullOrEmpty(role.RoleNameAr)
                        ? $"{role.RoleNameAr} ({role.RoleName})"
                        : role.RoleName;
                    clbRoles.Items.Add(displayText, false);
                }
            }
            catch (Exception ex)
            {
                UIHelper.ShowError($"خطأ في تحميل الأدوار: {ex.Message}");
            }
        }

        private void LoadUserData()
        {
            if (_currentUser == null) return;

            try
            {
                txtUsername.Text = _currentUser.Username;
                txtFullName.Text = _currentUser.FullName;
                txtEmail.Text = _currentUser.Email;
                chkIsActive.Checked = _currentUser.IsActive;
                
                // تحميل أدوار المستخدم
                var userRoles = UserDataAccess.GetUserRoles(_currentUser.UserId);
                for (int i = 0; i < _availableRoles.Count; i++)
                {
                    bool isAssigned = userRoles.Any(ur => ur.RoleId == _availableRoles[i].RoleId);
                    clbRoles.SetItemChecked(i, isAssigned);
                }
                
                // منع تعديل اسم المستخدم في وضع التعديل
                txtUsername.ReadOnly = true;
                txtUsername.BackColor = System.Drawing.Color.LightGray;
            }
            catch (Exception ex)
            {
                UIHelper.ShowError($"خطأ في تحميل بيانات المستخدم: {ex.Message}");
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (!ValidateInput()) return;

            try
            {
                if (_isEditMode)
                {
                    UpdateUser();
                }
                else
                {
                    CreateUser();
                }

                UIHelper.ShowSuccess("تم حفظ البيانات بنجاح");
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                UIHelper.ShowError($"خطأ في حفظ البيانات: {ex.Message}");
            }
        }

        private void CreateUser()
        {
            var newUser = new User
            {
                Username = txtUsername.Text.Trim(),
                FullName = txtFullName.Text.Trim(),
                Email = txtEmail.Text.Trim(),
                IsActive = chkIsActive.Checked
            };

            // إنشاء المستخدم
            int userId = UserDataAccess.CreateUser(newUser, txtPassword.Text);

            // تعيين الأدوار
            AssignUserRoles(userId);
        }

        private void UpdateUser()
        {
            _currentUser.FullName = txtFullName.Text.Trim();
            _currentUser.Email = txtEmail.Text.Trim();
            _currentUser.IsActive = chkIsActive.Checked;

            // تحديث بيانات المستخدم
            UserDataAccess.UpdateUser(_currentUser);

            // تحديث كلمة المرور إذا تم إدخال واحدة جديدة
            if (!string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                UserDataAccess.UpdateUserPassword(_currentUser.UserId, txtPassword.Text);
            }

            // تحديث الأدوار
            AssignUserRoles(_currentUser.UserId);
        }

        private void AssignUserRoles(int userId)
        {
            var selectedRoles = new List<int>();
            
            for (int i = 0; i < clbRoles.Items.Count; i++)
            {
                if (clbRoles.GetItemChecked(i))
                {
                    selectedRoles.Add(_availableRoles[i].RoleId);
                }
            }

            UserDataAccess.UpdateUserRoles(userId, selectedRoles);
        }

        private bool ValidateInput()
        {
            // التحقق من اسم المستخدم
            if (string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                UIHelper.ShowWarning("يرجى إدخال اسم المستخدم");
                txtUsername.Focus();
                return false;
            }

            // التحقق من الاسم الكامل
            if (string.IsNullOrWhiteSpace(txtFullName.Text))
            {
                UIHelper.ShowWarning("يرجى إدخال الاسم الكامل");
                txtFullName.Focus();
                return false;
            }

            // التحقق من البريد الإلكتروني
            if (string.IsNullOrWhiteSpace(txtEmail.Text) || !IsValidEmail(txtEmail.Text))
            {
                UIHelper.ShowWarning("يرجى إدخال بريد إلكتروني صحيح");
                txtEmail.Focus();
                return false;
            }

            // التحقق من كلمة المرور (في وضع الإضافة أو إذا تم إدخال كلمة مرور جديدة)
            if (!_isEditMode || !string.IsNullOrWhiteSpace(txtPassword.Text))
            {
                if (string.IsNullOrWhiteSpace(txtPassword.Text))
                {
                    UIHelper.ShowWarning("يرجى إدخال كلمة المرور");
                    txtPassword.Focus();
                    return false;
                }

                if (txtPassword.Text.Length < 6)
                {
                    UIHelper.ShowWarning("كلمة المرور يجب أن تكون 6 أحرف على الأقل");
                    txtPassword.Focus();
                    return false;
                }

                if (txtPassword.Text != txtConfirmPassword.Text)
                {
                    UIHelper.ShowWarning("كلمة المرور وتأكيدها غير متطابقتين");
                    txtConfirmPassword.Focus();
                    return false;
                }
            }

            // التحقق من تحديد دور واحد على الأقل
            if (clbRoles.CheckedItems.Count == 0)
            {
                UIHelper.ShowWarning("يرجى تحديد دور واحد على الأقل للمستخدم");
                return false;
            }

            return true;
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private void TxtUsername_Leave(object sender, EventArgs e)
        {
            if (!_isEditMode && !string.IsNullOrWhiteSpace(txtUsername.Text))
            {
                // التحقق من عدم وجود اسم المستخدم مسبقاً
                if (UserDataAccess.IsUsernameExists(txtUsername.Text.Trim()))
                {
                    UIHelper.ShowWarning("اسم المستخدم موجود مسبقاً، يرجى اختيار اسم آخر");
                    txtUsername.Focus();
                }
            }
        }

        private void TxtEmail_Leave(object sender, EventArgs e)
        {
            if (!string.IsNullOrWhiteSpace(txtEmail.Text) && !IsValidEmail(txtEmail.Text))
            {
                UIHelper.ShowWarning("تنسيق البريد الإلكتروني غير صحيح");
                txtEmail.Focus();
            }
        }

        private void TxtPassword_Leave(object sender, EventArgs e)
        {
            if (!string.IsNullOrWhiteSpace(txtPassword.Text) && txtPassword.Text.Length < 6)
            {
                UIHelper.ShowWarning("كلمة المرور يجب أن تكون 6 أحرف على الأقل");
                txtPassword.Focus();
            }
        }

        private void TxtConfirmPassword_Leave(object sender, EventArgs e)
        {
            if (!string.IsNullOrWhiteSpace(txtConfirmPassword.Text) && 
                txtPassword.Text != txtConfirmPassword.Text)
            {
                UIHelper.ShowWarning("كلمة المرور وتأكيدها غير متطابقتين");
                txtConfirmPassword.Focus();
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
