using System;
using System.Collections.Generic;
using System.Linq;
using Awqaf_Managment.Models.Accounting;
using Awqaf_Managment.DataAccess.Accounting;

namespace Awqaf_Managment.Services.Accounting
{
    public class ChartOfAccountsService
    {
        public static List<ChartOfAccount> GetAllAccounts()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("ChartOfAccountsService: محاولة تحميل الحسابات من قاعدة البيانات");
                var accounts = ChartOfAccountsDataAccess.GetAllAccounts();
                System.Diagnostics.Debug.WriteLine($"ChartOfAccountsService: تم تحميل {accounts.Count} حساب من قاعدة البيانات");
                return accounts;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"ChartOfAccountsService: خطأ في تحميل البيانات: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"ChartOfAccountsService: استخدام البيانات التجريبية");

                // في حالة فشل قاعدة البيانات، إرجاع بيانات تجريبية
                var sampleAccounts = GetSampleAccounts();
                System.Diagnostics.Debug.WriteLine($"ChartOfAccountsService: تم إنشاء {sampleAccounts.Count} حساب تجريبي");
                return sampleAccounts;
            }
        }

        public static bool AddAccount(ChartOfAccount account)
        {
            try
            {
                int result = ChartOfAccountsDataAccess.AddAccount(account);
                return result > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"ChartOfAccountsService: خطأ في إضافة الحساب: {ex.Message}");
                return false;
            }
        }

        public static bool UpdateAccount(ChartOfAccount account)
        {
            try
            {
                return ChartOfAccountsDataAccess.UpdateAccount(account);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"ChartOfAccountsService: خطأ في تحديث الحساب: {ex.Message}");
                return false;
            }
        }

        public static bool DeleteAccount(int accountId)
        {
            try
            {
                return ChartOfAccountsDataAccess.DeleteAccount(accountId);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"ChartOfAccountsService: خطأ في حذف الحساب: {ex.Message}");
                return false;
            }
        }

        public static List<AccountType> GetAllAccountTypes()
        {
            try
            {
                return ChartOfAccountsDataAccess.GetAllAccountTypes();
            }
            catch (Exception)
            {
                // في حالة فشل قاعدة البيانات، إرجاع بيانات تجريبية
                return GetSampleAccountTypes();
            }
        }

        public static List<AccountGroup> GetAllAccountGroups()
        {
            try
            {
                return ChartOfAccountsDataAccess.GetAllAccountGroups();
            }
            catch (Exception)
            {
                // في حالة فشل قاعدة البيانات، إرجاع بيانات تجريبية
                return GetSampleAccountGroups();
            }
        }

        private static List<ChartOfAccount> GetSampleAccounts()
        {
            return new List<ChartOfAccount>
            {
                // ========== الأصول ==========
                new ChartOfAccount
                {
                    AccountId = 1,
                    AccountCode = "1.00.000",
                    AccountNameAr = "الأصول",
                    AccountNameEn = "Assets",
                    AccountTypeId = 1,
                    AccountGroupId = 1,
                    LevelType = AccountLevelType.Main,
                    Nature = AccountNature.Debit,
                    IsParent = true,
                    IsActive = true,
                    AllowPosting = false,
                    CurrencyId = 1,
                    OpeningBalance = 0,
                    CreatedDate = DateTime.Now,
                },

                // الأصول المتداولة
                new ChartOfAccount
                {
                    AccountId = 2,
                    AccountCode = "1.01.000",
                    AccountNameAr = "الأصول المتداولة",
                    AccountNameEn = "Current Assets",
                    AccountTypeId = 1,
                    AccountGroupId = 1,
                    LevelType = AccountLevelType.Sub,
                    Nature = AccountNature.Debit,
                    ParentAccountId = 1,
                    IsParent = true,
                    IsActive = true,
                    AllowPosting = false,
                    CurrencyId = 1,
                    OpeningBalance = 0,
                    CreatedDate = DateTime.Now,
                },

                new ChartOfAccount
                {
                    AccountId = 3,
                    AccountCode = "1.01.001",
                    AccountNameAr = "النقدية في الصندوق",
                    AccountNameEn = "Cash in Hand",
                    AccountTypeId = 1,
                    AccountGroupId = 1,
                    LevelType = AccountLevelType.Detail,
                    Nature = AccountNature.Debit,
                    ParentAccountId = 2,
                    IsParent = false,
                    IsActive = true,
                    AllowPosting = true,
                    CurrencyId = 1,
                    OpeningBalance = 50000,
                    CreatedDate = DateTime.Now,
                },

                new ChartOfAccount
                {
                    AccountId = 4,
                    AccountCode = "1.01.002",
                    AccountNameAr = "البنك - الحساب الجاري",
                    AccountNameEn = "Bank - Current Account",
                    AccountTypeId = 1,
                    AccountGroupId = 1,
                    LevelType = AccountLevelType.Detail,
                    Nature = AccountNature.Debit,
                    ParentAccountId = 2,
                    IsParent = false,
                    IsActive = true,
                    AllowPosting = true,
                    CurrencyId = 1,
                    OpeningBalance = 250000,
                    CreatedDate = DateTime.Now,
                },

                new ChartOfAccount
                {
                    AccountId = 5,
                    AccountCode = "1.01.003",
                    AccountNameAr = "العملاء والمدينون",
                    AccountNameEn = "Accounts Receivable",
                    AccountTypeId = 1,
                    AccountGroupId = 1,
                    LevelType = AccountLevelType.Detail,
                    Nature = AccountNature.Debit,
                    ParentAccountId = 2,
                    IsParent = false,
                    IsActive = true,
                    AllowPosting = true,
                    CurrencyId = 1,
                    OpeningBalance = 75000,
                    CreatedDate = DateTime.Now,
                },

                new ChartOfAccount
                {
                    AccountId = 6,
                    AccountCode = "1.01.004",
                    AccountNameAr = "المخزون",
                    AccountNameEn = "Inventory",
                    AccountTypeId = 1,
                    AccountGroupId = 1,
                    LevelType = AccountLevelType.Detail,
                    Nature = AccountNature.Debit,
                    ParentAccountId = 2,
                    IsParent = false,
                    IsActive = true,
                    AllowPosting = true,
                    CurrencyId = 1,
                    OpeningBalance = 120000,
                    CreatedDate = DateTime.Now,
                },

                // الأصول الثابتة
                new ChartOfAccount
                {
                    AccountId = 7,
                    AccountCode = "1.02.000",
                    AccountNameAr = "الأصول الثابتة",
                    AccountNameEn = "Fixed Assets",
                    AccountTypeId = 1,
                    AccountGroupId = 2,
                    LevelType = AccountLevelType.Sub,
                    Nature = AccountNature.Debit,
                    ParentAccountId = 1,
                    IsParent = true,
                    IsActive = true,
                    AllowPosting = false,
                    CurrencyId = 1,
                    OpeningBalance = 0,
                    CreatedDate = DateTime.Now,
                },

                new ChartOfAccount
                {
                    AccountId = 8,
                    AccountCode = "1.02.001",
                    AccountNameAr = "الأراضي والمباني",
                    AccountNameEn = "Land and Buildings",
                    AccountTypeId = 1,
                    AccountGroupId = 2,
                    LevelType = AccountLevelType.Detail,
                    Nature = AccountNature.Debit,
                    ParentAccountId = 7,
                    IsParent = false,
                    IsActive = true,
                    AllowPosting = true,
                    CurrencyId = 1,
                    OpeningBalance = 500000,
                    CreatedDate = DateTime.Now,
                },

                new ChartOfAccount
                {
                    AccountId = 9,
                    AccountCode = "1.02.002",
                    AccountNameAr = "الأثاث والمعدات",
                    AccountNameEn = "Furniture and Equipment",
                    AccountTypeId = 1,
                    AccountGroupId = 2,
                    LevelType = AccountLevelType.Detail,
                    Nature = AccountNature.Debit,
                    ParentAccountId = 7,
                    IsParent = false,
                    IsActive = true,
                    AllowPosting = true,
                    CurrencyId = 1,
                    OpeningBalance = 80000,
                    CreatedDate = DateTime.Now,
                },

                // ========== الخصوم ==========
                new ChartOfAccount
                {
                    AccountId = 10,
                    AccountCode = "2.00.000",
                    AccountNameAr = "الخصوم",
                    AccountNameEn = "Liabilities",
                    AccountTypeId = 2,
                    AccountGroupId = 3,
                    LevelType = AccountLevelType.Main,
                    Nature = AccountNature.Credit,
                    IsParent = true,
                    IsActive = true,
                    AllowPosting = false,
                    CurrencyId = 1,
                    OpeningBalance = 0,
                    CreatedDate = DateTime.Now,
                },

                // الخصوم المتداولة
                new ChartOfAccount
                {
                    AccountId = 11,
                    AccountCode = "2.01.000",
                    AccountNameAr = "الخصوم المتداولة",
                    AccountNameEn = "Current Liabilities",
                    AccountTypeId = 2,
                    AccountGroupId = 3,
                    LevelType = AccountLevelType.Sub,
                    Nature = AccountNature.Credit,
                    ParentAccountId = 10,
                    IsParent = true,
                    IsActive = true,
                    AllowPosting = false,
                    CurrencyId = 1,
                    OpeningBalance = 0,
                    CreatedDate = DateTime.Now,
                },

                new ChartOfAccount
                {
                    AccountId = 12,
                    AccountCode = "2.01.001",
                    AccountNameAr = "الموردون والدائنون",
                    AccountNameEn = "Accounts Payable",
                    AccountTypeId = 2,
                    AccountGroupId = 3,
                    LevelType = AccountLevelType.Detail,
                    Nature = AccountNature.Credit,
                    ParentAccountId = 11,
                    IsParent = false,
                    IsActive = true,
                    AllowPosting = true,
                    CurrencyId = 1,
                    OpeningBalance = 45000,
                    CreatedDate = DateTime.Now,
                },

                new ChartOfAccount
                {
                    AccountId = 13,
                    AccountCode = "2.01.002",
                    AccountNameAr = "مصروفات مستحقة",
                    AccountNameEn = "Accrued Expenses",
                    AccountTypeId = 2,
                    AccountGroupId = 3,
                    LevelType = AccountLevelType.Detail,
                    Nature = AccountNature.Credit,
                    ParentAccountId = 11,
                    IsParent = false,
                    IsActive = true,
                    AllowPosting = true,
                    CurrencyId = 1,
                    OpeningBalance = 15000,
                    CreatedDate = DateTime.Now,
                },

                // ========== حقوق الملكية ==========
                new ChartOfAccount
                {
                    AccountId = 14,
                    AccountCode = "3.00.000",
                    AccountNameAr = "حقوق الملكية",
                    AccountNameEn = "Equity",
                    AccountTypeId = 3,
                    AccountGroupId = 5,
                    LevelType = AccountLevelType.Main,
                    Nature = AccountNature.Credit,
                    IsParent = true,
                    IsActive = true,
                    AllowPosting = false,
                    CurrencyId = 1,
                    OpeningBalance = 0,
                    CreatedDate = DateTime.Now,
                },

                new ChartOfAccount
                {
                    AccountId = 15,
                    AccountCode = "3.01.000",
                    AccountNameAr = "رأس المال",
                    AccountNameEn = "Capital",
                    AccountTypeId = 3,
                    AccountGroupId = 5,
                    LevelType = AccountLevelType.Sub,
                    Nature = AccountNature.Credit,
                    ParentAccountId = 14,
                    IsParent = true,
                    IsActive = true,
                    AllowPosting = false,
                    CurrencyId = 1,
                    OpeningBalance = 0,
                    CreatedDate = DateTime.Now,
                },

                new ChartOfAccount
                {
                    AccountId = 16,
                    AccountCode = "3.01.001",
                    AccountNameAr = "رأس المال المدفوع",
                    AccountNameEn = "Paid-up Capital",
                    AccountTypeId = 3,
                    AccountGroupId = 5,
                    LevelType = AccountLevelType.Detail,
                    Nature = AccountNature.Credit,
                    ParentAccountId = 15,
                    IsParent = false,
                    IsActive = true,
                    AllowPosting = true,
                    CurrencyId = 1,
                    OpeningBalance = 1000000,
                    CreatedDate = DateTime.Now,
                },

                new ChartOfAccount
                {
                    AccountId = 17,
                    AccountCode = "3.01.002",
                    AccountNameAr = "الأرباح المحتجزة",
                    AccountNameEn = "Retained Earnings",
                    AccountTypeId = 3,
                    AccountGroupId = 5,
                    LevelType = AccountLevelType.Detail,
                    Nature = AccountNature.Credit,
                    ParentAccountId = 15,
                    IsParent = false,
                    IsActive = true,
                    AllowPosting = true,
                    CurrencyId = 1,
                    OpeningBalance = 35000,
                    CreatedDate = DateTime.Now,
                },

                // ========== الإيرادات ==========
                new ChartOfAccount
                {
                    AccountId = 18,
                    AccountCode = "4.00.000",
                    AccountNameAr = "الإيرادات",
                    AccountNameEn = "Revenue",
                    AccountTypeId = 4,
                    AccountGroupId = 6,
                    LevelType = AccountLevelType.Main,
                    Nature = AccountNature.Credit,
                    IsParent = true,
                    IsActive = true,
                    AllowPosting = false,
                    CurrencyId = 1,
                    OpeningBalance = 0,
                    CreatedDate = DateTime.Now,
                },

                new ChartOfAccount
                {
                    AccountId = 19,
                    AccountCode = "4.01.000",
                    AccountNameAr = "إيرادات التشغيل",
                    AccountNameEn = "Operating Revenue",
                    AccountTypeId = 4,
                    AccountGroupId = 6,
                    LevelType = AccountLevelType.Sub,
                    Nature = AccountNature.Credit,
                    ParentAccountId = 18,
                    IsParent = true,
                    IsActive = true,
                    AllowPosting = false,
                    CurrencyId = 1,
                    OpeningBalance = 0,
                    CreatedDate = DateTime.Now,
                },

                new ChartOfAccount
                {
                    AccountId = 20,
                    AccountCode = "4.01.001",
                    AccountNameAr = "إيرادات المبيعات",
                    AccountNameEn = "Sales Revenue",
                    AccountTypeId = 4,
                    AccountGroupId = 6,
                    LevelType = AccountLevelType.Detail,
                    Nature = AccountNature.Credit,
                    ParentAccountId = 19,
                    IsParent = false,
                    IsActive = true,
                    AllowPosting = true,
                    CurrencyId = 1,
                    OpeningBalance = 0,
                    CreatedDate = DateTime.Now,
                },

                new ChartOfAccount
                {
                    AccountId = 21,
                    AccountCode = "4.01.002",
                    AccountNameAr = "إيرادات الخدمات",
                    AccountNameEn = "Service Revenue",
                    AccountTypeId = 4,
                    AccountGroupId = 6,
                    LevelType = AccountLevelType.Detail,
                    Nature = AccountNature.Credit,
                    ParentAccountId = 19,
                    IsParent = false,
                    IsActive = true,
                    AllowPosting = true,
                    CurrencyId = 1,
                    OpeningBalance = 0,
                    CreatedDate = DateTime.Now,
                },

                // ========== المصروفات ==========
                new ChartOfAccount
                {
                    AccountId = 22,
                    AccountCode = "5.00.000",
                    AccountNameAr = "المصروفات",
                    AccountNameEn = "Expenses",
                    AccountTypeId = 5,
                    AccountGroupId = 7,
                    LevelType = AccountLevelType.Main,
                    Nature = AccountNature.Debit,
                    IsParent = true,
                    IsActive = true,
                    AllowPosting = false,
                    CurrencyId = 1,
                    OpeningBalance = 0,
                    CreatedDate = DateTime.Now,
                },

                new ChartOfAccount
                {
                    AccountId = 23,
                    AccountCode = "5.01.000",
                    AccountNameAr = "مصروفات التشغيل",
                    AccountNameEn = "Operating Expenses",
                    AccountTypeId = 5,
                    AccountGroupId = 7,
                    LevelType = AccountLevelType.Sub,
                    Nature = AccountNature.Debit,
                    ParentAccountId = 22,
                    IsParent = true,
                    IsActive = true,
                    AllowPosting = false,
                    CurrencyId = 1,
                    OpeningBalance = 0,
                    CreatedDate = DateTime.Now,
                },

                new ChartOfAccount
                {
                    AccountId = 24,
                    AccountCode = "5.01.001",
                    AccountNameAr = "رواتب وأجور",
                    AccountNameEn = "Salaries and Wages",
                    AccountTypeId = 5,
                    AccountGroupId = 7,
                    LevelType = AccountLevelType.Detail,
                    Nature = AccountNature.Debit,
                    ParentAccountId = 23,
                    IsParent = false,
                    IsActive = true,
                    AllowPosting = true,
                    CurrencyId = 1,
                    OpeningBalance = 0,
                    CreatedDate = DateTime.Now,
                },

                new ChartOfAccount
                {
                    AccountId = 25,
                    AccountCode = "5.01.002",
                    AccountNameAr = "إيجارات",
                    AccountNameEn = "Rent Expense",
                    AccountTypeId = 5,
                    AccountGroupId = 7,
                    LevelType = AccountLevelType.Detail,
                    Nature = AccountNature.Debit,
                    ParentAccountId = 23,
                    IsParent = false,
                    IsActive = true,
                    AllowPosting = true,
                    CurrencyId = 1,
                    OpeningBalance = 0,
                    CreatedDate = DateTime.Now,
                },

                new ChartOfAccount
                {
                    AccountId = 26,
                    AccountCode = "5.01.003",
                    AccountNameAr = "مصروفات كهرباء وماء",
                    AccountNameEn = "Utilities Expense",
                    AccountTypeId = 5,
                    AccountGroupId = 7,
                    LevelType = AccountLevelType.Detail,
                    Nature = AccountNature.Debit,
                    ParentAccountId = 23,
                    IsParent = false,
                    IsActive = true,
                    AllowPosting = true,
                    CurrencyId = 1,
                    OpeningBalance = 0,
                    CreatedDate = DateTime.Now,
                },

                new ChartOfAccount
                {
                    AccountId = 27,
                    AccountCode = "5.01.004",
                    AccountNameAr = "مصروفات إدارية",
                    AccountNameEn = "Administrative Expenses",
                    AccountTypeId = 5,
                    AccountGroupId = 7,
                    LevelType = AccountLevelType.Detail,
                    Nature = AccountNature.Debit,
                    ParentAccountId = 23,
                    IsParent = false,
                    IsActive = true,
                    AllowPosting = true,
                    CurrencyId = 1,
                    OpeningBalance = 0,
                    CreatedDate = DateTime.Now,
                }
            };
        }

        private static List<AccountType> GetSampleAccountTypes()
        {
            return new List<AccountType>
            {
                new AccountType { AccountTypeId = 1, TypeNameAr = "أصول", TypeNameEn = "Assets", IsActive = true },
                new AccountType { AccountTypeId = 2, TypeNameAr = "خصوم", TypeNameEn = "Liabilities", IsActive = true },
                new AccountType { AccountTypeId = 3, TypeNameAr = "حقوق الملكية", TypeNameEn = "Equity", IsActive = true },
                new AccountType { AccountTypeId = 4, TypeNameAr = "إيرادات", TypeNameEn = "Revenue", IsActive = true },
                new AccountType { AccountTypeId = 5, TypeNameAr = "مصروفات", TypeNameEn = "Expenses", IsActive = true }
            };
        }

        private static List<AccountGroup> GetSampleAccountGroups()
        {
            return new List<AccountGroup>
            {
                new AccountGroup { AccountGroupId = 1, GroupNameAr = "أصول متداولة", GroupNameEn = "Current Assets", AccountTypeId = 1, IsActive = true },
                new AccountGroup { AccountGroupId = 2, GroupNameAr = "أصول ثابتة", GroupNameEn = "Fixed Assets", AccountTypeId = 1, IsActive = true },
                new AccountGroup { AccountGroupId = 3, GroupNameAr = "خصوم متداولة", GroupNameEn = "Current Liabilities", AccountTypeId = 2, IsActive = true },
                new AccountGroup { AccountGroupId = 4, GroupNameAr = "خصوم طويلة الأجل", GroupNameEn = "Long-term Liabilities", AccountTypeId = 2, IsActive = true },
                new AccountGroup { AccountGroupId = 5, GroupNameAr = "حقوق الملكية", GroupNameEn = "Equity", AccountTypeId = 3, IsActive = true },
                new AccountGroup { AccountGroupId = 6, GroupNameAr = "إيرادات التشغيل", GroupNameEn = "Operating Revenue", AccountTypeId = 4, IsActive = true },
                new AccountGroup { AccountGroupId = 7, GroupNameAr = "مصروفات التشغيل", GroupNameEn = "Operating Expenses", AccountTypeId = 5, IsActive = true }
            };
        }
    }
}
