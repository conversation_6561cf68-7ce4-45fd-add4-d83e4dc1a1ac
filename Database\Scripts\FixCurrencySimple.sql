-- إصلاح بسيط لجدول العملات
-- Simple fix for Currencies table
USE AwqafManagement
GO

-- حذف وإعادة إنشاء جدول العملات
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Currencies')
BEGIN
    PRINT 'حذف جدول العملات القديم...'
    DROP TABLE Currencies
END

PRINT 'إنشاء جدول العملات الجديد...'

CREATE TABLE [dbo].[Currencies](
    [CurrencyId] [int] IDENTITY(1,1) NOT NULL,
    [CurrencyCode] [nvarchar](10) NOT NULL,
    [CurrencyNameAr] [nvarchar](100) NOT NULL,
    [CurrencyNameEn] [nvarchar](100) NULL,
    [Symbol] [nvarchar](10) NOT NULL,
    [ExchangeRate] [decimal](18, 6) NOT NULL DEFAULT(1.000000),
    [DecimalPlaces] [int] NOT NULL DEFAULT(2),
    [IsBaseCurrency] [bit] NOT NULL DEFAULT(0),
    [IsActive] [bit] NOT NULL DEFAULT(1),
    [CreatedDate] [datetime] NOT NULL DEFAULT(GETDATE()),
    [ModifiedDate] [datetime] NULL,
    [CreatedBy] [nvarchar](50) NOT NULL,
    [ModifiedBy] [nvarchar](50) NULL,
    CONSTRAINT [PK_Currencies] PRIMARY KEY CLUSTERED ([CurrencyId] ASC),
    CONSTRAINT [UK_Currencies_CurrencyCode] UNIQUE NONCLUSTERED ([CurrencyCode] ASC)
)

PRINT 'تم إنشاء جدول العملات'

-- إضافة بيانات تجريبية
PRINT 'إضافة بيانات العملات الأساسية...'

INSERT INTO Currencies (CurrencyCode, CurrencyNameAr, CurrencyNameEn, Symbol, ExchangeRate, DecimalPlaces, IsBaseCurrency, IsActive, CreatedBy)
VALUES 
('SAR', N'ريال سعودي', 'Saudi Riyal', N'ر.س', 1.000000, 2, 1, 1, '1'),
('USD', N'دولار أمريكي', 'US Dollar', N'$', 3.750000, 2, 0, 1, '1'),
('EUR', N'يورو', 'Euro', N'€', 4.100000, 2, 0, 1, '1'),
('GBP', N'جنيه إسترليني', 'British Pound', N'£', 4.650000, 2, 0, 1, '1'),
('AED', N'درهم إماراتي', 'UAE Dirham', N'د.إ', 1.020000, 2, 0, 1, '1'),
('KWD', N'دينار كويتي', 'Kuwaiti Dinar', N'د.ك', 12.250000, 3, 0, 1, '1'),
('QAR', N'ريال قطري', 'Qatari Riyal', N'ر.ق', 1.030000, 2, 0, 1, '1'),
('BHD', N'دينار بحريني', 'Bahraini Dinar', N'د.ب', 9.950000, 3, 0, 1, '1'),
('OMR', N'ريال عماني', 'Omani Rial', N'ر.ع', 9.750000, 3, 0, 1, '1'),
('JOD', N'دينار أردني', 'Jordanian Dinar', N'د.أ', 5.290000, 3, 0, 1, '1')

PRINT 'تم إضافة بيانات العملات الأساسية'
PRINT 'تم الانتهاء من إصلاح جدول العملات!'
GO
