using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Security.Cryptography;
using System.Text;
using Awqaf_Managment.Models.Security;

namespace Awqaf_Managment.DataAccess.Security
{
    /// <summary>
    /// طبقة الوصول للبيانات الخاصة بالمستخدمين
    /// </summary>
    public class UserDataAccess
    {
        /// <summary>
        /// تسجيل دخول المستخدم
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <param name="password">كلمة المرور</param>
        /// <returns>بيانات المستخدم في حالة النجاح، null في حالة الفشل</returns>
        public static User AuthenticateUser(string username, string password)
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();

                    // استعلام مباشر للتحقق من المستخدم وكلمة المرور
                    string query = @"
                        SELECT UserId, Username, FullName, Email, IsActive, IsLocked
                        FROM Users
                        WHERE Username = @Username AND PasswordHash = @Password AND IsActive = 1 AND IsLocked = 0";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@Username", username);
                        command.Parameters.AddWithValue("@Password", password);

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                // تحديث تاريخ آخر دخول
                                var user = new User
                                {
                                    UserId = Convert.ToInt32(reader["UserId"]),
                                    Username = reader["Username"].ToString(),
                                    FullName = reader["FullName"].ToString(),
                                    Email = reader["Email"].ToString(),
                                    LastLoginDate = DateTime.Now
                                };

                                // تحديث تاريخ آخر دخول في قاعدة البيانات
                                reader.Close();
                                using (var updateCmd = new SqlCommand("UPDATE Users SET LastLoginDate = GETDATE() WHERE UserId = @UserId", connection))
                                {
                                    updateCmd.Parameters.AddWithValue("@UserId", user.UserId);
                                    updateCmd.ExecuteNonQuery();
                                }

                                return user;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تسجيل الدخول: {ex.Message}");
            }

            return null;
        }

        /// <summary>
        /// الحصول على صلاحيات المستخدم
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>قائمة الصلاحيات</returns>
        public static List<Permission> GetUserPermissions(int userId)
        {
            var permissions = new List<Permission>();

            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand("sp_GetUserPermissions", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@UserId", userId);

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                permissions.Add(new Permission
                                {
                                    PermissionName = reader["PermissionName"].ToString(),
                                    PermissionNameAr = reader["PermissionNameAr"].ToString(),
                                    ModuleName = reader["ModuleName"].ToString(),
                                    ModuleNameAr = reader["ModuleNameAr"].ToString(),
                                    CanView = Convert.ToBoolean(reader["CanView"]),
                                    CanAdd = Convert.ToBoolean(reader["CanAdd"]),
                                    CanEdit = Convert.ToBoolean(reader["CanEdit"]),
                                    CanDelete = Convert.ToBoolean(reader["CanDelete"]),
                                    CanPrint = Convert.ToBoolean(reader["CanPrint"]),
                                    CanExport = Convert.ToBoolean(reader["CanExport"])
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على الصلاحيات: {ex.Message}");
            }

            return permissions;
        }

        /// <summary>
        /// الحصول على جميع المستخدمين
        /// </summary>
        /// <returns>قائمة المستخدمين</returns>
        public static List<User> GetAllUsers()
        {
            var users = new List<User>();

            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT u.UserId, u.Username, u.FullName, u.Email, u.Phone,
                               u.IsActive, u.IsLocked, u.FailedLoginAttempts,
                               u.LastLoginDate, u.CreatedDate
                        FROM Users u
                        ORDER BY u.FullName";

                    using (var command = new SqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                users.Add(new User
                                {
                                    UserId = Convert.ToInt32(reader["UserId"]),
                                    Username = reader["Username"].ToString(),
                                    FullName = reader["FullName"].ToString(),
                                    Email = reader["Email"].ToString(),
                                    Phone = reader["Phone"].ToString(),
                                    IsActive = Convert.ToBoolean(reader["IsActive"]),
                                    IsLocked = Convert.ToBoolean(reader["IsLocked"]),
                                    FailedLoginAttempts = Convert.ToInt32(reader["FailedLoginAttempts"]),
                                    LastLoginDate = reader["LastLoginDate"] as DateTime?,
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"])
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على المستخدمين: {ex.Message}");
            }

            // تحميل أدوار كل مستخدم
            foreach (var user in users)
            {
                user.Roles = GetUserRolesList(user.UserId);
            }

            return users;
        }

        /// <summary>
        /// الحصول على أدوار مستخدم معين كقائمة Role
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>قائمة الأدوار</returns>
        public static List<Role> GetUserRolesList(int userId)
        {
            var roles = new List<Role>();

            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT r.RoleId, r.RoleName, r.RoleNameAr, r.Description, r.IsActive, r.CreatedDate
                        FROM Roles r
                        INNER JOIN UserRoles ur ON r.RoleId = ur.RoleId
                        WHERE ur.UserId = @UserId AND ur.IsActive = 1 AND r.IsActive = 1
                        ORDER BY r.RoleNameAr";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@UserId", userId);

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                roles.Add(new Role
                                {
                                    RoleId = Convert.ToInt32(reader["RoleId"]),
                                    RoleName = reader["RoleName"].ToString(),
                                    RoleNameAr = reader["RoleNameAr"].ToString(),
                                    Description = reader["Description"].ToString(),
                                    IsActive = Convert.ToBoolean(reader["IsActive"]),
                                    CreatedDate = Convert.ToDateTime(reader["CreatedDate"])
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على أدوار المستخدم: {ex.Message}");
            }

            return roles;
        }

        /// <summary>
        /// إضافة مستخدم جديد
        /// </summary>
        /// <param name="user">بيانات المستخدم</param>
        /// <param name="password">كلمة المرور</param>
        /// <param name="createdBy">معرف المستخدم المنشئ</param>
        /// <returns>معرف المستخدم الجديد</returns>
        public static int AddUser(User user, string password, int createdBy)
        {
            try
            {
                // إنشاء Salt وتشفير كلمة المرور
                string salt = Guid.NewGuid().ToString();
                string passwordHash = HashPassword(password, salt);

                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    string query = @"
                        INSERT INTO Users (Username, PasswordHash, Salt, FullName, Email, Phone, 
                                         IsActive, CreatedBy, CreatedDate)
                        VALUES (@Username, @PasswordHash, @Salt, @FullName, @Email, @Phone, 
                                @IsActive, @CreatedBy, @CreatedDate);
                        SELECT SCOPE_IDENTITY();";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@Username", user.Username);
                        command.Parameters.AddWithValue("@PasswordHash", passwordHash);
                        command.Parameters.AddWithValue("@Salt", salt);
                        command.Parameters.AddWithValue("@FullName", user.FullName);
                        command.Parameters.AddWithValue("@Email", user.Email ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@Phone", user.Phone ?? (object)DBNull.Value);
                        command.Parameters.AddWithValue("@IsActive", user.IsActive);
                        command.Parameters.AddWithValue("@CreatedBy", createdBy);
                        command.Parameters.AddWithValue("@CreatedDate", DateTime.Now);

                        return Convert.ToInt32(command.ExecuteScalar());
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إضافة المستخدم: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على Salt للمستخدم
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <returns>Salt أو null إذا لم يوجد المستخدم</returns>
        private static string GetUserSalt(string username)
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand("SELECT Salt FROM Users WHERE Username = @Username", connection))
                    {
                        command.Parameters.AddWithValue("@Username", username);
                        var result = command.ExecuteScalar();
                        return result?.ToString();
                    }
                }
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// تشفير كلمة المرور
        /// </summary>
        /// <param name="password">كلمة المرور</param>
        /// <param name="salt">المفتاح</param>
        /// <returns>كلمة المرور المشفرة</returns>
        private static string HashPassword(string password, string salt)
        {
            using (var sha256 = SHA256.Create())
            {
                byte[] hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password + salt));
                return Convert.ToBase64String(hashedBytes);
            }
        }

        /// <summary>
        /// التحقق من وجود اسم المستخدم
        /// </summary>
        /// <param name="username">اسم المستخدم</param>
        /// <param name="excludeUserId">معرف المستخدم المستثنى من البحث</param>
        /// <returns>true إذا كان موجود، false إذا لم يكن موجود</returns>
        public static bool IsUsernameExists(string username, int? excludeUserId = null)
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    string query = "SELECT COUNT(*) FROM Users WHERE Username = @Username";
                    
                    if (excludeUserId.HasValue)
                    {
                        query += " AND UserId != @ExcludeUserId";
                    }

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@Username", username);
                        if (excludeUserId.HasValue)
                        {
                            command.Parameters.AddWithValue("@ExcludeUserId", excludeUserId.Value);
                        }

                        return Convert.ToInt32(command.ExecuteScalar()) > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في التحقق من اسم المستخدم: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث كلمة مرور المستخدم
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="newPassword">كلمة المرور الجديدة</param>
        public static void UpdateUserPassword(int userId, string newPassword)
        {
            try
            {
                // إنشاء Salt جديد وتشفير كلمة المرور
                string salt = Guid.NewGuid().ToString();
                string passwordHash = HashPassword(newPassword, salt);

                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    string query = "UPDATE Users SET PasswordHash = @PasswordHash, Salt = @Salt WHERE UserId = @UserId";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@PasswordHash", passwordHash);
                        command.Parameters.AddWithValue("@Salt", salt);
                        command.Parameters.AddWithValue("@UserId", userId);

                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث كلمة المرور: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على أدوار المستخدم
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>قائمة أدوار المستخدم</returns>
        public static List<UserRole> GetUserRoles(int userId)
        {
            var userRoles = new List<UserRole>();

            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    string query = @"
                        SELECT ur.UserRoleId, ur.UserId, ur.RoleId, ur.IsActive, ur.AssignedDate,
                               r.RoleName, r.RoleNameAr, r.Description
                        FROM UserRoles ur
                        INNER JOIN Roles r ON ur.RoleId = r.RoleId
                        WHERE ur.UserId = @UserId AND ur.IsActive = 1";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@UserId", userId);

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                userRoles.Add(new UserRole
                                {
                                    UserRoleId = Convert.ToInt32(reader["UserRoleId"]),
                                    UserId = Convert.ToInt32(reader["UserId"]),
                                    RoleId = Convert.ToInt32(reader["RoleId"]),
                                    IsActive = Convert.ToBoolean(reader["IsActive"]),
                                    AssignedDate = Convert.ToDateTime(reader["AssignedDate"])
                                });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في جلب أدوار المستخدم: {ex.Message}");
            }

            return userRoles;
        }

        /// <summary>
        /// إنشاء مستخدم جديد
        /// </summary>
        /// <param name="user">بيانات المستخدم</param>
        /// <param name="password">كلمة المرور</param>
        /// <returns>معرف المستخدم الجديد</returns>
        public static int CreateUser(User user, string password)
        {
            try
            {
                // إنشاء Salt وتشفير كلمة المرور
                string salt = Guid.NewGuid().ToString();
                string passwordHash = HashPassword(password, salt);

                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    string query = @"
                        INSERT INTO Users (Username, FullName, Email, PasswordHash, Salt, IsActive, IsLocked, CreatedDate)
                        VALUES (@Username, @FullName, @Email, @PasswordHash, @Salt, @IsActive, 0, GETDATE());
                        SELECT SCOPE_IDENTITY();";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@Username", user.Username);
                        command.Parameters.AddWithValue("@FullName", user.FullName);
                        command.Parameters.AddWithValue("@Email", user.Email);
                        command.Parameters.AddWithValue("@PasswordHash", passwordHash);
                        command.Parameters.AddWithValue("@Salt", salt);
                        command.Parameters.AddWithValue("@IsActive", user.IsActive);

                        return Convert.ToInt32(command.ExecuteScalar());
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء المستخدم: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث بيانات المستخدم
        /// </summary>
        /// <param name="user">بيانات المستخدم</param>
        public static void UpdateUser(User user)
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    string query = @"
                        UPDATE Users
                        SET FullName = @FullName,
                            Email = @Email,
                            IsActive = @IsActive
                        WHERE UserId = @UserId";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@UserId", user.UserId);
                        command.Parameters.AddWithValue("@FullName", user.FullName);
                        command.Parameters.AddWithValue("@Email", user.Email);
                        command.Parameters.AddWithValue("@IsActive", user.IsActive);

                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث المستخدم: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث أدوار المستخدم
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="roleIds">قائمة معرفات الأدوار الجديدة</param>
        public static void UpdateUserRoles(int userId, List<int> roleIds)
        {
            try
            {
                using (var connection = DatabaseConnection.CreateConnection())
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // حذف جميع الأدوار الحالية للمستخدم
                            string deleteQuery = "DELETE FROM UserRoles WHERE UserId = @UserId";
                            using (var deleteCommand = new SqlCommand(deleteQuery, connection, transaction))
                            {
                                deleteCommand.Parameters.AddWithValue("@UserId", userId);
                                deleteCommand.ExecuteNonQuery();
                            }

                            // إضافة الأدوار الجديدة
                            foreach (int roleId in roleIds)
                            {
                                string insertQuery = @"
                                    INSERT INTO UserRoles (UserId, RoleId, IsActive, AssignedDate)
                                    VALUES (@UserId, @RoleId, 1, GETDATE())";

                                using (var insertCommand = new SqlCommand(insertQuery, connection, transaction))
                                {
                                    insertCommand.Parameters.AddWithValue("@UserId", userId);
                                    insertCommand.Parameters.AddWithValue("@RoleId", roleId);
                                    insertCommand.ExecuteNonQuery();
                                }
                            }

                            transaction.Commit();
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث أدوار المستخدم: {ex.Message}");
            }
        }
    }
}
