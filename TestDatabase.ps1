# Test Database Connection and Data
try {
    Write-Host "Testing database connection..." -ForegroundColor Green
    
    # Test connection
    $result = sqlcmd -S NAJEEB -d AwqafManagement -Q "SELECT COUNT(*) as RecordCount FROM ChartOfAccounts" -h -1
    Write-Host "Current record count: $result" -ForegroundColor Yellow
    
    # Run the Arabic data script
    Write-Host "Running Arabic data script..." -ForegroundColor Green
    sqlcmd -S NAJEEB -d AwqafManagement -i "Database\Scripts\CorrectArabicData.sql"
    
    # Test again
    $result2 = sqlcmd -S NAJEEB -d AwqafManagement -Q "SELECT COUNT(*) as RecordCount FROM ChartOfAccounts" -h -1
    Write-Host "New record count: $result2" -ForegroundColor Yellow
    
    # Show sample data
    Write-Host "Sample Arabic data:" -ForegroundColor Green
    sqlcmd -S NAJEEB -d AwqafManagement -Q "SELECT TOP 5 AccountCode, AccountNameAr FROM ChartOfAccounts ORDER BY AccountCode" -h -1
    
    Write-Host "Database test completed successfully!" -ForegroundColor Green
}
catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}
