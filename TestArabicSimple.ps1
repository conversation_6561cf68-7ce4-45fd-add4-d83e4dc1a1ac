# اختبار بسيط للنص العربي
# Simple Arabic Text Test

Write-Host "=== اختبار النص العربي ===" -ForegroundColor Green

try {
    # الاتصال بقاعدة البيانات
    $connectionString = "Server=NAJEEB;Database=AwqafManagement;Integrated Security=true;TrustServerCertificate=true;"
    $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    $connection.Open()
    
    Write-Host "✓ تم الاتصال بقاعدة البيانات بنجاح" -ForegroundColor Green
    
    # استعلام بسيط
    $query = "SELECT TOP 5 AccountCode, AccountNameAr FROM ChartOfAccounts WHERE IsActive = 1 ORDER BY AccountCode"
    $command = New-Object System.Data.SqlClient.SqlCommand($query, $connection)
    $reader = $command.ExecuteReader()
    
    Write-Host "`n--- عينة من الحسابات ---" -ForegroundColor Yellow
    $count = 0
    while ($reader.Read() -and $count -lt 5) {
        $code = $reader["AccountCode"]
        $nameAr = $reader["AccountNameAr"]
        Write-Host "$code : $nameAr"
        $count++
    }
    
    $reader.Close()
    $connection.Close()
    
    Write-Host "`n✓ انتهى الاختبار بنجاح" -ForegroundColor Green
}
catch {
    Write-Host "❌ خطأ: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nاضغط أي مفتاح للمتابعة..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
