using System;
using System.Collections.Generic;
using System.Linq;
using Awqaf_Managment.Models.Accounting;
using DataAccess.Accounting;

namespace Services.Accounting
{
    public static class JournalEntryService
    {
        #region Get Methods

        /// <summary>
        /// الحصول على جميع القيود اليومية
        /// Get all journal entries
        /// </summary>
        /// <returns>قائمة القيود اليومية</returns>
        public static List<JournalEntry> GetAllJournalEntries()
        {
            try
            {
                return JournalEntryDataAccess.GetAllJournalEntries();
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على القيود اليومية: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على قيد يومي بالمعرف
        /// Get journal entry by ID
        /// </summary>
        /// <param name="journalEntryId">معرف القيد</param>
        /// <returns>القيد اليومي</returns>
        public static JournalEntry GetJournalEntryById(int journalEntryId)
        {
            try
            {
                if (journalEntryId <= 0)
                    throw new ArgumentException("معرف القيد غير صحيح");

                return JournalEntryDataAccess.GetJournalEntryById(journalEntryId);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على القيد اليومي: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على تفاصيل القيد اليومي
        /// Get journal entry details
        /// </summary>
        /// <param name="journalEntryId">معرف القيد</param>
        /// <returns>قائمة تفاصيل القيد</returns>
        public static List<JournalEntryDetail> GetJournalEntryDetails(int journalEntryId)
        {
            try
            {
                if (journalEntryId <= 0)
                    throw new ArgumentException("معرف القيد غير صحيح");

                return JournalEntryDataAccess.GetJournalEntryDetails(journalEntryId);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على تفاصيل القيد: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على رقم القيد التالي
        /// Get next journal entry number
        /// </summary>
        /// <returns>رقم القيد التالي</returns>
        public static string GetNextJournalEntryNumber()
        {
            try
            {
                return JournalEntryDataAccess.GetNextJournalEntryNumber();
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على رقم القيد التالي: {ex.Message}", ex);
            }
        }

        #endregion

        #region Save Methods

        /// <summary>
        /// حفظ قيد يومي جديد
        /// Save new journal entry
        /// </summary>
        /// <param name="journalEntry">القيد اليومي</param>
        /// <param name="details">تفاصيل القيد</param>
        /// <returns>معرف القيد المحفوظ</returns>
        public static int SaveJournalEntry(JournalEntry journalEntry, List<JournalEntryDetail> details)
        {
            try
            {
                // التحقق من صحة البيانات
                ValidateJournalEntry(journalEntry, details);

                // حفظ القيد
                if (journalEntry.JournalEntryId == 0)
                {
                    return JournalEntryDataAccess.InsertJournalEntry(journalEntry, details);
                }
                else
                {
                    JournalEntryDataAccess.UpdateJournalEntry(journalEntry, details);
                    return journalEntry.JournalEntryId;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حفظ القيد اليومي: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تحديث قيد يومي
        /// Update journal entry
        /// </summary>
        /// <param name="journalEntry">القيد اليومي</param>
        /// <param name="details">تفاصيل القيد</param>
        public static void UpdateJournalEntry(JournalEntry journalEntry, List<JournalEntryDetail> details)
        {
            try
            {
                // التحقق من صحة البيانات
                ValidateJournalEntry(journalEntry, details);

                // التحقق من حالة القيد
                if (journalEntry.IsPosted)
                    throw new InvalidOperationException("لا يمكن تعديل قيد مرحل");

                JournalEntryDataAccess.UpdateJournalEntry(journalEntry, details);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث القيد اليومي: {ex.Message}", ex);
            }
        }

        #endregion

        #region Delete Methods

        /// <summary>
        /// حذف قيد يومي
        /// Delete journal entry
        /// </summary>
        /// <param name="journalEntryId">معرف القيد</param>
        public static void DeleteJournalEntry(int journalEntryId)
        {
            try
            {
                if (journalEntryId <= 0)
                    throw new ArgumentException("معرف القيد غير صحيح");

                // التحقق من حالة القيد
                var journalEntry = GetJournalEntryById(journalEntryId);
                if (journalEntry == null)
                    throw new InvalidOperationException("القيد غير موجود");

                if (journalEntry.IsPosted)
                    throw new InvalidOperationException("لا يمكن حذف قيد مرحل");

                JournalEntryDataAccess.DeleteJournalEntry(journalEntryId);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حذف القيد اليومي: {ex.Message}", ex);
            }
        }

        #endregion

        #region Posting Methods

        /// <summary>
        /// ترحيل قيد يومي
        /// Post journal entry
        /// </summary>
        /// <param name="journalEntryId">معرف القيد</param>
        /// <param name="postedBy">المستخدم المرحل</param>
        public static void PostJournalEntry(int journalEntryId, string postedBy)
        {
            try
            {
                if (journalEntryId <= 0)
                    throw new ArgumentException("معرف القيد غير صحيح");

                if (string.IsNullOrWhiteSpace(postedBy))
                    throw new ArgumentException("يجب تحديد المستخدم المرحل");

                // التحقق من حالة القيد
                var journalEntry = GetJournalEntryById(journalEntryId);
                if (journalEntry == null)
                    throw new InvalidOperationException("القيد غير موجود");

                if (journalEntry.IsPosted)
                    throw new InvalidOperationException("القيد مرحل مسبقاً");

                // التحقق من توازن القيد
                var details = GetJournalEntryDetails(journalEntryId);
                ValidateJournalEntryBalance(details);

                JournalEntryDataAccess.PostJournalEntry(journalEntryId, postedBy);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في ترحيل القيد اليومي: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// إلغاء ترحيل قيد يومي
        /// Unpost journal entry
        /// </summary>
        /// <param name="journalEntryId">معرف القيد</param>
        /// <param name="unpostedBy">المستخدم الملغي للترحيل</param>
        public static void UnpostJournalEntry(int journalEntryId, string unpostedBy)
        {
            try
            {
                if (journalEntryId <= 0)
                    throw new ArgumentException("معرف القيد غير صحيح");

                if (string.IsNullOrWhiteSpace(unpostedBy))
                    throw new ArgumentException("يجب تحديد المستخدم الملغي للترحيل");

                // التحقق من حالة القيد
                var journalEntry = GetJournalEntryById(journalEntryId);
                if (journalEntry == null)
                    throw new InvalidOperationException("القيد غير موجود");

                if (!journalEntry.IsPosted)
                    throw new InvalidOperationException("القيد غير مرحل");

                JournalEntryDataAccess.UnpostJournalEntry(journalEntryId, unpostedBy);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إلغاء ترحيل القيد اليومي: {ex.Message}", ex);
            }
        }

        #endregion

        #region Validation Methods

        /// <summary>
        /// التحقق من صحة القيد اليومي
        /// Validate journal entry
        /// </summary>
        /// <param name="journalEntry">القيد اليومي</param>
        /// <param name="details">تفاصيل القيد</param>
        private static void ValidateJournalEntry(JournalEntry journalEntry, List<JournalEntryDetail> details)
        {
            if (journalEntry == null)
                throw new ArgumentNullException(nameof(journalEntry), "القيد اليومي مطلوب");

            if (details == null || !details.Any())
                throw new ArgumentException("تفاصيل القيد مطلوبة");

            if (details.Count < 2)
                throw new ArgumentException("يجب أن يحتوي القيد على سطرين على الأقل");

            if (string.IsNullOrWhiteSpace(journalEntry.Description))
                throw new ArgumentException("بيان القيد مطلوب");

            // التحقق من صحة التفاصيل
            foreach (var detail in details)
            {
                var validationMessage = detail.Validate();
                if (!string.IsNullOrEmpty(validationMessage))
                    throw new ArgumentException($"خطأ في السطر {detail.LineNumber}: {validationMessage}");
            }

            // التحقق من توازن القيد
            ValidateJournalEntryBalance(details);
        }

        /// <summary>
        /// التحقق من توازن القيد
        /// Validate journal entry balance
        /// </summary>
        /// <param name="details">تفاصيل القيد</param>
        private static void ValidateJournalEntryBalance(List<JournalEntryDetail> details)
        {
            var totalDebit = details.Sum(d => d.DebitAmount);
            var totalCredit = details.Sum(d => d.CreditAmount);

            if (totalDebit != totalCredit)
                throw new ArgumentException($"القيد غير متوازن - إجمالي المدين: {totalDebit:N2}, إجمالي الدائن: {totalCredit:N2}");

            if (totalDebit == 0)
                throw new ArgumentException("يجب أن يحتوي القيد على مبالغ");
        }

        #endregion

        #region Search Methods

        /// <summary>
        /// البحث في القيود اليومية
        /// Search journal entries
        /// </summary>
        /// <param name="searchCriteria">معايير البحث</param>
        /// <returns>قائمة القيود المطابقة</returns>
        public static List<JournalEntry> SearchJournalEntries(JournalEntrySearchCriteria searchCriteria)
        {
            try
            {
                return JournalEntryDataAccess.SearchJournalEntries(searchCriteria);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في البحث في القيود اليومية: {ex.Message}", ex);
            }
        }

        #endregion
    }

    /// <summary>
    /// معايير البحث في القيود اليومية
    /// Journal Entry Search Criteria
    /// </summary>
    public class JournalEntrySearchCriteria
    {
        public string EntryNumber { get; set; } = "";
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public string Description { get; set; } = "";
        public int? AccountId { get; set; }
        public bool? IsPosted { get; set; }
        public string CreatedBy { get; set; } = "";
    }
}
