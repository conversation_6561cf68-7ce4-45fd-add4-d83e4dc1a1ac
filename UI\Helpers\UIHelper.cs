using System;
using System.Drawing;
using System.Windows.Forms;

namespace Awqaf_Managment.UI.Helpers
{
    /// <summary>
    /// مساعد واجهة المستخدم
    /// UI Helper Class
    /// </summary>
    public static class UIHelper
    {
        /// <summary>
        /// تطبيق التصميم العصري على النموذج
        /// Apply modern design to form
        /// </summary>
        /// <param name="form">النموذج</param>
        public static void ApplyModernDesign(Form form)
        {
            if (form == null) return;

            form.BackColor = Color.FromArgb(248, 249, 250);
            form.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
        }

        /// <summary>
        /// تطبيق الخط العربي على التحكم
        /// Apply Arabic font to control
        /// </summary>
        /// <param name="control">التحكم</param>
        public static void ApplyArabicFont(Control control)
        {
            if (control == null) return;

            try
            {
                // استخدام خط عربي مناسب
                control.Font = new Font("Tahoma", 9F, FontStyle.Regular);
                control.RightToLeft = RightToLeft.Yes;

                // تطبيق الخط على جميع التحكمات الفرعية
                foreach (Control childControl in control.Controls)
                {
                    ApplyArabicFont(childControl);
                }
            }
            catch (Exception)
            {
                // في حالة فشل تطبيق الخط، استخدم الخط الافتراضي
                control.Font = new Font("Microsoft Sans Serif", 9F, FontStyle.Regular);
                control.RightToLeft = RightToLeft.Yes;
            }
        }

        /// <summary>
        /// تطبيق تصميم الأزرار العصري
        /// Apply modern button design
        /// </summary>
        /// <param name="button">الزر</param>
        /// <param name="backColor">لون الخلفية</param>
        /// <param name="foreColor">لون النص</param>
        public static void ApplyButtonStyle(Button button, Color backColor, Color foreColor)
        {
            if (button == null) return;

            button.BackColor = backColor;
            button.ForeColor = foreColor;
            button.FlatStyle = FlatStyle.Flat;
            button.FlatAppearance.BorderSize = 0;
            button.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
        }

        /// <summary>
        /// تطبيق تصميم GroupBox العصري
        /// Apply modern GroupBox design
        /// </summary>
        /// <param name="groupBox">مجموعة التحكم</param>
        public static void ApplyGroupBoxStyle(GroupBox groupBox)
        {
            if (groupBox == null) return;

            groupBox.BackColor = Color.White;
            groupBox.ForeColor = Color.FromArgb(52, 58, 64);
            groupBox.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
        }

        /// <summary>
        /// تطبيق تصميم TextBox العصري
        /// Apply modern TextBox design
        /// </summary>
        /// <param name="textBox">مربع النص</param>
        public static void ApplyTextBoxStyle(TextBox textBox)
        {
            if (textBox == null) return;

            textBox.BackColor = Color.White;
            textBox.ForeColor = Color.FromArgb(52, 58, 64);
            textBox.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            textBox.BorderStyle = BorderStyle.FixedSingle;
        }

        /// <summary>
        /// تطبيق تصميم ComboBox العصري
        /// Apply modern ComboBox design
        /// </summary>
        /// <param name="comboBox">قائمة منسدلة</param>
        public static void ApplyComboBoxStyle(ComboBox comboBox)
        {
            if (comboBox == null) return;

            comboBox.BackColor = Color.White;
            comboBox.ForeColor = Color.FromArgb(52, 58, 64);
            comboBox.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            comboBox.FlatStyle = FlatStyle.Flat;
        }

        /// <summary>
        /// تطبيق تصميم TreeView العصري
        /// Apply modern TreeView design
        /// </summary>
        /// <param name="treeView">عرض الشجرة</param>
        public static void ApplyTreeViewStyle(TreeView treeView)
        {
            if (treeView == null) return;

            treeView.BackColor = Color.White;
            treeView.ForeColor = Color.FromArgb(52, 58, 64);
            treeView.Font = new Font("Segoe UI", 9F, FontStyle.Regular);
            treeView.BorderStyle = BorderStyle.FixedSingle;
            treeView.ShowLines = true;
            treeView.ShowPlusMinus = true;
            treeView.ShowRootLines = true;
        }

        /// <summary>
        /// إظهار رسالة خطأ بالعربية
        /// Show Arabic error message
        /// </summary>
        /// <param name="message">الرسالة</param>
        /// <param name="title">العنوان</param>
        /// <param name="useModernNotification">استخدام الإشعار العصري</param>
        public static void ShowErrorMessage(string message, string title = "خطأ", bool useModernNotification = true)
        {
            try
            {
                if (useModernNotification)
                {
                    NotificationHelper.ShowError(message, title);
                }
                else
                {
                    MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Error,
                        MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                }
            }
            catch
            {
                MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
        }

        /// <summary>
        /// إظهار رسالة نجاح بالعربية
        /// Show Arabic success message
        /// </summary>
        /// <param name="message">الرسالة</param>
        /// <param name="title">العنوان</param>
        /// <param name="useModernNotification">استخدام الإشعار العصري</param>
        public static void ShowSuccessMessage(string message, string title = "نجح", bool useModernNotification = true)
        {
            try
            {
                if (useModernNotification)
                {
                    NotificationHelper.ShowSuccess(message, title);
                }
                else
                {
                    MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Information,
                        MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                }
            }
            catch
            {
                MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Information,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
        }

        /// <summary>
        /// إظهار رسالة تحذير بالعربية
        /// Show Arabic warning message
        /// </summary>
        /// <param name="message">الرسالة</param>
        /// <param name="title">العنوان</param>
        /// <param name="useModernNotification">استخدام الإشعار العصري</param>
        public static void ShowWarningMessage(string message, string title = "تحذير", bool useModernNotification = true)
        {
            try
            {
                if (useModernNotification)
                {
                    NotificationHelper.ShowWarning(message, title);
                }
                else
                {
                    MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Warning,
                        MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                }
            }
            catch
            {
                MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Warning,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
        }

        /// <summary>
        /// إظهار رسالة معلومات بالعربية
        /// Show Arabic information message
        /// </summary>
        /// <param name="message">الرسالة</param>
        /// <param name="title">العنوان</param>
        /// <param name="useModernNotification">استخدام الإشعار العصري</param>
        public static void ShowInfoMessage(string message, string title = "معلومات", bool useModernNotification = true)
        {
            try
            {
                if (useModernNotification)
                {
                    NotificationHelper.ShowInfo(message, title);
                }
                else
                {
                    MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Information,
                        MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                }
            }
            catch
            {
                MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Information,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
        }

        /// <summary>
        /// إظهار رسالة تأكيد بالعربية
        /// Show Arabic confirmation message
        /// </summary>
        /// <param name="message">الرسالة</param>
        /// <param name="title">العنوان</param>
        /// <returns>نتيجة الحوار</returns>
        public static DialogResult ShowConfirmMessage(string message, string title = "تأكيد")
        {
            return MessageBox.Show(message, title, MessageBoxButtons.YesNo, MessageBoxIcon.Question,
                MessageBoxDefaultButton.Button2, MessageBoxOptions.RtlReading);
        }

        /// <summary>
        /// إظهار رسالة تأكيد بالعربية
        /// Show Arabic confirmation message
        /// </summary>
        /// <param name="message">الرسالة</param>
        /// <param name="title">العنوان</param>
        /// <returns>نتيجة الحوار</returns>
        public static bool ShowConfirmationMessage(string message, string title = "تأكيد")
        {
            var result = MessageBox.Show(message, title, MessageBoxButtons.YesNo, MessageBoxIcon.Question,
                MessageBoxDefaultButton.Button2, MessageBoxOptions.RtlReading);
            return result == DialogResult.Yes;
        }

        /// <summary>
        /// إظهار رسالة نجاح بالعربية
        /// Show Arabic success message
        /// </summary>
        /// <param name="message">الرسالة</param>
        /// <param name="title">العنوان</param>
        public static void ShowSuccessMessage(string message, string title = "نجح")
        {
            MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Information,
                MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
        }

        /// <summary>
        /// تطبيق التصميم العصري على الزر
        /// Apply modern style to button
        /// </summary>
        /// <param name="button">الزر</param>
        public static void ApplyModernButtonStyle(Button button)
        {
            if (button == null) return;

            button.FlatStyle = FlatStyle.Flat;
            button.FlatAppearance.BorderSize = 0;
            button.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            button.Cursor = Cursors.Hand;

            // تطبيق لون افتراضي حسب نوع الزر
            if (button.Name.Contains("Save") || button.Name.Contains("Add") || button.Name.Contains("حفظ") || button.Name.Contains("إضافة"))
            {
                button.BackColor = Color.FromArgb(46, 204, 113);
                button.ForeColor = Color.White;
            }
            else if (button.Name.Contains("Delete") || button.Name.Contains("Remove") || button.Name.Contains("حذف"))
            {
                button.BackColor = Color.FromArgb(231, 76, 60);
                button.ForeColor = Color.White;
            }
            else if (button.Name.Contains("Edit") || button.Name.Contains("Update") || button.Name.Contains("تعديل"))
            {
                button.BackColor = Color.FromArgb(52, 152, 219);
                button.ForeColor = Color.White;
            }
            else if (button.Name.Contains("Cancel") || button.Name.Contains("Close") || button.Name.Contains("إلغاء") || button.Name.Contains("إغلاق"))
            {
                button.BackColor = Color.FromArgb(149, 165, 166);
                button.ForeColor = Color.White;
            }
            else
            {
                button.BackColor = Color.FromArgb(52, 73, 94);
                button.ForeColor = Color.White;
            }
        }
    }
}
