-- سكريبت محسن لإدراج بيانات عربية تجريبية شاملة
-- Enhanced Arabic Sample Data Script

USE AwqafManagement;
GO

-- حذف البيانات الموجودة
DELETE FROM ChartOfAccounts;
DBCC CHECKIDENT ('ChartOfAccounts', RESEED, 0);
GO

-- إدراج الحسابات الرئيسية والفرعية مع ترميز عربي محسن
SET IDENTITY_INSERT ChartOfAccounts ON;

-- 1. الأصول (Assets) - 1.00.000
INSERT INTO ChartOfAccounts (AccountId, AccountCode, AccountNameAr, AccountNameEn, AccountTypeId, AccountGroupId, ParentAccountId, LevelType, AccountNature, IsParent, IsActive, AllowPosting, AllowDirectEntry, OpeningBalance, OpeningBalanceType, CurrencyId, Description, CreatedDate, ModifiedDate)
VALUES 
(1, N'1.00.000', N'الأصول', N'Assets', 1, 1, NULL, 0, 0, 1, 1, 0, 0, 0.00, 0, 1, N'مجموعة الأصول الرئيسية', GETDATE(), GETDATE());

-- 1.1 الأصول المتداولة (Current Assets) - 1.01.000
INSERT INTO ChartOfAccounts (AccountId, AccountCode, AccountNameAr, AccountNameEn, AccountTypeId, AccountGroupId, ParentAccountId, LevelType, AccountNature, IsParent, IsActive, AllowPosting, AllowDirectEntry, OpeningBalance, OpeningBalanceType, CurrencyId, Description, CreatedDate, ModifiedDate)
VALUES 
(2, N'1.01.000', N'الأصول المتداولة', N'Current Assets', 1, 1, 1, 1, 0, 1, 1, 0, 0, 0.00, 0, 1, N'الأصول قصيرة الأجل', GETDATE(), GETDATE());

-- 1.1.1 النقدية والبنوك (Cash and Banks)
INSERT INTO ChartOfAccounts (AccountId, AccountCode, AccountNameAr, AccountNameEn, AccountTypeId, AccountGroupId, ParentAccountId, LevelType, AccountNature, IsParent, IsActive, AllowPosting, AllowDirectEntry, OpeningBalance, OpeningBalanceType, CurrencyId, Description, CreatedDate, ModifiedDate)
VALUES 
(3, N'1.01.001', N'الصندوق الرئيسي', N'Main Cash', 1, 1, 2, 2, 0, 0, 1, 1, 1, 50000.00, 0, 1, N'صندوق النقدية الرئيسي', GETDATE(), GETDATE()),
(4, N'1.01.002', N'البنك الأهلي التجاري', N'National Commercial Bank', 1, 1, 2, 2, 0, 0, 1, 1, 1, 250000.00, 0, 1, N'حساب البنك الأهلي التجاري', GETDATE(), GETDATE()),
(5, N'1.01.003', N'بنك الراجحي', N'Al Rajhi Bank', 1, 1, 2, 2, 0, 0, 1, 1, 1, 180000.00, 0, 1, N'حساب بنك الراجحي', GETDATE(), GETDATE()),
(6, N'1.01.004', N'بنك الرياض', N'Riyad Bank', 1, 1, 2, 2, 0, 0, 1, 1, 1, 120000.00, 0, 1, N'حساب بنك الرياض', GETDATE(), GETDATE());

-- 1.1.2 العملاء والذمم المدينة (Accounts Receivable)
INSERT INTO ChartOfAccounts (AccountId, AccountCode, AccountNameAr, AccountNameEn, AccountTypeId, AccountGroupId, ParentAccountId, LevelType, AccountNature, IsParent, IsActive, AllowPosting, AllowDirectEntry, OpeningBalance, OpeningBalanceType, CurrencyId, Description, CreatedDate, ModifiedDate)
VALUES 
(7, N'1.01.005', N'العملاء والذمم المدينة', N'Accounts Receivable', 1, 1, 2, 2, 0, 1, 1, 0, 0, 0.00, 0, 1, N'مجموعة العملاء والذمم المدينة', GETDATE(), GETDATE()),
(8, N'1.01.006', N'عميل - شركة الأوقاف العقارية', N'Client - Real Estate Endowments Co.', 1, 1, 7, 2, 0, 0, 1, 1, 1, 75000.00, 0, 1, N'شركة الأوقاف العقارية', GETDATE(), GETDATE()),
(9, N'1.01.007', N'عميل - مؤسسة الخير الإسلامية', N'Client - Islamic Charity Foundation', 1, 1, 7, 2, 0, 0, 1, 1, 1, 45000.00, 0, 1, N'مؤسسة الخير الإسلامية', GETDATE(), GETDATE());

-- 1.2 الأصول الثابتة (Fixed Assets) - 1.02.000
INSERT INTO ChartOfAccounts (AccountId, AccountCode, AccountNameAr, AccountNameEn, AccountTypeId, AccountGroupId, ParentAccountId, LevelType, AccountNature, IsParent, IsActive, AllowPosting, AllowDirectEntry, OpeningBalance, OpeningBalanceType, CurrencyId, Description, CreatedDate, ModifiedDate)
VALUES 
(10, N'1.02.000', N'الأصول الثابتة', N'Fixed Assets', 1, 1, 1, 1, 0, 1, 1, 0, 0, 0.00, 0, 1, N'الأصول طويلة الأجل', GETDATE(), GETDATE()),
(11, N'1.02.001', N'الأراضي والعقارات', N'Land and Real Estate', 1, 1, 10, 2, 0, 0, 1, 1, 1, 2500000.00, 0, 1, N'الأراضي والعقارات الوقفية', GETDATE(), GETDATE()),
(12, N'1.02.002', N'المباني والإنشاءات', N'Buildings and Constructions', 1, 1, 10, 2, 0, 0, 1, 1, 1, 1800000.00, 0, 1, N'المباني والإنشاءات الوقفية', GETDATE(), GETDATE()),
(13, N'1.02.003', N'الأثاث والمعدات', N'Furniture and Equipment', 1, 1, 10, 2, 0, 0, 1, 1, 1, 150000.00, 0, 1, N'الأثاث والمعدات المكتبية', GETDATE(), GETDATE());

-- 2. الخصوم (Liabilities) - 2.00.000
INSERT INTO ChartOfAccounts (AccountId, AccountCode, AccountNameAr, AccountNameEn, AccountTypeId, AccountGroupId, ParentAccountId, LevelType, AccountNature, IsParent, IsActive, AllowPosting, AllowDirectEntry, OpeningBalance, OpeningBalanceType, CurrencyId, Description, CreatedDate, ModifiedDate)
VALUES 
(14, N'2.00.000', N'الخصوم', N'Liabilities', 2, 2, NULL, 0, 1, 1, 1, 0, 0, 0.00, 1, 1, N'مجموعة الخصوم الرئيسية', GETDATE(), GETDATE());

-- 2.1 الخصوم المتداولة (Current Liabilities)
INSERT INTO ChartOfAccounts (AccountId, AccountCode, AccountNameAr, AccountNameEn, AccountTypeId, AccountGroupId, ParentAccountId, LevelType, AccountNature, IsParent, IsActive, AllowPosting, AllowDirectEntry, OpeningBalance, OpeningBalanceType, CurrencyId, Description, CreatedDate, ModifiedDate)
VALUES 
(15, N'2.01.000', N'الخصوم المتداولة', N'Current Liabilities', 2, 2, 14, 1, 1, 1, 1, 0, 0, 0.00, 1, 1, N'الخصوم قصيرة الأجل', GETDATE(), GETDATE()),
(16, N'2.01.001', N'الموردون والذمم الدائنة', N'Accounts Payable', 2, 2, 15, 2, 1, 0, 1, 1, 1, 25000.00, 1, 1, N'الموردون والذمم الدائنة', GETDATE(), GETDATE()),
(17, N'2.01.002', N'المصروفات المستحقة', N'Accrued Expenses', 2, 2, 15, 2, 1, 0, 1, 1, 1, 15000.00, 1, 1, N'المصروفات المستحقة الدفع', GETDATE(), GETDATE());

-- 3. حقوق الملكية (Equity) - 3.00.000
INSERT INTO ChartOfAccounts (AccountId, AccountCode, AccountNameAr, AccountNameEn, AccountTypeId, AccountGroupId, ParentAccountId, LevelType, AccountNature, IsParent, IsActive, AllowPosting, AllowDirectEntry, OpeningBalance, OpeningBalanceType, CurrencyId, Description, CreatedDate, ModifiedDate)
VALUES 
(18, N'3.00.000', N'حقوق الملكية', N'Equity', 3, 3, NULL, 0, 1, 1, 1, 0, 0, 0.00, 1, 1, N'مجموعة حقوق الملكية', GETDATE(), GETDATE()),
(19, N'3.01.001', N'رأس المال الوقفي', N'Endowment Capital', 3, 3, 18, 2, 1, 0, 1, 1, 1, 5000000.00, 1, 1, N'رأس المال الوقفي الأساسي', GETDATE(), GETDATE()),
(20, N'3.01.002', N'الأرباح المحتجزة', N'Retained Earnings', 3, 3, 18, 2, 1, 0, 1, 1, 1, 500000.00, 1, 1, N'الأرباح المحتجزة من السنوات السابقة', GETDATE(), GETDATE());

-- 4. الإيرادات (Revenues) - 4.00.000
INSERT INTO ChartOfAccounts (AccountId, AccountCode, AccountNameAr, AccountNameEn, AccountTypeId, AccountGroupId, ParentAccountId, LevelType, AccountNature, IsParent, IsActive, AllowPosting, AllowDirectEntry, OpeningBalance, OpeningBalanceType, CurrencyId, Description, CreatedDate, ModifiedDate)
VALUES 
(21, N'4.00.000', N'الإيرادات', N'Revenues', 4, 4, NULL, 0, 1, 1, 1, 0, 0, 0.00, 1, 1, N'مجموعة الإيرادات الرئيسية', GETDATE(), GETDATE()),
(22, N'4.01.001', N'إيرادات الإيجارات', N'Rental Income', 4, 4, 21, 2, 1, 0, 1, 1, 1, 0.00, 1, 1, N'إيرادات إيجار العقارات الوقفية', GETDATE(), GETDATE()),
(23, N'4.01.002', N'إيرادات الاستثمارات', N'Investment Income', 4, 4, 21, 2, 1, 0, 1, 1, 1, 0.00, 1, 1, N'إيرادات الاستثمارات المالية', GETDATE(), GETDATE()),
(24, N'4.01.003', N'التبرعات والهبات', N'Donations and Gifts', 4, 4, 21, 2, 1, 0, 1, 1, 1, 0.00, 1, 1, N'التبرعات والهبات الواردة', GETDATE(), GETDATE());

-- 5. المصروفات (Expenses) - 5.00.000
INSERT INTO ChartOfAccounts (AccountId, AccountCode, AccountNameAr, AccountNameEn, AccountTypeId, AccountGroupId, ParentAccountId, LevelType, AccountNature, IsParent, IsActive, AllowPosting, AllowDirectEntry, OpeningBalance, OpeningBalanceType, CurrencyId, Description, CreatedDate, ModifiedDate)
VALUES 
(25, N'5.00.000', N'المصروفات', N'Expenses', 5, 5, NULL, 0, 0, 1, 1, 0, 0, 0.00, 0, 1, N'مجموعة المصروفات الرئيسية', GETDATE(), GETDATE()),
(26, N'5.01.001', N'مصروفات الصيانة', N'Maintenance Expenses', 5, 5, 25, 2, 0, 0, 1, 1, 1, 0.00, 0, 1, N'مصروفات صيانة العقارات', GETDATE(), GETDATE()),
(27, N'5.01.002', N'الرواتب والأجور', N'Salaries and Wages', 5, 5, 25, 2, 0, 0, 1, 1, 1, 0.00, 0, 1, N'رواتب وأجور الموظفين', GETDATE(), GETDATE()),
(28, N'5.01.003', N'المصروفات الإدارية', N'Administrative Expenses', 5, 5, 25, 2, 0, 0, 1, 1, 1, 0.00, 0, 1, N'المصروفات الإدارية العامة', GETDATE(), GETDATE()),
(29, N'5.01.004', N'مصروفات الكهرباء والماء', N'Utilities Expenses', 5, 5, 25, 2, 0, 0, 1, 1, 1, 0.00, 0, 1, N'مصروفات الكهرباء والماء والهاتف', GETDATE(), GETDATE()),
(30, N'5.01.005', N'مصروفات التأمين', N'Insurance Expenses', 5, 5, 25, 2, 0, 0, 1, 1, 1, 0.00, 0, 1, N'مصروفات التأمين على العقارات', GETDATE(), GETDATE());

SET IDENTITY_INSERT ChartOfAccounts OFF;
GO

-- عرض النتائج
SELECT 
    AccountCode as 'رمز الحساب',
    AccountNameAr as 'اسم الحساب بالعربية',
    AccountNameEn as 'اسم الحساب بالإنجليزية',
    CASE LevelType 
        WHEN 0 THEN N'رئيسي'
        WHEN 1 THEN N'فرعي'
        WHEN 2 THEN N'تفصيلي'
    END as 'مستوى الحساب',
    CASE AccountNature 
        WHEN 0 THEN N'مدين'
        WHEN 1 THEN N'دائن'
    END as 'طبيعة الحساب',
    CASE IsParent 
        WHEN 1 THEN N'نعم'
        WHEN 0 THEN N'لا'
    END as 'حساب أب',
    OpeningBalance as 'الرصيد الافتتاحي'
FROM ChartOfAccounts 
ORDER BY AccountCode;

PRINT N'تم إدراج ' + CAST(@@ROWCOUNT AS NVARCHAR(10)) + N' حساب محاسبي بنجاح مع ترميز عربي محسن';
