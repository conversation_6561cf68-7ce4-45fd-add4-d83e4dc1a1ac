-- إنشاء جداول القيود اليومية
-- Create Journal Entry Tables

USE AwqafManagement;
GO

-- إنشاء جدول القيود اليومية الرئيسي
-- Create main Journal Entries table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'JournalEntries')
BEGIN
    CREATE TABLE JournalEntries (
        JournalEntryId INT IDENTITY(1,1) PRIMARY KEY,
        EntryNumber NVARCHAR(20) NOT NULL UNIQUE,
        EntryDate DATE NOT NULL,
        Description NVARCHAR(500) NOT NULL,
        TotalAmount DECIMAL(18,2) NOT NULL DEFAULT 0,
        CurrencyId INT NOT NULL DEFAULT 1,
        IsPosted BIT NOT NULL DEFAULT 0,
        PostedDate DATETIME NULL,
        PostedBy NVARCHAR(50) NULL,
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        CreatedBy NVARCHAR(50) NOT NULL,
        ModifiedDate DATETIME NULL,
        ModifiedBy NVARCHAR(50) NULL,
        
        -- Foreign Key Constraints
        CONSTRAINT FK_JournalEntries_Currency 
            FOREIGN KEY (CurrencyId) REFERENCES Currencies(CurrencyId),
            
        -- Check Constraints
        CONSTRAINT CK_JournalEntries_TotalAmount 
            CHECK (TotalAmount >= 0),
        CONSTRAINT CK_JournalEntries_EntryDate 
            CHECK (EntryDate <= GETDATE()),
        CONSTRAINT CK_JournalEntries_PostedDate 
            CHECK (PostedDate IS NULL OR PostedDate >= EntryDate)
    );
    
    PRINT 'تم إنشاء جدول JournalEntries بنجاح';
END
ELSE
BEGIN
    PRINT 'جدول JournalEntries موجود مسبقاً';
END
GO

-- إنشاء جدول تفاصيل القيود اليومية
-- Create Journal Entry Details table
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'JournalEntryDetails')
BEGIN
    CREATE TABLE JournalEntryDetails (
        JournalEntryDetailId INT IDENTITY(1,1) PRIMARY KEY,
        JournalEntryId INT NOT NULL,
        AccountId INT NOT NULL,
        LineNumber INT NOT NULL,
        DebitAmount DECIMAL(18,2) NOT NULL DEFAULT 0,
        CreditAmount DECIMAL(18,2) NOT NULL DEFAULT 0,
        Description NVARCHAR(500) NULL,
        Reference NVARCHAR(100) NULL,
        CostCenterId INT NULL,
        CreatedDate DATETIME NOT NULL DEFAULT GETDATE(),
        CreatedBy NVARCHAR(50) NOT NULL,
        
        -- Foreign Key Constraints
        CONSTRAINT FK_JournalEntryDetails_JournalEntry 
            FOREIGN KEY (JournalEntryId) REFERENCES JournalEntries(JournalEntryId) 
            ON DELETE CASCADE,
        CONSTRAINT FK_JournalEntryDetails_Account 
            FOREIGN KEY (AccountId) REFERENCES ChartOfAccounts(AccountId),
            
        -- Check Constraints
        CONSTRAINT CK_JournalEntryDetails_Amounts 
            CHECK (DebitAmount >= 0 AND CreditAmount >= 0),
        CONSTRAINT CK_JournalEntryDetails_NotBothAmounts 
            CHECK (NOT (DebitAmount > 0 AND CreditAmount > 0)),
        CONSTRAINT CK_JournalEntryDetails_HasAmount 
            CHECK (DebitAmount > 0 OR CreditAmount > 0),
        CONSTRAINT CK_JournalEntryDetails_LineNumber 
            CHECK (LineNumber > 0),
            
        -- Unique Constraint
        CONSTRAINT UK_JournalEntryDetails_LineNumber 
            UNIQUE (JournalEntryId, LineNumber)
    );
    
    PRINT 'تم إنشاء جدول JournalEntryDetails بنجاح';
END
ELSE
BEGIN
    PRINT 'جدول JournalEntryDetails موجود مسبقاً';
END
GO

-- إنشاء الفهارس
-- Create Indexes

-- فهرس على رقم القيد
CREATE NONCLUSTERED INDEX IX_JournalEntries_EntryNumber 
ON JournalEntries (EntryNumber);

-- فهرس على تاريخ القيد
CREATE NONCLUSTERED INDEX IX_JournalEntries_EntryDate 
ON JournalEntries (EntryDate DESC);

-- فهرس على حالة الترحيل
CREATE NONCLUSTERED INDEX IX_JournalEntries_IsPosted 
ON JournalEntries (IsPosted);

-- فهرس على معرف القيد في التفاصيل
CREATE NONCLUSTERED INDEX IX_JournalEntryDetails_JournalEntryId 
ON JournalEntryDetails (JournalEntryId);

-- فهرس على معرف الحساب في التفاصيل
CREATE NONCLUSTERED INDEX IX_JournalEntryDetails_AccountId 
ON JournalEntryDetails (AccountId);

PRINT 'تم إنشاء الفهارس بنجاح';
GO

-- إنشاء إجراءات مخزنة للتحقق من توازن القيود
-- Create stored procedures for journal entry validation

-- إجراء للتحقق من توازن القيد
CREATE OR ALTER PROCEDURE sp_ValidateJournalEntryBalance
    @JournalEntryId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @TotalDebit DECIMAL(18,2);
    DECLARE @TotalCredit DECIMAL(18,2);
    DECLARE @IsBalanced BIT = 0;
    
    SELECT 
        @TotalDebit = SUM(DebitAmount),
        @TotalCredit = SUM(CreditAmount)
    FROM JournalEntryDetails 
    WHERE JournalEntryId = @JournalEntryId;
    
    IF @TotalDebit = @TotalCredit AND @TotalDebit > 0
        SET @IsBalanced = 1;
    
    SELECT 
        @TotalDebit AS TotalDebit,
        @TotalCredit AS TotalCredit,
        @IsBalanced AS IsBalanced,
        (@TotalDebit - @TotalCredit) AS Difference;
END
GO

-- إجراء لتحديث إجمالي مبلغ القيد
CREATE OR ALTER PROCEDURE sp_UpdateJournalEntryTotal
    @JournalEntryId INT
AS
BEGIN
    SET NOCOUNT ON;
    
    UPDATE JournalEntries 
    SET TotalAmount = (
        SELECT ISNULL(SUM(DebitAmount), 0) 
        FROM JournalEntryDetails 
        WHERE JournalEntryId = @JournalEntryId
    )
    WHERE JournalEntryId = @JournalEntryId;
END
GO

-- إنشاء مشغلات للتحقق من صحة البيانات
-- Create triggers for data validation

-- مشغل للتحقق من توازن القيد عند الإدراج أو التحديث
CREATE OR ALTER TRIGGER tr_JournalEntryDetails_ValidateBalance
ON JournalEntryDetails
AFTER INSERT, UPDATE, DELETE
AS
BEGIN
    SET NOCOUNT ON;
    
    -- الحصول على معرفات القيود المتأثرة
    DECLARE @AffectedEntries TABLE (JournalEntryId INT);
    
    INSERT INTO @AffectedEntries (JournalEntryId)
    SELECT DISTINCT JournalEntryId FROM inserted
    UNION
    SELECT DISTINCT JournalEntryId FROM deleted;
    
    -- تحديث إجمالي المبالغ للقيود المتأثرة
    UPDATE je
    SET TotalAmount = (
        SELECT ISNULL(SUM(DebitAmount), 0) 
        FROM JournalEntryDetails jed 
        WHERE jed.JournalEntryId = je.JournalEntryId
    )
    FROM JournalEntries je
    INNER JOIN @AffectedEntries ae ON je.JournalEntryId = ae.JournalEntryId;
END
GO

-- إدراج بيانات تجريبية
-- Insert sample data

-- قيد تجريبي 1: قيد افتتاحي
IF NOT EXISTS (SELECT * FROM JournalEntries WHERE EntryNumber = 'JE000001')
BEGIN
    INSERT INTO JournalEntries (EntryNumber, EntryDate, Description, CurrencyId, CreatedBy)
    VALUES ('JE000001', '2024-01-01', 'قيد افتتاحي - رصيد أول المدة', 1, 'النظام');
    
    DECLARE @JournalEntryId1 INT = SCOPE_IDENTITY();
    
    -- تفاصيل القيد الافتتاحي
    INSERT INTO JournalEntryDetails (JournalEntryId, AccountId, LineNumber, DebitAmount, CreditAmount, Description, CreatedBy)
    VALUES 
    (@JournalEntryId1, 1, 1, 100000.00, 0.00, 'رصيد افتتاحي - النقدية', 'النظام'),
    (@JournalEntryId1, 2, 2, 50000.00, 0.00, 'رصيد افتتاحي - البنك', 'النظام'),
    (@JournalEntryId1, 10, 3, 0.00, 150000.00, 'رصيد افتتاحي - رأس المال', 'النظام');
    
    PRINT 'تم إدراج القيد التجريبي الأول بنجاح';
END

-- قيد تجريبي 2: قيد شراء
IF NOT EXISTS (SELECT * FROM JournalEntries WHERE EntryNumber = 'JE000002')
BEGIN
    INSERT INTO JournalEntries (EntryNumber, EntryDate, Description, CurrencyId, CreatedBy)
    VALUES ('JE000002', '2024-01-02', 'شراء أثاث مكتبي', 1, 'النظام');
    
    DECLARE @JournalEntryId2 INT = SCOPE_IDENTITY();
    
    -- تفاصيل قيد الشراء
    INSERT INTO JournalEntryDetails (JournalEntryId, AccountId, LineNumber, DebitAmount, CreditAmount, Description, CreatedBy)
    VALUES 
    (@JournalEntryId2, 5, 1, 15000.00, 0.00, 'شراء أثاث مكتبي', 'النظام'),
    (@JournalEntryId2, 2, 2, 0.00, 15000.00, 'دفع من البنك', 'النظام');
    
    PRINT 'تم إدراج القيد التجريبي الثاني بنجاح';
END

-- قيد تجريبي 3: قيد إيراد
IF NOT EXISTS (SELECT * FROM JournalEntries WHERE EntryNumber = 'JE000003')
BEGIN
    INSERT INTO JournalEntries (EntryNumber, EntryDate, Description, CurrencyId, CreatedBy)
    VALUES ('JE000003', '2024-01-03', 'إيراد خدمات استشارية', 1, 'النظام');
    
    DECLARE @JournalEntryId3 INT = SCOPE_IDENTITY();
    
    -- تفاصيل قيد الإيراد
    INSERT INTO JournalEntryDetails (JournalEntryId, AccountId, LineNumber, DebitAmount, CreditAmount, Description, CreatedBy)
    VALUES 
    (@JournalEntryId3, 1, 1, 25000.00, 0.00, 'تحصيل نقدي', 'النظام'),
    (@JournalEntryId3, 15, 2, 0.00, 25000.00, 'إيراد خدمات استشارية', 'النظام');
    
    PRINT 'تم إدراج القيد التجريبي الثالث بنجاح';
END

PRINT 'تم إنشاء جداول القيود اليومية وإدراج البيانات التجريبية بنجاح';
GO
