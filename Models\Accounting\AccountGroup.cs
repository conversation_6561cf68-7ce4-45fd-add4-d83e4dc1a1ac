using System;

namespace Awqaf_Managment.Models.Accounting
{
    /// <summary>
    /// نموذج بيانات مجموعة الحساب
    /// Account Group Data Model
    /// </summary>
    public class AccountGroup
    {
        #region Properties
        /// <summary>
        /// معرف مجموعة الحساب
        /// Account Group ID
        /// </summary>
        public int AccountGroupId { get; set; }

        /// <summary>
        /// اسم مجموعة الحساب بالعربية
        /// Account Group Name in Arabic
        /// </summary>
        public string GroupNameAr { get; set; } = string.Empty;

        /// <summary>
        /// اسم مجموعة الحساب بالإنجليزية
        /// Account Group Name in English
        /// </summary>
        public string GroupNameEn { get; set; } = string.Empty;

        /// <summary>
        /// رمز مجموعة الحساب
        /// Account Group Code
        /// </summary>
        public string GroupCode { get; set; } = string.Empty;

        /// <summary>
        /// الوصف
        /// Description
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// طبيعة الحساب الافتراضية
        /// Default Account Nature
        /// </summary>
        public AccountNature DefaultNature { get; set; } = AccountNature.Debit;

        /// <summary>
        /// معرف المجموعة الأب
        /// Parent Group ID
        /// </summary>
        public int? ParentGroupId { get; set; }

        /// <summary>
        /// حالة النشاط
        /// Active Status
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// ترتيب العرض
        /// Display Order
        /// </summary>
        public int DisplayOrder { get; set; } = 0;

        /// <summary>
        /// لون المجموعة (للعرض)
        /// Group Color (for display)
        /// </summary>
        public string Color { get; set; } = "#3498db";

        /// <summary>
        /// أيقونة المجموعة
        /// Group Icon
        /// </summary>
        public string Icon { get; set; } = "📁";

        /// <summary>
        /// تاريخ الإنشاء
        /// Creation Date
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تعديل
        /// Last Modified Date
        /// </summary>
        public DateTime? ModifiedDate { get; set; }

        /// <summary>
        /// معرف المستخدم المنشئ
        /// Created By User ID
        /// </summary>
        public int CreatedBy { get; set; }

        /// <summary>
        /// معرف المستخدم المعدل
        /// Modified By User ID
        /// </summary>
        public int? ModifiedBy { get; set; }

        /// <summary>
        /// معرف نوع الحساب
        /// Account Type ID
        /// </summary>
        public int AccountTypeId { get; set; }

        /// <summary>
        /// اسم المجموعة (للتوافق مع الإصدارات القديمة)
        /// Group Name (for backward compatibility)
        /// </summary>
        public string GroupName { get; set; } = string.Empty;

        /// <summary>
        /// اسم نوع الحساب
        /// Account Type Name
        /// </summary>
        public string AccountTypeName { get; set; } = string.Empty;

        /// <summary>
        /// اسم نوع الحساب بالعربية
        /// Account Type Name in Arabic
        /// </summary>
        public string AccountTypeNameAr { get; set; } = string.Empty;

        /// <summary>
        /// اسم المجموعة (للتوافق مع الإصدارات القديمة)
        /// Account Group Name (for backward compatibility)
        /// </summary>
        public string AccountGroupName => GroupNameAr;
        #endregion

        #region Constructor
        /// <summary>
        /// منشئ افتراضي
        /// Default Constructor
        /// </summary>
        public AccountGroup()
        {
            CreatedDate = DateTime.Now;
            IsActive = true;
            DefaultNature = AccountNature.Debit;
            Color = "#3498db";
            Icon = "📁";
        }

        /// <summary>
        /// منشئ بمعاملات
        /// Parameterized Constructor
        /// </summary>
        /// <param name="groupNameAr">اسم المجموعة بالعربية</param>
        /// <param name="groupCode">رمز المجموعة</param>
        /// <param name="defaultNature">الطبيعة الافتراضية</param>
        public AccountGroup(string groupNameAr, string groupCode, AccountNature defaultNature = AccountNature.Debit)
        {
            GroupNameAr = groupNameAr;
            GroupCode = groupCode;
            DefaultNature = defaultNature;
            CreatedDate = DateTime.Now;
            IsActive = true;
            Color = "#3498db";
            Icon = "📁";
        }
        #endregion

        #region Methods
        /// <summary>
        /// التحقق من صحة بيانات مجموعة الحساب
        /// Validate account group data
        /// </summary>
        /// <returns>true إذا كانت البيانات صحيحة</returns>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(GroupNameAr) &&
                   !string.IsNullOrWhiteSpace(GroupCode);
        }

        /// <summary>
        /// إرجاع تمثيل نصي لمجموعة الحساب
        /// Return string representation of account group
        /// </summary>
        /// <returns>النص الممثل لمجموعة الحساب</returns>
        public override string ToString()
        {
            return $"{GroupCode} - {GroupNameAr}";
        }

        /// <summary>
        /// مقارنة مجموعات الحسابات
        /// Compare account groups
        /// </summary>
        /// <param name="obj">الكائن للمقارنة</param>
        /// <returns>true إذا كانت المجموعات متطابقة</returns>
        public override bool Equals(object obj)
        {
            AccountGroup other = obj as AccountGroup;
            if (other != null)
            {
                return AccountGroupId == other.AccountGroupId ||
                       (AccountGroupId == 0 && other.AccountGroupId == 0 &&
                        GroupCode.Equals(other.GroupCode, StringComparison.OrdinalIgnoreCase));
            }
            return false;
        }

        /// <summary>
        /// الحصول على رمز التجمع
        /// Get hash code
        /// </summary>
        /// <returns>رمز التجمع</returns>
        public override int GetHashCode()
        {
            return AccountGroupId != 0 ? AccountGroupId.GetHashCode() : GroupCode.GetHashCode();
        }
        #endregion
    }
}
