USE AwqafManagement;
GO

-- حذف المستخدم الحالي وإعادة إنشاؤه
DELETE FROM UserRoles WHERE UserId = (SELECT UserId FROM Users WHERE Username = 'admin');
DELETE FROM Users WHERE Username = 'admin';

-- إنشاء مستخدم admin جديد
-- كلمة المرور: admin123
-- Salt: admin_salt_123
-- Hash: SHA256(admin123 + admin_salt_123) في Base64

DECLARE @Salt NVARCHAR(255) = 'admin_salt_123';
DECLARE @Password NVARCHAR(255) = 'admin123';

-- إدراج المستخدم الجديد
INSERT INTO Users (Username, PasswordHash, Salt, FullName, Email, IsActive) 
VALUES ('admin', 'YourHashWillBeCalculatedHere', @Salt, 'مدير النظام', '<EMAIL>', 1);

-- الحصول على معرف المستخدم الجديد
DECLARE @AdminUserId INT = (SELECT UserId FROM Users WHERE Username = 'admin');

-- ربط المدير بدور مدير النظام
DECLARE @AdminRoleId INT = (SELECT RoleId FROM Roles WHERE RoleName = 'Administrator');

IF @AdminRoleId IS NOT NULL
BEGIN
    INSERT INTO UserRoles (UserId, RoleId) VALUES (@AdminUserId, @AdminRoleId);
END

-- الآن سنحدث كلمة المرور بالتشفير الصحيح
-- سنستخدم salt بسيط: "salt123"
-- admin123 + salt123 = admin123salt123

UPDATE Users
SET PasswordHash = 'YourHashWillBeCalculatedHere', Salt = 'salt123'
WHERE Username = 'admin';

PRINT 'تم إعادة تعيين كلمة مرور المدير بنجاح';
PRINT 'اسم المستخدم: admin';
PRINT 'كلمة المرور: admin123';

-- التحقق من البيانات
SELECT Username, FullName, Salt, LEFT(PasswordHash, 20) + '...' AS PasswordHash_Preview
FROM Users WHERE Username = 'admin';
