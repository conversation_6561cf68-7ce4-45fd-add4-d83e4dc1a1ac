-- إضافة الإيرادات والمصروفات
-- Add Revenues and Expenses

USE AwqafManagement;
GO

-- إدراج الإيرادات والمصروفات
SET IDENTITY_INSERT ChartOfAccounts ON;

-- ===== 4. الإيرادات (Revenues) =====
INSERT INTO ChartOfAccounts (AccountId, AccountCode, AccountName, AccountNameAr, AccountTypeId, AccountGroupId, ParentAccountId, AccountLevel, IsParent, IsActive, AllowPosting, Description, CurrencyCode, OpeningBalance, CurrentBalance, CreatedDate, CreatedBy)
VALUES 
-- 4.0 الإيرادات - الحساب الرئيسي
(32, N'4.00.000', N'Revenues', N'الإيرادات', 1006, 1012, NULL, 1, 1, 1, 0, N'مجموعة الإيرادات الرئيسية', N'SAR', 0.00, 0.00, GETDATE(), 1),

-- 4.1 إيرادات التشغيل
(33, N'4.01.000', N'Operating Revenues', N'إيرادات التشغيل', 1006, 1012, 32, 2, 1, 1, 0, N'إيرادات التشغيل الأساسية', N'SAR', 0.00, 0.00, GETDATE(), 1),

-- 4.1.1 إيرادات الإيجارات
(34, N'4.01.001', N'Residential Rental Income', N'إيرادات الإيجارات السكنية', 1006, 1012, 33, 3, 0, 1, 1, N'إيرادات إيجار العقارات السكنية', N'SAR', 0.00, 0.00, GETDATE(), 1),
(35, N'4.01.002', N'Commercial Rental Income', N'إيرادات الإيجارات التجارية', 1006, 1012, 33, 3, 0, 1, 1, N'إيرادات إيجار العقارات التجارية', N'SAR', 0.00, 0.00, GETDATE(), 1),
(36, N'4.01.003', N'Administrative Rental Income', N'إيرادات الإيجارات الإدارية', 1006, 1012, 33, 3, 0, 1, 1, N'إيرادات إيجار المكاتب الإدارية', N'SAR', 0.00, 0.00, GETDATE(), 1),

-- 4.1.2 إيرادات الاستثمارات
(37, N'4.01.010', N'Investment Income', N'إيرادات الاستثمارات', 1006, 1012, 33, 3, 0, 1, 1, N'إيرادات الاستثمارات المالية', N'SAR', 0.00, 0.00, GETDATE(), 1),
(38, N'4.01.011', N'Dividend Income', N'أرباح الأسهم', 1006, 1012, 33, 3, 0, 1, 1, N'أرباح الأسهم والاستثمارات', N'SAR', 0.00, 0.00, GETDATE(), 1),
(39, N'4.01.012', N'Bank Deposit Interest', N'فوائد الودائع البنكية', 1006, 1012, 33, 3, 0, 1, 1, N'فوائد الودائع البنكية', N'SAR', 0.00, 0.00, GETDATE(), 1),

-- 4.2 إيرادات أخرى
(40, N'4.02.000', N'Other Revenues', N'إيرادات أخرى', 1006, 1012, 32, 2, 1, 1, 0, N'الإيرادات الأخرى', N'SAR', 0.00, 0.00, GETDATE(), 1),
(41, N'4.02.001', N'Donations and Gifts', N'التبرعات والهبات', 1006, 1012, 40, 3, 0, 1, 1, N'التبرعات والهبات الواردة', N'SAR', 0.00, 0.00, GETDATE(), 1),
(42, N'4.02.002', N'Miscellaneous Income', N'إيرادات متنوعة', 1006, 1012, 40, 3, 0, 1, 1, N'إيرادات متنوعة أخرى', N'SAR', 0.00, 0.00, GETDATE(), 1),

-- ===== 5. المصروفات (Expenses) =====
(43, N'5.00.000', N'Expenses', N'المصروفات', 1007, 1013, NULL, 1, 1, 1, 0, N'مجموعة المصروفات الرئيسية', N'SAR', 0.00, 0.00, GETDATE(), 1),

-- 5.1 مصروفات التشغيل
(44, N'5.01.000', N'Operating Expenses', N'مصروفات التشغيل', 1007, 1013, 43, 2, 1, 1, 0, N'مصروفات التشغيل الأساسية', N'SAR', 0.00, 0.00, GETDATE(), 1),

-- 5.1.1 الرواتب والأجور
(45, N'5.01.001', N'Salaries and Wages', N'الرواتب والأجور', 1007, 1013, 44, 3, 0, 1, 1, N'رواتب وأجور الموظفين', N'SAR', 0.00, 0.00, GETDATE(), 1),
(46, N'5.01.002', N'Employee Allowances', N'بدلات الموظفين', 1007, 1013, 44, 3, 0, 1, 1, N'بدلات ومكافآت الموظفين', N'SAR', 0.00, 0.00, GETDATE(), 1),
(47, N'5.01.003', N'Social Insurance', N'التأمينات الاجتماعية', 1007, 1013, 44, 3, 0, 1, 1, N'التأمينات الاجتماعية للموظفين', N'SAR', 0.00, 0.00, GETDATE(), 1),

-- 5.1.2 مصروفات الصيانة
(48, N'5.01.010', N'Maintenance Expenses', N'مصروفات الصيانة', 1007, 1013, 44, 3, 0, 1, 1, N'مصروفات صيانة العقارات', N'SAR', 0.00, 0.00, GETDATE(), 1),
(49, N'5.01.011', N'Building Maintenance', N'صيانة المباني', 1007, 1013, 44, 3, 0, 1, 1, N'صيانة المباني والإنشاءات', N'SAR', 0.00, 0.00, GETDATE(), 1),
(50, N'5.01.012', N'Equipment Maintenance', N'صيانة المعدات', 1007, 1013, 44, 3, 0, 1, 1, N'صيانة المعدات والأجهزة', N'SAR', 0.00, 0.00, GETDATE(), 1),

-- 5.1.3 المصروفات الإدارية
(51, N'5.01.020', N'Administrative Expenses', N'المصروفات الإدارية', 1007, 1013, 44, 3, 0, 1, 1, N'المصروفات الإدارية العامة', N'SAR', 0.00, 0.00, GETDATE(), 1),
(52, N'5.01.021', N'Utilities Expenses', N'مصروفات الكهرباء والماء', 1007, 1013, 44, 3, 0, 1, 1, N'مصروفات الكهرباء والماء والهاتف', N'SAR', 0.00, 0.00, GETDATE(), 1),
(53, N'5.01.022', N'Insurance Expenses', N'مصروفات التأمين', 1007, 1013, 44, 3, 0, 1, 1, N'مصروفات التأمين على العقارات', N'SAR', 0.00, 0.00, GETDATE(), 1),
(54, N'5.01.023', N'Stationery Expenses', N'مصروفات القرطاسية', 1007, 1013, 44, 3, 0, 1, 1, N'مصروفات القرطاسية والمكتبية', N'SAR', 0.00, 0.00, GETDATE(), 1),
(55, N'5.01.024', N'Communication Expenses', N'مصروفات الاتصالات', 1007, 1013, 44, 3, 0, 1, 1, N'مصروفات الهاتف والإنترنت', N'SAR', 0.00, 0.00, GETDATE(), 1),
(56, N'5.01.025', N'Travel Expenses', N'مصروفات السفر والانتقال', 1007, 1013, 44, 3, 0, 1, 1, N'مصروفات السفر والانتقال', N'SAR', 0.00, 0.00, GETDATE(), 1),

-- 5.2 مصروفات أخرى
(57, N'5.02.000', N'Other Expenses', N'مصروفات أخرى', 1007, 1013, 43, 2, 1, 1, 0, N'المصروفات الأخرى', N'SAR', 0.00, 0.00, GETDATE(), 1),
(58, N'5.02.001', N'Bank Charges', N'رسوم بنكية', 1007, 1013, 57, 3, 0, 1, 1, N'الرسوم والعمولات البنكية', N'SAR', 0.00, 0.00, GETDATE(), 1),
(59, N'5.02.002', N'Legal Expenses', N'مصروفات قانونية', 1007, 1013, 57, 3, 0, 1, 1, N'المصروفات القانونية والاستشارية', N'SAR', 0.00, 0.00, GETDATE(), 1),
(60, N'5.02.003', N'Miscellaneous Expenses', N'مصروفات متنوعة', 1007, 1013, 57, 3, 0, 1, 1, N'مصروفات متنوعة أخرى', N'SAR', 0.00, 0.00, GETDATE(), 1);

SET IDENTITY_INSERT ChartOfAccounts OFF;
GO

-- عرض ملخص الحسابات المدرجة
SELECT 
    CASE AccountLevel 
        WHEN 1 THEN N'رئيسي'
        WHEN 2 THEN N'فرعي'
        WHEN 3 THEN N'تفصيلي'
    END as 'نوع الحساب',
    COUNT(*) as 'عدد الحسابات'
FROM ChartOfAccounts 
GROUP BY AccountLevel
ORDER BY AccountLevel;

-- عرض إجمالي الحسابات
SELECT COUNT(*) as 'إجمالي الحسابات' FROM ChartOfAccounts;

PRINT N'تم إدراج 60 حساب محاسبي شامل (رئيسي وفرعي وتفصيلي) بنجاح';
