using System.ComponentModel;

namespace Awqaf_Managment.Models.Accounting
{
    /// <summary>
    /// أنواع القيود اليومية
    /// Journal Entry Types
    /// </summary>
    public enum JournalType
    {
        /// <summary>
        /// قيد عادي
        /// Normal Entry
        /// </summary>
        [Description("قيد عادي")]
        Normal = 1,

        /// <summary>
        /// قيد افتتاحي
        /// Opening Entry
        /// </summary>
        [Description("قيد افتتاحي")]
        Opening = 2,

        /// <summary>
        /// قيد إقفال
        /// Closing Entry
        /// </summary>
        [Description("قيد إقفال")]
        Closing = 3,

        /// <summary>
        /// قيد تسوية
        /// Adjustment Entry
        /// </summary>
        [Description("قيد تسوية")]
        Adjustment = 4,

        /// <summary>
        /// قيد عكسي
        /// Reversing Entry
        /// </summary>
        [Description("قيد عكسي")]
        Reversing = 5,

        /// <summary>
        /// قيد تصحيحي
        /// Correcting Entry
        /// </summary>
        [Description("قيد تصحيحي")]
        Correcting = 6
    }

    /// <summary>
    /// حالات القيد اليومي
    /// Journal Entry Status
    /// </summary>
    public enum JournalStatus
    {
        /// <summary>
        /// مسودة
        /// Draft
        /// </summary>
        [Description("مسودة")]
        Draft = 1,

        /// <summary>
        /// معتمد
        /// Approved
        /// </summary>
        [Description("معتمد")]
        Approved = 2,

        /// <summary>
        /// مرحل
        /// Posted
        /// </summary>
        [Description("مرحل")]
        Posted = 3,

        /// <summary>
        /// ملغي
        /// Cancelled
        /// </summary>
        [Description("ملغي")]
        Cancelled = 4,

        /// <summary>
        /// مرفوض
        /// Rejected
        /// </summary>
        [Description("مرفوض")]
        Rejected = 5
    }
}
