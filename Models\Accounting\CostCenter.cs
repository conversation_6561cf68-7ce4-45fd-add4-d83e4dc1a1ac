using System;
using System.ComponentModel.DataAnnotations;

namespace Awqaf_Managment.Models.Accounting
{
    /// <summary>
    /// نموذج مركز التكلفة
    /// Cost Center Model
    /// </summary>
    public class CostCenter
    {
        #region Properties
        
        /// <summary>
        /// معرف مركز التكلفة
        /// Cost Center ID
        /// </summary>
        public int CostCenterId { get; set; }

        /// <summary>
        /// رمز مركز التكلفة
        /// Cost Center Code
        /// </summary>
        [Required]
        [StringLength(20)]
        public string CostCenterCode { get; set; } = string.Empty;

        /// <summary>
        /// اسم مركز التكلفة بالعربية
        /// Cost Center Name in Arabic
        /// </summary>
        [Required]
        [StringLength(100)]
        public string CostCenterNameAr { get; set; } = string.Empty;

        /// <summary>
        /// اسم مركز التكلفة بالإنجليزية
        /// Cost Center Name in English
        /// </summary>
        [StringLength(100)]
        public string CostCenterNameEn { get; set; } = string.Empty;

        /// <summary>
        /// الوصف
        /// Description
        /// </summary>
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// معرف مركز التكلفة الأب
        /// Parent Cost Center ID
        /// </summary>
        public int? ParentCostCenterId { get; set; }

        /// <summary>
        /// هل هو نشط
        /// Is Active
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// تاريخ الإنشاء
        /// Creation Date
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// معرف المستخدم المنشئ
        /// Created By User ID
        /// </summary>
        public int CreatedBy { get; set; }

        /// <summary>
        /// تاريخ آخر تعديل
        /// Last Modified Date
        /// </summary>
        public DateTime? ModifiedDate { get; set; }

        /// <summary>
        /// معرف المستخدم المعدل
        /// Modified By User ID
        /// </summary>
        public int? ModifiedBy { get; set; }

        #endregion

        #region Constructor

        /// <summary>
        /// منشئ افتراضي
        /// Default Constructor
        /// </summary>
        public CostCenter()
        {
            CreatedDate = DateTime.Now;
            IsActive = true;
        }

        /// <summary>
        /// منشئ بمعاملات
        /// Parameterized Constructor
        /// </summary>
        /// <param name="code">رمز مركز التكلفة</param>
        /// <param name="nameAr">الاسم بالعربية</param>
        /// <param name="nameEn">الاسم بالإنجليزية</param>
        public CostCenter(string code, string nameAr, string nameEn = "")
        {
            CostCenterCode = code;
            CostCenterNameAr = nameAr;
            CostCenterNameEn = nameEn;
            CreatedDate = DateTime.Now;
            IsActive = true;
        }

        #endregion

        #region Methods

        /// <summary>
        /// إرجاع تمثيل نصي لمركز التكلفة
        /// String representation of the cost center
        /// </summary>
        /// <returns></returns>
        public override string ToString()
        {
            return $"{CostCenterCode} - {CostCenterNameAr}";
        }

        #endregion
    }
}
