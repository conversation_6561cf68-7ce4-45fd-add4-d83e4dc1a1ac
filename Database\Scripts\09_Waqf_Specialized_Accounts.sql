-- ===================================================================
-- إضافة الحسابات المتخصصة للأوقاف
-- Add Specialized Waqf Accounts
-- ===================================================================

USE AwqafManagement;
GO

-- ===================================================================
-- إضافة أنواع حسابات خاصة بالأوقاف
-- Add Waqf-specific Account Types
-- ===================================================================

-- التحقق من وجود أنواع الحسابات الأساسية أولاً
IF NOT EXISTS (SELECT 1 FROM AccountTypes WHERE AccountTypeCode = 'WAQF_ASSETS')
BEGIN
    INSERT INTO AccountTypes (AccountTypeCode, AccountTypeName, AccountTypeNameAr, Description, DisplayOrder, IsActive, CreatedDate)
    VALUES 
    ('WAQF_ASSETS', 'Waqf Assets', N'الأصول الوقفية', N'جميع الأصول المخصصة للوقف والمحبسة لله', 6, 1, GETDATE()),
    ('WAQF_REVENUE', 'Waqf Revenue', N'إيرادات الأوقاف', N'جميع الإيرادات الناتجة من الأوقاف', 7, 1, GETDATE()),
    ('WAQF_EXPENSES', 'Waqf Expenses', N'مصروفات الأوقاف', N'جميع المصروفات المتعلقة بإدارة وصيانة الأوقاف', 8, 1, GETDATE()),
    ('BENEFICIARIES', 'Beneficiaries', N'المستفيدون', N'حسابات المستفيدين من الأوقاف', 9, 1, GETDATE()),
    ('WAQF_RESERVES', 'Waqf Reserves', N'احتياطيات الأوقاف', N'الاحتياطيات والمخصصات الخاصة بالأوقاف', 10, 1, GETDATE());
    
    PRINT N'✓ تم إضافة أنواع الحسابات الخاصة بالأوقاف';
END

-- ===================================================================
-- إضافة مجموعات حسابات خاصة بالأوقاف
-- Add Waqf-specific Account Groups
-- ===================================================================

DECLARE @WaqfAssetsTypeId INT = (SELECT AccountTypeId FROM AccountTypes WHERE AccountTypeCode = 'WAQF_ASSETS');
DECLARE @WaqfRevenueTypeId INT = (SELECT AccountTypeId FROM AccountTypes WHERE AccountTypeCode = 'WAQF_REVENUE');
DECLARE @WaqfExpensesTypeId INT = (SELECT AccountTypeId FROM AccountTypes WHERE AccountTypeCode = 'WAQF_EXPENSES');
DECLARE @BeneficiariesTypeId INT = (SELECT AccountTypeId FROM AccountTypes WHERE AccountTypeCode = 'BENEFICIARIES');
DECLARE @WaqfReservesTypeId INT = (SELECT AccountTypeId FROM AccountTypes WHERE AccountTypeCode = 'WAQF_RESERVES');

IF NOT EXISTS (SELECT 1 FROM AccountGroups WHERE GroupCode = 'WAQF_REAL_ESTATE')
BEGIN
    INSERT INTO AccountGroups (GroupCode, GroupName, GroupNameAr, AccountTypeId, Description, DisplayOrder, IsActive, CreatedDate)
    VALUES 
    -- مجموعات الأصول الوقفية
    ('WAQF_REAL_ESTATE', 'Waqf Real Estate', N'العقارات الوقفية', @WaqfAssetsTypeId, N'جميع العقارات المحبسة للوقف', 1, 1, GETDATE()),
    ('WAQF_INVESTMENTS', 'Waqf Investments', N'الاستثمارات الوقفية', @WaqfAssetsTypeId, N'الاستثمارات والأوقاف النقدية', 2, 1, GETDATE()),
    ('WAQF_EQUIPMENT', 'Waqf Equipment', N'معدات الأوقاف', @WaqfAssetsTypeId, N'المعدات والأثاث الخاص بالأوقاف', 3, 1, GETDATE()),
    
    -- مجموعات إيرادات الأوقاف
    ('RENTAL_INCOME', 'Rental Income', N'إيرادات الإيجارات', @WaqfRevenueTypeId, N'إيرادات إيجار العقارات الوقفية', 4, 1, GETDATE()),
    ('INVESTMENT_INCOME', 'Investment Income', N'إيرادات الاستثمارات', @WaqfRevenueTypeId, N'عوائد الاستثمارات الوقفية', 5, 1, GETDATE()),
    ('DONATIONS', 'Donations', N'التبرعات والهبات', @WaqfRevenueTypeId, N'التبرعات والهبات الجديدة للوقف', 6, 1, GETDATE()),
    
    -- مجموعات مصروفات الأوقاف
    ('MAINTENANCE_COSTS', 'Maintenance Costs', N'تكاليف الصيانة', @WaqfExpensesTypeId, N'تكاليف صيانة وإصلاح الأوقاف', 7, 1, GETDATE()),
    ('ADMIN_EXPENSES', 'Administrative Expenses', N'المصروفات الإدارية', @WaqfExpensesTypeId, N'مصروفات إدارة الأوقاف', 8, 1, GETDATE()),
    ('DEVELOPMENT_COSTS', 'Development Costs', N'تكاليف التطوير', @WaqfExpensesTypeId, N'تكاليف تطوير وتحسين الأوقاف', 9, 1, GETDATE()),
    
    -- مجموعات المستفيدين
    ('POOR_NEEDY', 'Poor and Needy', N'الفقراء والمساكين', @BeneficiariesTypeId, N'مستحقات الفقراء والمساكين', 10, 1, GETDATE()),
    ('STUDENTS', 'Students', N'طلاب العلم', @BeneficiariesTypeId, N'مستحقات طلاب العلم والمنح الدراسية', 11, 1, GETDATE()),
    ('CHARITY_WORKS', 'Charity Works', N'الأعمال الخيرية', @BeneficiariesTypeId, N'مستحقات الأعمال الخيرية والمشاريع الاجتماعية', 12, 1, GETDATE()),
    
    -- مجموعات الاحتياطيات
    ('MAINTENANCE_RESERVE', 'Maintenance Reserve', N'احتياطي الصيانة', @WaqfReservesTypeId, N'احتياطي صيانة وإصلاح الأوقاف', 13, 1, GETDATE()),
    ('DEVELOPMENT_RESERVE', 'Development Reserve', N'احتياطي التطوير', @WaqfReservesTypeId, N'احتياطي تطوير وتوسيع الأوقاف', 14, 1, GETDATE()),
    ('EMERGENCY_RESERVE', 'Emergency Reserve', N'احتياطي الطوارئ', @WaqfReservesTypeId, N'احتياطي للحالات الطارئة', 15, 1, GETDATE());
    
    PRINT N'✓ تم إضافة مجموعات الحسابات الخاصة بالأوقاف';
END

-- ===================================================================
-- إضافة الحسابات الرئيسية للأوقاف
-- Add Main Waqf Accounts
-- ===================================================================

-- الحصول على معرفات المجموعات
DECLARE @WaqfRealEstateGroupId INT = (SELECT AccountGroupId FROM AccountGroups WHERE GroupCode = 'WAQF_REAL_ESTATE');
DECLARE @WaqfInvestmentsGroupId INT = (SELECT AccountGroupId FROM AccountGroups WHERE GroupCode = 'WAQF_INVESTMENTS');
DECLARE @RentalIncomeGroupId INT = (SELECT AccountGroupId FROM AccountGroups WHERE GroupCode = 'RENTAL_INCOME');
DECLARE @InvestmentIncomeGroupId INT = (SELECT AccountGroupId FROM AccountGroups WHERE GroupCode = 'INVESTMENT_INCOME');
DECLARE @DonationsGroupId INT = (SELECT AccountGroupId FROM AccountGroups WHERE GroupCode = 'DONATIONS');
DECLARE @MaintenanceCostsGroupId INT = (SELECT AccountGroupId FROM AccountGroups WHERE GroupCode = 'MAINTENANCE_COSTS');
DECLARE @AdminExpensesGroupId INT = (SELECT AccountGroupId FROM AccountGroups WHERE GroupCode = 'ADMIN_EXPENSES');
DECLARE @PoorNeedyGroupId INT = (SELECT AccountGroupId FROM AccountGroups WHERE GroupCode = 'POOR_NEEDY');
DECLARE @StudentsGroupId INT = (SELECT AccountGroupId FROM AccountGroups WHERE GroupCode = 'STUDENTS');
DECLARE @MaintenanceReserveGroupId INT = (SELECT AccountGroupId FROM AccountGroups WHERE GroupCode = 'MAINTENANCE_RESERVE');

-- التحقق من عدم وجود الحسابات مسبقاً
IF NOT EXISTS (SELECT 1 FROM ChartOfAccounts WHERE AccountCode = '6.00.000')
BEGIN
    INSERT INTO ChartOfAccounts (
        AccountCode, AccountNameAr, AccountNameEn, AccountTypeId, AccountGroupId,
        ParentAccountId, LevelType, Nature, OpeningBalance, OpeningBalanceType,
        CurrencyId, Description, AllowPosting, AllowDirectEntry, IsActive,
        AccountLevel, IsParent, IsParentAccount, CreatedDate, CreatedBy
    ) VALUES 
    
    -- ===== الأصول الوقفية =====
    ('6.00.000', N'الأصول الوقفية', 'Waqf Assets', @WaqfAssetsTypeId, @WaqfRealEstateGroupId, 
     NULL, 1, 1, 0, 1, 1, N'جميع الأصول المحبسة للوقف', 0, 0, 1, 1, 1, 1, GETDATE(), 1),
    
    ('6.01.000', N'العقارات الوقفية', 'Waqf Real Estate', @WaqfAssetsTypeId, @WaqfRealEstateGroupId,
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '6.00.000'), 2, 1, 0, 1, 1, N'العقارات المحبسة للوقف', 0, 0, 1, 2, 1, 1, GETDATE(), 1),
    
    ('6.01.001', N'العقارات السكنية الوقفية', 'Waqf Residential Properties', @WaqfAssetsTypeId, @WaqfRealEstateGroupId,
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '6.01.000'), 3, 1, 0, 1, 1, N'الشقق والفلل الوقفية', 1, 1, 1, 3, 0, 0, GETDATE(), 1),
    
    ('6.01.002', N'العقارات التجارية الوقفية', 'Waqf Commercial Properties', @WaqfAssetsTypeId, @WaqfRealEstateGroupId,
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '6.01.000'), 3, 1, 0, 1, 1, N'المحلات والمكاتب الوقفية', 1, 1, 1, 3, 0, 0, GETDATE(), 1),
    
    ('6.01.003', N'الأراضي الوقفية', 'Waqf Lands', @WaqfAssetsTypeId, @WaqfRealEstateGroupId,
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '6.01.000'), 3, 1, 0, 1, 1, N'الأراضي المحبسة للوقف', 1, 1, 1, 3, 0, 0, GETDATE(), 1),
    
    ('6.02.000', N'الاستثمارات الوقفية', 'Waqf Investments', @WaqfAssetsTypeId, @WaqfInvestmentsGroupId,
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '6.00.000'), 2, 1, 0, 1, 1, N'الاستثمارات والأوقاف النقدية', 0, 0, 1, 2, 1, 1, GETDATE(), 1),
    
    ('6.02.001', N'الأوقاف النقدية المستثمرة', 'Invested Cash Waqf', @WaqfAssetsTypeId, @WaqfInvestmentsGroupId,
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '6.02.000'), 3, 1, 0, 1, 1, N'الأموال النقدية المحبسة والمستثمرة', 1, 1, 1, 3, 0, 0, GETDATE(), 1),
    
    -- ===== إيرادات الأوقاف =====
    ('7.00.000', N'إيرادات الأوقاف', 'Waqf Revenue', @WaqfRevenueTypeId, @RentalIncomeGroupId,
     NULL, 1, 2, 0, 2, 1, N'جميع إيرادات الأوقاف', 0, 0, 1, 1, 1, 1, GETDATE(), 1),
    
    ('7.01.000', N'إيرادات الإيجارات', 'Rental Income', @WaqfRevenueTypeId, @RentalIncomeGroupId,
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '7.00.000'), 2, 2, 0, 2, 1, N'إيرادات إيجار العقارات الوقفية', 0, 0, 1, 2, 1, 1, GETDATE(), 1),
    
    ('7.01.001', N'إيجار العقارات السكنية', 'Residential Rental Income', @WaqfRevenueTypeId, @RentalIncomeGroupId,
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '7.01.000'), 3, 2, 0, 2, 1, N'إيرادات إيجار الشقق والفلل', 1, 1, 1, 3, 0, 0, GETDATE(), 1),
    
    ('7.01.002', N'إيجار العقارات التجارية', 'Commercial Rental Income', @WaqfRevenueTypeId, @RentalIncomeGroupId,
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '7.01.000'), 3, 2, 0, 2, 1, N'إيرادات إيجار المحلات والمكاتب', 1, 1, 1, 3, 0, 0, GETDATE(), 1),
    
    ('7.02.000', N'إيرادات الاستثمارات', 'Investment Income', @WaqfRevenueTypeId, @InvestmentIncomeGroupId,
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '7.00.000'), 2, 2, 0, 2, 1, N'عوائد الاستثمارات الوقفية', 0, 0, 1, 2, 1, 1, GETDATE(), 1),
    
    ('7.02.001', N'أرباح الاستثمارات النقدية', 'Cash Investment Profits', @WaqfRevenueTypeId, @InvestmentIncomeGroupId,
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '7.02.000'), 3, 2, 0, 2, 1, N'أرباح استثمار الأوقاف النقدية', 1, 1, 1, 3, 0, 0, GETDATE(), 1),
    
    ('7.03.000', N'التبرعات والهبات', 'Donations and Gifts', @WaqfRevenueTypeId, @DonationsGroupId,
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '7.00.000'), 2, 2, 0, 2, 1, N'التبرعات والهبات الجديدة', 0, 0, 1, 2, 1, 1, GETDATE(), 1),
    
    ('7.03.001', N'تبرعات نقدية جديدة', 'New Cash Donations', @WaqfRevenueTypeId, @DonationsGroupId,
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '7.03.000'), 3, 2, 0, 2, 1, N'التبرعات النقدية الجديدة للوقف', 1, 1, 1, 3, 0, 0, GETDATE(), 1),
    
    -- ===== مصروفات الأوقاف =====
    ('8.00.000', N'مصروفات الأوقاف', 'Waqf Expenses', @WaqfExpensesTypeId, @MaintenanceCostsGroupId,
     NULL, 1, 1, 0, 1, 1, N'جميع مصروفات إدارة الأوقاف', 0, 0, 1, 1, 1, 1, GETDATE(), 1),
    
    ('8.01.000', N'مصروفات الصيانة', 'Maintenance Expenses', @WaqfExpensesTypeId, @MaintenanceCostsGroupId,
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '8.00.000'), 2, 1, 0, 1, 1, N'تكاليف صيانة العقارات الوقفية', 0, 0, 1, 2, 1, 1, GETDATE(), 1),
    
    ('8.01.001', N'صيانة العقارات السكنية', 'Residential Maintenance', @WaqfExpensesTypeId, @MaintenanceCostsGroupId,
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '8.01.000'), 3, 1, 0, 1, 1, N'تكاليف صيانة الشقق والفلل', 1, 1, 1, 3, 0, 0, GETDATE(), 1),
    
    ('8.01.002', N'صيانة العقارات التجارية', 'Commercial Maintenance', @WaqfExpensesTypeId, @MaintenanceCostsGroupId,
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '8.01.000'), 3, 1, 0, 1, 1, N'تكاليف صيانة المحلات والمكاتب', 1, 1, 1, 3, 0, 0, GETDATE(), 1),
    
    ('8.02.000', N'المصروفات الإدارية', 'Administrative Expenses', @WaqfExpensesTypeId, @AdminExpensesGroupId,
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '8.00.000'), 2, 1, 0, 1, 1, N'مصروفات إدارة الأوقاف', 0, 0, 1, 2, 1, 1, GETDATE(), 1),
    
    ('8.02.001', N'رواتب إدارة الأوقاف', 'Waqf Management Salaries', @WaqfExpensesTypeId, @AdminExpensesGroupId,
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '8.02.000'), 3, 1, 0, 1, 1, N'رواتب موظفي إدارة الأوقاف', 1, 1, 1, 3, 0, 0, GETDATE(), 1),
    
    -- ===== المستفيدون =====
    ('9.00.000', N'المستفيدون من الأوقاف', 'Waqf Beneficiaries', @BeneficiariesTypeId, @PoorNeedyGroupId,
     NULL, 1, 2, 0, 2, 1, N'جميع المستفيدين من الأوقاف', 0, 0, 1, 1, 1, 1, GETDATE(), 1),
    
    ('9.01.000', N'الفقراء والمساكين', 'Poor and Needy', @BeneficiariesTypeId, @PoorNeedyGroupId,
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '9.00.000'), 2, 2, 0, 2, 1, N'مستحقات الفقراء والمساكين', 0, 0, 1, 2, 1, 1, GETDATE(), 1),
    
    ('9.01.001', N'مساعدات شهرية للفقراء', 'Monthly Aid for Poor', @BeneficiariesTypeId, @PoorNeedyGroupId,
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '9.01.000'), 3, 2, 0, 2, 1, N'المساعدات الشهرية للأسر الفقيرة', 1, 1, 1, 3, 0, 0, GETDATE(), 1),
    
    ('9.02.000', N'طلاب العلم', 'Students', @BeneficiariesTypeId, @StudentsGroupId,
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '9.00.000'), 2, 2, 0, 2, 1, N'مستحقات طلاب العلم', 0, 0, 1, 2, 1, 1, GETDATE(), 1),
    
    ('9.02.001', N'منح دراسية', 'Educational Scholarships', @BeneficiariesTypeId, @StudentsGroupId,
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '9.02.000'), 3, 2, 0, 2, 1, N'المنح الدراسية لطلاب العلم', 1, 1, 1, 3, 0, 0, GETDATE(), 1),
    
    -- ===== احتياطيات الأوقاف =====
    ('10.00.000', N'احتياطيات الأوقاف', 'Waqf Reserves', @WaqfReservesTypeId, @MaintenanceReserveGroupId,
     NULL, 1, 2, 0, 2, 1, N'جميع احتياطيات الأوقاف', 0, 0, 1, 1, 1, 1, GETDATE(), 1),
    
    ('10.01.000', N'احتياطي الصيانة', 'Maintenance Reserve', @WaqfReservesTypeId, @MaintenanceReserveGroupId,
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '10.00.000'), 2, 2, 0, 2, 1, N'احتياطي صيانة الأوقاف', 0, 0, 1, 2, 1, 1, GETDATE(), 1),
    
    ('10.01.001', N'احتياطي صيانة العقارات', 'Property Maintenance Reserve', @WaqfReservesTypeId, @MaintenanceReserveGroupId,
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '10.01.000'), 3, 2, 0, 2, 1, N'احتياطي صيانة العقارات الوقفية', 1, 1, 1, 3, 0, 0, GETDATE(), 1);
    
    PRINT N'✓ تم إضافة الحسابات المتخصصة للأوقاف بنجاح';
END

-- ===================================================================
-- عرض ملخص الحسابات المضافة
-- Display Summary of Added Accounts
-- ===================================================================

SELECT 
    'أنواع الحسابات الوقفية' as 'النوع',
    COUNT(*) as 'العدد'
FROM AccountTypes 
WHERE AccountTypeCode IN ('WAQF_ASSETS', 'WAQF_REVENUE', 'WAQF_EXPENSES', 'BENEFICIARIES', 'WAQF_RESERVES')

UNION ALL

SELECT 
    'مجموعات الحسابات الوقفية' as 'النوع',
    COUNT(*) as 'العدد'
FROM AccountGroups 
WHERE GroupCode IN ('WAQF_REAL_ESTATE', 'WAQF_INVESTMENTS', 'RENTAL_INCOME', 'INVESTMENT_INCOME', 
                    'DONATIONS', 'MAINTENANCE_COSTS', 'ADMIN_EXPENSES', 'POOR_NEEDY', 'STUDENTS', 
                    'MAINTENANCE_RESERVE', 'DEVELOPMENT_RESERVE', 'EMERGENCY_RESERVE')

UNION ALL

SELECT 
    'الحسابات الوقفية' as 'النوع',
    COUNT(*) as 'العدد'
FROM ChartOfAccounts 
WHERE AccountCode LIKE '6.%' OR AccountCode LIKE '7.%' OR AccountCode LIKE '8.%' 
   OR AccountCode LIKE '9.%' OR AccountCode LIKE '10.%';

-- عرض الحسابات الرئيسية للأوقاف
SELECT 
    AccountCode as 'رمز الحساب',
    AccountNameAr as 'اسم الحساب',
    CASE LevelType 
        WHEN 1 THEN N'رئيسي'
        WHEN 2 THEN N'فرعي'
        WHEN 3 THEN N'تفصيلي'
    END as 'المستوى',
    CASE Nature 
        WHEN 1 THEN N'مدين'
        WHEN 2 THEN N'دائن'
    END as 'الطبيعة'
FROM ChartOfAccounts 
WHERE AccountCode LIKE '6.%' OR AccountCode LIKE '7.%' OR AccountCode LIKE '8.%' 
   OR AccountCode LIKE '9.%' OR AccountCode LIKE '10.%'
ORDER BY AccountCode;

-- ===================================================================
-- إنشاء جدول ربط الحسابات بالعقارات
-- Create Account-Property Link Table
-- ===================================================================

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[AccountPropertyLink]') AND type in (N'U'))
BEGIN
    CREATE TABLE AccountPropertyLink (
        LinkId INT IDENTITY(1,1) PRIMARY KEY,
        AccountId INT NOT NULL,
        PropertyId INT NOT NULL,
        LinkType NVARCHAR(50) NOT NULL, -- REVENUE, EXPENSE, ASSET, RESERVE

        -- تفاصيل الربط
        LinkDescription NVARCHAR(500) NULL,
        PercentageShare DECIMAL(5,2) NULL, -- نسبة الحصة من العقار
        FixedAmount DECIMAL(18,2) NULL,    -- مبلغ ثابت

        -- معلومات التفعيل
        IsActive BIT NOT NULL DEFAULT 1,
        EffectiveDate DATE NOT NULL DEFAULT GETDATE(),
        ExpiryDate DATE NULL,

        -- معلومات النظام
        CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
        CreatedBy INT NULL,
        ModifiedDate DATETIME2 NULL,
        ModifiedBy INT NULL,

        -- المفاتيح الخارجية
        CONSTRAINT FK_AccountPropertyLink_Account
            FOREIGN KEY (AccountId) REFERENCES ChartOfAccounts(AccountId),

        CONSTRAINT FK_AccountPropertyLink_CreatedBy
            FOREIGN KEY (CreatedBy) REFERENCES Users(UserId),

        CONSTRAINT FK_AccountPropertyLink_ModifiedBy
            FOREIGN KEY (ModifiedBy) REFERENCES Users(UserId),

        -- قيود فريدة
        CONSTRAINT UQ_AccountPropertyLink_Account_Property_Type
            UNIQUE (AccountId, PropertyId, LinkType)
    );

    PRINT N'✓ تم إنشاء جدول ربط الحسابات بالعقارات';
END

-- إنشاء الفهارس
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_AccountPropertyLink_AccountId')
BEGIN
    CREATE INDEX IX_AccountPropertyLink_AccountId ON AccountPropertyLink(AccountId);
    PRINT N'✓ تم إنشاء فهرس معرف الحساب';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_AccountPropertyLink_PropertyId')
BEGIN
    CREATE INDEX IX_AccountPropertyLink_PropertyId ON AccountPropertyLink(PropertyId);
    PRINT N'✓ تم إنشاء فهرس معرف العقار';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_AccountPropertyLink_LinkType')
BEGIN
    CREATE INDEX IX_AccountPropertyLink_LinkType ON AccountPropertyLink(LinkType);
    PRINT N'✓ تم إنشاء فهرس نوع الربط';
END

-- ===================================================================
-- إنشاء جدول تصنيفات الأوقاف
-- Create Waqf Categories Table
-- ===================================================================

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[WaqfCategories]') AND type in (N'U'))
BEGIN
    CREATE TABLE WaqfCategories (
        CategoryId INT IDENTITY(1,1) PRIMARY KEY,
        CategoryCode NVARCHAR(20) NOT NULL UNIQUE,
        CategoryNameAr NVARCHAR(100) NOT NULL,
        CategoryNameEn NVARCHAR(100) NULL,

        -- تفاصيل التصنيف
        Description NVARCHAR(500) NULL,
        WaqfType NVARCHAR(50) NOT NULL, -- KHAYRI, AHLI, MUSHTARAK

        -- الحسابات المرتبطة
        DefaultAssetAccountId INT NULL,
        DefaultRevenueAccountId INT NULL,
        DefaultExpenseAccountId INT NULL,
        DefaultBeneficiaryAccountId INT NULL,

        -- معلومات النظام
        DisplayOrder INT NOT NULL DEFAULT 0,
        IsActive BIT NOT NULL DEFAULT 1,
        CreatedDate DATETIME2 NOT NULL DEFAULT GETDATE(),
        CreatedBy INT NULL,
        ModifiedDate DATETIME2 NULL,
        ModifiedBy INT NULL,

        -- المفاتيح الخارجية
        CONSTRAINT FK_WaqfCategories_DefaultAssetAccount
            FOREIGN KEY (DefaultAssetAccountId) REFERENCES ChartOfAccounts(AccountId),
        CONSTRAINT FK_WaqfCategories_DefaultRevenueAccount
            FOREIGN KEY (DefaultRevenueAccountId) REFERENCES ChartOfAccounts(AccountId),
        CONSTRAINT FK_WaqfCategories_DefaultExpenseAccount
            FOREIGN KEY (DefaultExpenseAccountId) REFERENCES ChartOfAccounts(AccountId),
        CONSTRAINT FK_WaqfCategories_DefaultBeneficiaryAccount
            FOREIGN KEY (DefaultBeneficiaryAccountId) REFERENCES ChartOfAccounts(AccountId)
    );

    PRINT N'✓ تم إنشاء جدول تصنيفات الأوقاف';
END

-- إدراج تصنيفات الأوقاف الأساسية
IF NOT EXISTS (SELECT 1 FROM WaqfCategories WHERE CategoryCode = 'RESIDENTIAL')
BEGIN
    INSERT INTO WaqfCategories (
        CategoryCode, CategoryNameAr, CategoryNameEn, Description, WaqfType,
        DefaultAssetAccountId, DefaultRevenueAccountId, DefaultExpenseAccountId,
        DisplayOrder, IsActive, CreatedBy
    ) VALUES
    ('RESIDENTIAL', N'العقارات السكنية', 'Residential Properties', N'الشقق والفلل والمنازل الوقفية', 'KHAYRI',
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '6.01.001'),
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '7.01.001'),
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '8.01.001'),
     1, 1, 1),

    ('COMMERCIAL', N'العقارات التجارية', 'Commercial Properties', N'المحلات والمكاتب والمراكز التجارية الوقفية', 'KHAYRI',
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '6.01.002'),
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '7.01.002'),
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '8.01.002'),
     2, 1, 1),

    ('AGRICULTURAL', N'الأراضي الزراعية', 'Agricultural Lands', N'الأراضي الزراعية والمزارع الوقفية', 'KHAYRI',
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '6.01.003'),
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '7.01.001'),
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '8.01.001'),
     3, 1, 1),

    ('INVESTMENT', N'الاستثمارات النقدية', 'Cash Investments', N'الأوقاف النقدية المستثمرة', 'KHAYRI',
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '6.02.001'),
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '7.02.001'),
     (SELECT AccountId FROM ChartOfAccounts WHERE AccountCode = '8.02.001'),
     4, 1, 1);

    PRINT N'✓ تم إدراج تصنيفات الأوقاف الأساسية';
END

-- ===================================================================
-- إنشاء Views للتقارير المحاسبية للأوقاف
-- Create Views for Waqf Accounting Reports
-- ===================================================================

-- View لملخص الأصول الوقفية
IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_WaqfAssetsummary')
    DROP VIEW vw_WaqfAssetsummary;
GO

CREATE VIEW vw_WaqfAssetsummary AS
SELECT
    ca.AccountId,
    ca.AccountCode,
    ca.AccountNameAr,
    ca.AccountNameEn,
    ag.GroupNameAr as GroupName,
    ca.OpeningBalance,
    ca.CurrentBalance,
    ca.IsActive,
    wc.CategoryNameAr as WaqfCategory
FROM ChartOfAccounts ca
INNER JOIN AccountGroups ag ON ca.AccountGroupId = ag.AccountGroupId
LEFT JOIN AccountPropertyLink apl ON ca.AccountId = apl.AccountId AND apl.LinkType = 'ASSET'
LEFT JOIN WaqfCategories wc ON apl.PropertyId = wc.CategoryId
WHERE ca.AccountCode LIKE '6.%' AND ca.IsActive = 1;
GO

-- View لملخص إيرادات الأوقاف
IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_WaqfRevenueSummary')
    DROP VIEW vw_WaqfRevenueSummary;
GO

CREATE VIEW vw_WaqfRevenueSummary AS
SELECT
    ca.AccountId,
    ca.AccountCode,
    ca.AccountNameAr,
    ca.AccountNameEn,
    ag.GroupNameAr as GroupName,
    ca.CurrentBalance as RevenueAmount,
    ca.IsActive,
    wc.CategoryNameAr as WaqfCategory
FROM ChartOfAccounts ca
INNER JOIN AccountGroups ag ON ca.AccountGroupId = ag.AccountGroupId
LEFT JOIN AccountPropertyLink apl ON ca.AccountId = apl.AccountId AND apl.LinkType = 'REVENUE'
LEFT JOIN WaqfCategories wc ON apl.PropertyId = wc.CategoryId
WHERE ca.AccountCode LIKE '7.%' AND ca.IsActive = 1;
GO

-- View لملخص مصروفات الأوقاف
IF EXISTS (SELECT * FROM sys.views WHERE name = 'vw_WaqfExpensesSummary')
    DROP VIEW vw_WaqfExpensesSummary;
GO

CREATE VIEW vw_WaqfExpensesSummary AS
SELECT
    ca.AccountId,
    ca.AccountCode,
    ca.AccountNameAr,
    ca.AccountNameEn,
    ag.GroupNameAr as GroupName,
    ca.CurrentBalance as ExpenseAmount,
    ca.IsActive,
    wc.CategoryNameAr as WaqfCategory
FROM ChartOfAccounts ca
INNER JOIN AccountGroups ag ON ca.AccountGroupId = ag.AccountGroupId
LEFT JOIN AccountPropertyLink apl ON ca.AccountId = apl.AccountId AND apl.LinkType = 'EXPENSE'
LEFT JOIN WaqfCategories wc ON apl.PropertyId = wc.CategoryId
WHERE ca.AccountCode LIKE '8.%' AND ca.IsActive = 1;
GO

PRINT N'✓ تم إنشاء Views التقارير المحاسبية للأوقاف';

PRINT N'=== تم إنشاء النظام المحاسبي المتخصص للأوقاف بنجاح ===';
GO
