# تشغيل سكريبت إدراج الحسابات الرئيسية والفرعية
# Run Main and Sub Accounts Insertion Script

Write-Host "========================================" -ForegroundColor Green
Write-Host "إدراج الحسابات الرئيسية والفرعية" -ForegroundColor Green
Write-Host "Insert Main and Sub Accounts" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

try {
    Write-Host "تشغيل سكريبت إدراج الحسابات..." -ForegroundColor Yellow
    Write-Host "Running accounts insertion script..." -ForegroundColor Yellow
    
    # تنفيذ السكريبت
    $result = sqlcmd -S NAJEEB -d AwqafManagement -i "Database\Scripts\InsertMainAccounts.sql" 2>&1
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "تم تنفيذ السكريبت بنجاح!" -ForegroundColor Green
        Write-Host "Script executed successfully!" -ForegroundColor Green
        
        # عرض النتائج
        if ($result) {
            Write-Host "نتائج التنفيذ:" -ForegroundColor Cyan
            Write-Host "Execution Results:" -ForegroundColor Cyan
            $result | ForEach-Object { Write-Host $_ -ForegroundColor White }
        }
        
        Write-Host ""
        Write-Host "اختبار البيانات المدرجة..." -ForegroundColor Yellow
        Write-Host "Testing inserted data..." -ForegroundColor Yellow
        
        # اختبار عدد الحسابات
        $count = sqlcmd -S NAJEEB -d AwqafManagement -Q "SELECT COUNT(*) FROM ChartOfAccounts" -h -1
        Write-Host "إجمالي الحسابات المدرجة: $count" -ForegroundColor Green
        Write-Host "Total accounts inserted: $count" -ForegroundColor Green
        
        # عرض الحسابات الرئيسية
        Write-Host ""
        Write-Host "الحسابات الرئيسية:" -ForegroundColor Cyan
        Write-Host "Main Accounts:" -ForegroundColor Cyan
        $mainAccounts = sqlcmd -S NAJEEB -d AwqafManagement -Q "SELECT AccountCode + ' - ' + AccountNameAr FROM ChartOfAccounts WHERE LevelType = 1 ORDER BY AccountCode" -h -1
        $mainAccounts | ForEach-Object { Write-Host "  $_" -ForegroundColor White }
        
    } else {
        Write-Host "حدث خطأ أثناء تنفيذ السكريبت!" -ForegroundColor Red
        Write-Host "Error occurred during script execution!" -ForegroundColor Red
        if ($result) {
            $result | ForEach-Object { Write-Host $_ -ForegroundColor Red }
        }
    }
    
} catch {
    Write-Host "خطأ: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "انتهى تنفيذ العملية" -ForegroundColor Green
Write-Host "Operation completed" -ForegroundColor Green
