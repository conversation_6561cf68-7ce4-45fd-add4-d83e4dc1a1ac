-- ===================================================================
-- اختبار سريع وإصلاح البيانات
-- Quick Test and Fix Data
-- ===================================================================

USE AwqafManagement;
GO

PRINT N'=== اختبار سريع للبيانات ===';
PRINT '=== Quick Data Test ===';
PRINT '';

-- ===================================================================
-- 1. فحص وجود الجداول
-- ===================================================================
PRINT N'1. فحص وجود الجداول:';

IF OBJECT_ID('AccountTypes', 'U') IS NOT NULL
    PRINT N'✓ جدول AccountTypes موجود'
ELSE
    PRINT N'❌ جدول AccountTypes غير موجود'

IF OBJECT_ID('AccountGroups', 'U') IS NOT NULL
    PRINT N'✓ جدول AccountGroups موجود'
ELSE
    PRINT N'❌ جدول AccountGroups غير موجود'

IF OBJECT_ID('ChartOfAccounts', 'U') IS NOT NULL
    PRINT N'✓ جدول ChartOfAccounts موجود'
ELSE
    PRINT N'❌ جدول ChartOfAccounts غير موجود'

PRINT '';

-- ===================================================================
-- 2. فحص البيانات الموجودة
-- ===================================================================
PRINT N'2. فحص البيانات الموجودة:';

DECLARE @TypesCount INT = (SELECT COUNT(*) FROM AccountTypes WHERE IsActive = 1);
DECLARE @GroupsCount INT = (SELECT COUNT(*) FROM AccountGroups WHERE IsActive = 1);
DECLARE @AccountsCount INT = (SELECT COUNT(*) FROM ChartOfAccounts WHERE IsActive = 1);

PRINT N'عدد أنواع الحسابات: ' + CAST(@TypesCount AS NVARCHAR(10));
PRINT N'عدد مجموعات الحسابات: ' + CAST(@GroupsCount AS NVARCHAR(10));
PRINT N'عدد الحسابات: ' + CAST(@AccountsCount AS NVARCHAR(10));

PRINT '';

-- ===================================================================
-- 3. إدراج البيانات إذا كانت فارغة
-- ===================================================================
IF @TypesCount = 0
BEGIN
    PRINT N'3. إدراج أنواع الحسابات...';
    
    INSERT INTO AccountTypes (AccountTypeCode, AccountTypeName, AccountTypeNameAr, Description, DisplayOrder, IsActive, CreatedDate)
    VALUES 
        ('AST', 'Assets', N'الأصول', N'جميع الأصول المملوكة للمؤسسة', 1, 1, GETDATE()),
        ('LIB', 'Liabilities', N'الخصوم', N'جميع الالتزامات والديون', 2, 1, GETDATE()),
        ('EQT', 'Equity', N'حقوق الملكية', N'حقوق أصحاب المؤسسة', 3, 1, GETDATE()),
        ('REV', 'Revenue', N'الإيرادات', N'جميع الإيرادات والدخل', 4, 1, GETDATE()),
        ('EXP', 'Expenses', N'المصروفات', N'جميع المصروفات والتكاليف', 5, 1, GETDATE());
    
    PRINT N'✓ تم إدراج أنواع الحسابات';
END

IF @GroupsCount = 0
BEGIN
    PRINT N'4. إدراج مجموعات الحسابات...';
    
    INSERT INTO AccountGroups (GroupCode, GroupName, GroupNameAr, AccountTypeId, Description, DisplayOrder, IsActive, CreatedDate)
    VALUES 
        ('CA', 'Current Assets', N'أصول متداولة', 1, N'الأصول التي يمكن تحويلها لنقد خلال سنة', 1, 1, GETDATE()),
        ('FA', 'Fixed Assets', N'أصول ثابتة', 1, N'الأصول طويلة الأجل', 2, 1, GETDATE()),
        ('CL', 'Current Liabilities', N'خصوم متداولة', 2, N'الالتزامات المستحقة خلال سنة', 1, 1, GETDATE()),
        ('LL', 'Long-term Liabilities', N'خصوم طويلة الأجل', 2, N'الالتزامات طويلة الأجل', 2, 1, GETDATE()),
        ('EQ', 'Equity', N'حقوق الملكية', 3, N'حقوق أصحاب المؤسسة', 1, 1, GETDATE()),
        ('OR', 'Operating Revenue', N'إيرادات التشغيل', 4, N'الإيرادات من الأنشطة الرئيسية', 1, 1, GETDATE()),
        ('OE', 'Operating Expenses', N'مصروفات التشغيل', 5, N'المصروفات التشغيلية', 1, 1, GETDATE());
    
    PRINT N'✓ تم إدراج مجموعات الحسابات';
END

IF @AccountsCount = 0
BEGIN
    PRINT N'5. إدراج الحسابات الأساسية...';
    
    -- الأصول الرئيسية
    INSERT INTO ChartOfAccounts (AccountCode, AccountName, AccountNameAr, AccountTypeId, AccountGroupId, ParentAccountId, AccountLevel, IsParent, IsActive, AllowPosting, CurrencyCode, OpeningBalance, Description, CreatedDate)
    VALUES 
        ('1.00.000', 'Assets', N'الأصول', 1, 1, NULL, 1, 1, 1, 0, 'SAR', 0, N'جميع أصول المؤسسة', GETDATE()),
        ('1.01.000', 'Current Assets', N'الأصول المتداولة', 1, 1, 1, 2, 1, 1, 0, 'SAR', 0, N'الأصول قصيرة الأجل', GETDATE()),
        ('1.01.001', 'Cash in Hand', N'النقدية في الصندوق', 1, 1, 2, 3, 0, 1, 1, 'SAR', 50000, N'النقد المتوفر في الصندوق', GETDATE()),
        ('1.01.002', 'Bank - Current Account', N'البنك - الحساب الجاري', 1, 1, 2, 3, 0, 1, 1, 'SAR', 250000, N'الحساب الجاري في البنك', GETDATE()),
        ('1.01.003', 'Accounts Receivable', N'العملاء والمدينون', 1, 1, 2, 3, 0, 1, 1, 'SAR', 75000, N'المبالغ المستحقة من العملاء', GETDATE()),
        
        -- الخصوم
        ('2.00.000', 'Liabilities', N'الخصوم', 2, 3, NULL, 1, 1, 1, 0, 'SAR', 0, N'جميع التزامات المؤسسة', GETDATE()),
        ('2.01.000', 'Current Liabilities', N'الخصوم المتداولة', 2, 3, 6, 2, 1, 1, 0, 'SAR', 0, N'الالتزامات قصيرة الأجل', GETDATE()),
        ('2.01.001', 'Accounts Payable', N'الموردون والدائنون', 2, 3, 7, 3, 0, 1, 1, 'SAR', 45000, N'المبالغ المستحقة للموردين', GETDATE()),
        
        -- حقوق الملكية
        ('3.00.000', 'Equity', N'حقوق الملكية', 3, 5, NULL, 1, 1, 1, 0, 'SAR', 0, N'حقوق أصحاب المؤسسة', GETDATE()),
        ('3.01.001', 'Paid-up Capital', N'رأس المال المدفوع', 3, 5, 9, 2, 0, 1, 1, 'SAR', 1000000, N'رأس المال المدفوع فعلياً', GETDATE()),
        
        -- الإيرادات
        ('4.00.000', 'Revenue', N'الإيرادات', 4, 6, NULL, 1, 1, 1, 0, 'SAR', 0, N'جميع إيرادات المؤسسة', GETDATE()),
        ('4.01.001', 'Sales Revenue', N'إيرادات المبيعات', 4, 6, 11, 2, 0, 1, 1, 'SAR', 0, N'إيرادات من بيع البضائع', GETDATE()),
        
        -- المصروفات
        ('5.00.000', 'Expenses', N'المصروفات', 5, 7, NULL, 1, 1, 1, 0, 'SAR', 0, N'جميع مصروفات المؤسسة', GETDATE()),
        ('5.01.001', 'Salaries and Wages', N'رواتب وأجور', 5, 7, 13, 2, 0, 1, 1, 'SAR', 0, N'رواتب وأجور الموظفين', GETDATE());
    
    PRINT N'✓ تم إدراج الحسابات الأساسية';
END

PRINT '';

-- ===================================================================
-- 4. عرض النتائج النهائية
-- ===================================================================
PRINT N'=== النتائج النهائية ===';

SELECT 
    COUNT(*) as 'عدد أنواع الحسابات',
    (SELECT COUNT(*) FROM AccountGroups WHERE IsActive = 1) as 'عدد مجموعات الحسابات',
    (SELECT COUNT(*) FROM ChartOfAccounts WHERE IsActive = 1) as 'عدد الحسابات'
FROM AccountTypes 
WHERE IsActive = 1;

PRINT '';
PRINT N'=== عرض الحسابات الرئيسية ===';

SELECT 
    AccountCode as 'رمز الحساب',
    AccountNameAr as 'اسم الحساب',
    AccountLevel as 'المستوى',
    CASE WHEN IsParent = 1 THEN N'نعم' ELSE N'لا' END as 'حساب أب',
    OpeningBalance as 'الرصيد الافتتاحي'
FROM ChartOfAccounts 
WHERE IsActive = 1 
ORDER BY AccountCode;

PRINT '';
PRINT N'=== انتهى الاختبار ===';
PRINT '=== Test Complete ===';
