using System;
using System.Windows.Forms;
using Awqaf_Managment.UI.Forms.Accounting;

namespace Awqaf_Managment.Test
{
    /// <summary>
    /// اختبار النموذج مباشرة
    /// Direct Form Test
    /// </summary>
    public static class TestFormProgram
    {
        [STAThread]
        public static void Main()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                Console.WriteLine("Starting form test...");
                
                // إنشاء النموذج
                var form = new AddEditAccountForm();
                
                // تشغيل النموذج
                Application.Run(form);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تشغيل النموذج: {ex.Message}\n\nتفاصيل: {ex.StackTrace}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
