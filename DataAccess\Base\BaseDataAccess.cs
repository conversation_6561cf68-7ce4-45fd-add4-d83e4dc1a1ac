using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;

namespace Awqaf_Managment.DataAccess.Base
{
    /// <summary>
    /// الفئة الأساسية للوصول للبيانات
    /// Base Data Access Class
    /// </summary>
    public abstract class BaseDataAccess
    {
        #region Connection Management

        /// <summary>
        /// الحصول على اتصال قاعدة البيانات
        /// Get Database Connection
        /// </summary>
        /// <returns>اتصال قاعدة البيانات</returns>
        protected SqlConnection GetConnection()
        {
            try
            {
                string connectionString = GetConnectionString();
                return new SqlConnection(connectionString);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الاتصال بقاعدة البيانات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على نص الاتصال
        /// Get Connection String
        /// </summary>
        /// <returns>نص الاتصال</returns>
        protected virtual string GetConnectionString()
        {
            try
            {
                // محاولة الحصول على نص الاتصال من ملف التكوين
                string connectionString = ConfigurationManager.ConnectionStrings["DefaultConnection"]?.ConnectionString;
                
                if (string.IsNullOrEmpty(connectionString))
                {
                    // استخدام نص اتصال افتراضي
                    connectionString = "Server=NAJEEB;Database=AwqafManagement;Integrated Security=true;Charset=UTF8;";
                }
                
                return connectionString;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على نص الاتصال: {ex.Message}", ex);
            }
        }

        #endregion

        #region Command Execution Helpers

        /// <summary>
        /// تنفيذ استعلام وإرجاع عدد الصفوف المتأثرة
        /// Execute Non-Query and Return Affected Rows Count
        /// </summary>
        /// <param name="query">الاستعلام</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>عدد الصفوف المتأثرة</returns>
        protected int ExecuteNonQuery(string query, params SqlParameter[] parameters)
        {
            try
            {
                using (var connection = GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        if (parameters != null)
                        {
                            command.Parameters.AddRange(parameters);
                        }
                        return command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تنفيذ الاستعلام: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تنفيذ استعلام وإرجاع قيمة واحدة
        /// Execute Scalar and Return Single Value
        /// </summary>
        /// <param name="query">الاستعلام</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>القيمة المرجعة</returns>
        protected object ExecuteScalar(string query, params SqlParameter[] parameters)
        {
            try
            {
                using (var connection = GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        if (parameters != null)
                        {
                            command.Parameters.AddRange(parameters);
                        }
                        return command.ExecuteScalar();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تنفيذ الاستعلام: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تنفيذ استعلام وإرجاع DataTable
        /// Execute Query and Return DataTable
        /// </summary>
        /// <param name="query">الاستعلام</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>جدول البيانات</returns>
        protected DataTable ExecuteQuery(string query, params SqlParameter[] parameters)
        {
            try
            {
                using (var connection = GetConnection())
                {
                    connection.Open();
                    using (var command = new SqlCommand(query, connection))
                    {
                        if (parameters != null)
                        {
                            command.Parameters.AddRange(parameters);
                        }
                        
                        using (var adapter = new SqlDataAdapter(command))
                        {
                            var dataTable = new DataTable();
                            adapter.Fill(dataTable);
                            return dataTable;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تنفيذ الاستعلام: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تنفيذ استعلام وإرجاع SqlDataReader
        /// Execute Query and Return SqlDataReader
        /// </summary>
        /// <param name="query">الاستعلام</param>
        /// <param name="connection">الاتصال</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>قارئ البيانات</returns>
        protected SqlDataReader ExecuteReader(string query, SqlConnection connection, params SqlParameter[] parameters)
        {
            try
            {
                var command = new SqlCommand(query, connection);
                if (parameters != null)
                {
                    command.Parameters.AddRange(parameters);
                }
                return command.ExecuteReader();
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تنفيذ الاستعلام: {ex.Message}", ex);
            }
        }

        #endregion

        #region Parameter Helpers

        /// <summary>
        /// إنشاء معامل SQL
        /// Create SQL Parameter
        /// </summary>
        /// <param name="name">اسم المعامل</param>
        /// <param name="value">قيمة المعامل</param>
        /// <returns>معامل SQL</returns>
        protected SqlParameter CreateParameter(string name, object value)
        {
            return new SqlParameter(name, value ?? DBNull.Value);
        }

        /// <summary>
        /// إنشاء معامل SQL مع نوع البيانات
        /// Create SQL Parameter with Data Type
        /// </summary>
        /// <param name="name">اسم المعامل</param>
        /// <param name="value">قيمة المعامل</param>
        /// <param name="dbType">نوع البيانات</param>
        /// <returns>معامل SQL</returns>
        protected SqlParameter CreateParameter(string name, object value, SqlDbType dbType)
        {
            return new SqlParameter(name, dbType) { Value = value ?? DBNull.Value };
        }

        /// <summary>
        /// إنشاء معامل SQL مع نوع البيانات والحجم
        /// Create SQL Parameter with Data Type and Size
        /// </summary>
        /// <param name="name">اسم المعامل</param>
        /// <param name="value">قيمة المعامل</param>
        /// <param name="dbType">نوع البيانات</param>
        /// <param name="size">الحجم</param>
        /// <returns>معامل SQL</returns>
        protected SqlParameter CreateParameter(string name, object value, SqlDbType dbType, int size)
        {
            return new SqlParameter(name, dbType, size) { Value = value ?? DBNull.Value };
        }

        #endregion

        #region Validation Helpers

        /// <summary>
        /// التحقق من وجود قيمة في قاعدة البيانات
        /// Check if value exists in database
        /// </summary>
        /// <param name="query">الاستعلام</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>true إذا كانت القيمة موجودة</returns>
        protected bool Exists(string query, params SqlParameter[] parameters)
        {
            try
            {
                var result = ExecuteScalar(query, parameters);
                return result != null && Convert.ToInt32(result) > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في التحقق من وجود البيانات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على العدد من قاعدة البيانات
        /// Get count from database
        /// </summary>
        /// <param name="query">الاستعلام</param>
        /// <param name="parameters">المعاملات</param>
        /// <returns>العدد</returns>
        protected int GetCount(string query, params SqlParameter[] parameters)
        {
            try
            {
                var result = ExecuteScalar(query, parameters);
                return result != null ? Convert.ToInt32(result) : 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على العدد: {ex.Message}", ex);
            }
        }

        #endregion

        #region Transaction Helpers

        /// <summary>
        /// تنفيذ عملية ضمن معاملة
        /// Execute operation within transaction
        /// </summary>
        /// <param name="operation">العملية المراد تنفيذها</param>
        protected void ExecuteInTransaction(Action<SqlConnection, SqlTransaction> operation)
        {
            using (var connection = GetConnection())
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        operation(connection, transaction);
                        transaction.Commit();
                    }
                    catch
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }

        /// <summary>
        /// تنفيذ عملية ضمن معاملة مع إرجاع قيمة
        /// Execute operation within transaction with return value
        /// </summary>
        /// <typeparam name="T">نوع القيمة المرجعة</typeparam>
        /// <param name="operation">العملية المراد تنفيذها</param>
        /// <returns>القيمة المرجعة</returns>
        protected T ExecuteInTransaction<T>(Func<SqlConnection, SqlTransaction, T> operation)
        {
            using (var connection = GetConnection())
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        var result = operation(connection, transaction);
                        transaction.Commit();
                        return result;
                    }
                    catch
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }

        #endregion
    }
}
