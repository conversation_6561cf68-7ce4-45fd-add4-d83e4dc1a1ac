using System;
using System.Collections.Generic;
using System.Linq;
using Awqaf_Managment.Models.Accounting;

namespace Awqaf_Managment.Services.Accounting
{
    /// <summary>
    /// خدمة مراكز التكلفة
    /// Cost Center Service
    /// </summary>
    public class CostCenterService
    {
        #region Public Methods

        /// <summary>
        /// الحصول على جميع مراكز التكلفة
        /// Get All Cost Centers
        /// </summary>
        /// <returns>قائمة مراكز التكلفة</returns>
        public List<CostCenter> GetAllCostCenters()
        {
            try
            {
                // في الوقت الحالي، سنرجع بيانات تجريبية
                // حتى يتم إنشاء طبقة الوصول للبيانات
                return GetSampleCostCenters();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CostCenterService: خطأ في تحميل مراكز التكلفة: {ex.Message}");
                return new List<CostCenter>();
            }
        }

        /// <summary>
        /// الحصول على مركز تكلفة بالمعرف
        /// Get Cost Center By ID
        /// </summary>
        /// <param name="costCenterId">معرف مركز التكلفة</param>
        /// <returns>مركز التكلفة</returns>
        public CostCenter GetCostCenterById(int costCenterId)
        {
            var costCenters = GetAllCostCenters();
            return costCenters.FirstOrDefault(cc => cc.CostCenterId == costCenterId);
        }

        /// <summary>
        /// الحصول على مراكز التكلفة النشطة
        /// Get Active Cost Centers
        /// </summary>
        /// <returns>قائمة مراكز التكلفة النشطة</returns>
        public List<CostCenter> GetActiveCostCenters()
        {
            return GetAllCostCenters().Where(cc => cc.IsActive).ToList();
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// الحصول على بيانات تجريبية لمراكز التكلفة
        /// Get Sample Cost Centers Data
        /// </summary>
        /// <returns>قائمة مراكز التكلفة التجريبية</returns>
        private List<CostCenter> GetSampleCostCenters()
        {
            return new List<CostCenter>
            {
                new CostCenter
                {
                    CostCenterId = 1,
                    CostCenterCode = "CC001",
                    CostCenterNameAr = "الإدارة العامة",
                    CostCenterNameEn = "General Administration",
                    Description = "مركز تكلفة الإدارة العامة",
                    IsActive = true,
                    CreatedDate = DateTime.Now,
                    CreatedBy = 1
                },
                new CostCenter
                {
                    CostCenterId = 2,
                    CostCenterCode = "CC002",
                    CostCenterNameAr = "إدارة الأوقاف",
                    CostCenterNameEn = "Waqf Management",
                    Description = "مركز تكلفة إدارة الأوقاف",
                    IsActive = true,
                    CreatedDate = DateTime.Now,
                    CreatedBy = 1
                },
                new CostCenter
                {
                    CostCenterId = 3,
                    CostCenterCode = "CC003",
                    CostCenterNameAr = "الشؤون المالية",
                    CostCenterNameEn = "Financial Affairs",
                    Description = "مركز تكلفة الشؤون المالية",
                    IsActive = true,
                    CreatedDate = DateTime.Now,
                    CreatedBy = 1
                },
                new CostCenter
                {
                    CostCenterId = 4,
                    CostCenterCode = "CC004",
                    CostCenterNameAr = "الموارد البشرية",
                    CostCenterNameEn = "Human Resources",
                    Description = "مركز تكلفة الموارد البشرية",
                    IsActive = true,
                    CreatedDate = DateTime.Now,
                    CreatedBy = 1
                },
                new CostCenter
                {
                    CostCenterId = 5,
                    CostCenterCode = "CC005",
                    CostCenterNameAr = "تقنية المعلومات",
                    CostCenterNameEn = "Information Technology",
                    Description = "مركز تكلفة تقنية المعلومات",
                    IsActive = true,
                    CreatedDate = DateTime.Now,
                    CreatedBy = 1
                },
                new CostCenter
                {
                    CostCenterId = 6,
                    CostCenterCode = "CC006",
                    CostCenterNameAr = "الصيانة والخدمات",
                    CostCenterNameEn = "Maintenance & Services",
                    Description = "مركز تكلفة الصيانة والخدمات",
                    IsActive = true,
                    CreatedDate = DateTime.Now,
                    CreatedBy = 1
                },
                new CostCenter
                {
                    CostCenterId = 7,
                    CostCenterCode = "CC007",
                    CostCenterNameAr = "التسويق والعلاقات العامة",
                    CostCenterNameEn = "Marketing & Public Relations",
                    Description = "مركز تكلفة التسويق والعلاقات العامة",
                    IsActive = true,
                    CreatedDate = DateTime.Now,
                    CreatedBy = 1
                },
                new CostCenter
                {
                    CostCenterId = 8,
                    CostCenterCode = "CC008",
                    CostCenterNameAr = "الشؤون القانونية",
                    CostCenterNameEn = "Legal Affairs",
                    Description = "مركز تكلفة الشؤون القانونية",
                    IsActive = true,
                    CreatedDate = DateTime.Now,
                    CreatedBy = 1
                },
                new CostCenter
                {
                    CostCenterId = 9,
                    CostCenterCode = "CC009",
                    CostCenterNameAr = "المشاريع الاستثمارية",
                    CostCenterNameEn = "Investment Projects",
                    Description = "مركز تكلفة المشاريع الاستثمارية",
                    IsActive = true,
                    CreatedDate = DateTime.Now,
                    CreatedBy = 1
                },
                new CostCenter
                {
                    CostCenterId = 10,
                    CostCenterCode = "CC010",
                    CostCenterNameAr = "الأنشطة الخيرية",
                    CostCenterNameEn = "Charitable Activities",
                    Description = "مركز تكلفة الأنشطة الخيرية",
                    IsActive = true,
                    CreatedDate = DateTime.Now,
                    CreatedBy = 1
                }
            };
        }

        #endregion
    }
}
