@echo off
echo ========================================
echo    تنظيف وإعادة بناء المشروع
echo    Clean and Rebuild Project
echo ========================================
echo.

echo تنظيف الملفات المؤقتة...
echo Cleaning temporary files...

REM حذف مجلد bin
if exist "bin" (
    echo حذف مجلد bin...
    rmdir /s /q "bin"
)

REM حذف مجلد obj
if exist "obj" (
    echo حذف مجلد obj...
    rmdir /s /q "obj"
)

REM إنشاء مجلد bin\Debug
echo إنشاء مجلدات البناء...
mkdir "bin\Debug" 2>nul

echo.
echo ========================================
echo البحث عن أدوات البناء...
echo Looking for build tools...

REM Try to find Visual Studio Build Tools
set "VSWHERE=%ProgramFiles(x86)%\Microsoft Visual Studio\Installer\vswhere.exe"
if exist "%VSWHERE%" (
    for /f "usebackq tokens=*" %%i in (`"%VSWHERE%" -latest -products * -requires Microsoft.Component.MSBuild -property installationPath`) do (
        set "VSINSTALLDIR=%%i"
    )
)

REM Try MSBuild from Visual Studio
if defined VSINSTALLDIR (
    set "MSBUILD=%VSINSTALLDIR%\MSBuild\Current\Bin\MSBuild.exe"
    if exist "%MSBUILD%" (
        echo ✓ استخدام Visual Studio MSBuild...
        echo ✓ Using Visual Studio MSBuild...
        "%MSBUILD%" Awqaf_Managment.csproj /p:Configuration=Debug /p:Platform="Any CPU" /t:Rebuild
        goto :success
    )
)

REM Try .NET Framework MSBuild
set "MSBUILD=%ProgramFiles(x86)%\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\MSBuild.exe"
if exist "%MSBUILD%" (
    echo ✓ استخدام .NET Framework MSBuild 2019...
    echo ✓ Using .NET Framework MSBuild 2019...
    "%MSBUILD%" Awqaf_Managment.csproj /p:Configuration=Debug /p:Platform="Any CPU" /t:Rebuild
    goto :success
)

REM Try .NET Framework MSBuild 2017
set "MSBUILD=%ProgramFiles(x86)%\Microsoft Visual Studio\2017\BuildTools\MSBuild\15.0\Bin\MSBuild.exe"
if exist "%MSBUILD%" (
    echo ✓ استخدام .NET Framework MSBuild 2017...
    echo ✓ Using .NET Framework MSBuild 2017...
    "%MSBUILD%" Awqaf_Managment.csproj /p:Configuration=Debug /p:Platform="Any CPU" /t:Rebuild
    goto :success
)

echo ❌ لم يتم العثور على أدوات البناء
echo ❌ MSBuild not found
echo يرجى إغلاق Visual Studio وإعادة فتح المشروع
echo Please close Visual Studio and reopen the project
goto :end

:success
echo.
echo ✅ تم إعادة البناء بنجاح!
echo ✅ Rebuild completed successfully!
echo.
echo تشغيل التطبيق...
echo Starting application...
start "" "bin\Debug\Awqaf_Managment.exe"
echo.
echo ========================================
echo بيانات تسجيل الدخول:
echo Login Credentials:
echo Username: admin
echo Password: admin123
echo ========================================

:end
echo.
echo اضغط أي مفتاح للإغلاق...
echo Press any key to close...
pause >nul
