-- فحص هيكل جداول القيود اليومية
-- Check Journal Entry Tables Structure
USE AwqafManagement
GO

PRINT 'فحص هيكل جدول JournalEntries...'
SELECT 
    c.name AS ColumnName,
    t.name AS DataType,
    c.max_length,
    c.is_nullable,
    c.is_identity
FROM sys.columns c
INNER JOIN sys.types t ON c.system_type_id = t.system_type_id
WHERE c.object_id = OBJECT_ID('JournalEntries')
ORDER BY c.column_id

PRINT 'فحص هيكل جدول JournalEntryDetails...'
SELECT 
    c.name AS ColumnName,
    t.name AS DataType,
    c.max_length,
    c.is_nullable,
    c.is_identity
FROM sys.columns c
INNER JOIN sys.types t ON c.system_type_id = t.system_type_id
WHERE c.object_id = OBJECT_ID('JournalEntryDetails')
ORDER BY c.column_id

GO
