-- إنشاء جداول القيود اليومية
-- Journal Entry Tables Creation Script

USE [AwqafManagement]
GO

-- جدول القيود اليومية الرئيسي
-- Main Journal Entries Table
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[JournalEntries]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[JournalEntries](
        [JournalEntryId] [int] IDENTITY(1,1) NOT NULL,
        [JournalNumber] [nvarchar](50) NOT NULL,
        [JournalDate] [datetime] NOT NULL,
        [JournalType] [int] NOT NULL DEFAULT(1),
        [Status] [int] NOT NULL DEFAULT(1),
        [Description] [nvarchar](500) NULL,
        [Reference] [nvarchar](100) NULL,
        [TotalDebit] [decimal](18, 2) NOT NULL DEFAULT(0),
        [TotalCredit] [decimal](18, 2) NOT NULL DEFAULT(0),
        [CreatedBy] [nvarchar](50) NOT NULL,
        [CreatedDate] [datetime] NOT NULL DEFAULT(GETDATE()),
        [ModifiedBy] [nvarchar](50) NULL,
        [ModifiedDate] [datetime] NULL,
        [ApprovedBy] [nvarchar](50) NULL,
        [ApprovedDate] [datetime] NULL,
        [PostedBy] [nvarchar](50) NULL,
        [PostedDate] [datetime] NULL,
        [Notes] [nvarchar](1000) NULL,
        CONSTRAINT [PK_JournalEntries] PRIMARY KEY CLUSTERED ([JournalEntryId] ASC),
        CONSTRAINT [UK_JournalEntries_JournalNumber] UNIQUE NONCLUSTERED ([JournalNumber] ASC)
    )
END
GO

-- جدول تفاصيل القيود اليومية
-- Journal Entry Details Table
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[JournalEntryDetails]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[JournalEntryDetails](
        [JournalEntryDetailId] [int] IDENTITY(1,1) NOT NULL,
        [JournalEntryId] [int] NOT NULL,
        [AccountId] [int] NOT NULL,
        [LineNumber] [int] NOT NULL,
        [DebitAmount] [decimal](18, 2) NOT NULL DEFAULT(0),
        [CreditAmount] [decimal](18, 2) NOT NULL DEFAULT(0),
        [Description] [nvarchar](500) NULL,
        [Reference] [nvarchar](100) NULL,
        [CostCenterId] [int] NULL,
        [CreatedDate] [datetime] NOT NULL DEFAULT(GETDATE()),
        [CreatedBy] [nvarchar](50) NOT NULL,
        CONSTRAINT [PK_JournalEntryDetails] PRIMARY KEY CLUSTERED ([JournalEntryDetailId] ASC),
        CONSTRAINT [FK_JournalEntryDetails_JournalEntries] FOREIGN KEY([JournalEntryId]) 
            REFERENCES [dbo].[JournalEntries] ([JournalEntryId]) ON DELETE CASCADE,
        CONSTRAINT [FK_JournalEntryDetails_ChartOfAccounts] FOREIGN KEY([AccountId]) 
            REFERENCES [dbo].[ChartOfAccounts] ([AccountId]),
        CONSTRAINT [FK_JournalEntryDetails_CostCenters] FOREIGN KEY([CostCenterId]) 
            REFERENCES [dbo].[CostCenters] ([CostCenterId])
    )
END
GO

-- إنشاء الفهارس
-- Create Indexes
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[JournalEntries]') AND name = N'IX_JournalEntries_JournalDate')
    CREATE NONCLUSTERED INDEX [IX_JournalEntries_JournalDate] ON [dbo].[JournalEntries] ([JournalDate] DESC)
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[JournalEntries]') AND name = N'IX_JournalEntries_Status')
    CREATE NONCLUSTERED INDEX [IX_JournalEntries_Status] ON [dbo].[JournalEntries] ([Status])
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[JournalEntryDetails]') AND name = N'IX_JournalEntryDetails_JournalEntryId')
    CREATE NONCLUSTERED INDEX [IX_JournalEntryDetails_JournalEntryId] ON [dbo].[JournalEntryDetails] ([JournalEntryId])
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[JournalEntryDetails]') AND name = N'IX_JournalEntryDetails_AccountId')
    CREATE NONCLUSTERED INDEX [IX_JournalEntryDetails_AccountId] ON [dbo].[JournalEntryDetails] ([AccountId])
GO

-- إضافة قيود التحقق
-- Add Check Constraints
IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE object_id = OBJECT_ID(N'[dbo].[CK_JournalEntries_TotalDebit]'))
    ALTER TABLE [dbo].[JournalEntries] ADD CONSTRAINT [CK_JournalEntries_TotalDebit] CHECK ([TotalDebit] >= 0)
GO

IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE object_id = OBJECT_ID(N'[dbo].[CK_JournalEntries_TotalCredit]'))
    ALTER TABLE [dbo].[JournalEntries] ADD CONSTRAINT [CK_JournalEntries_TotalCredit] CHECK ([TotalCredit] >= 0)
GO

IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE object_id = OBJECT_ID(N'[dbo].[CK_JournalEntryDetails_DebitAmount]'))
    ALTER TABLE [dbo].[JournalEntryDetails] ADD CONSTRAINT [CK_JournalEntryDetails_DebitAmount] CHECK ([DebitAmount] >= 0)
GO

IF NOT EXISTS (SELECT * FROM sys.check_constraints WHERE object_id = OBJECT_ID(N'[dbo].[CK_JournalEntryDetails_CreditAmount]'))
    ALTER TABLE [dbo].[JournalEntryDetails] ADD CONSTRAINT [CK_JournalEntryDetails_CreditAmount] CHECK ([CreditAmount] >= 0)
GO

-- إضافة بيانات تجريبية
-- Insert Sample Data
IF NOT EXISTS (SELECT * FROM JournalEntries WHERE JournalNumber = '1')
BEGIN
    INSERT INTO JournalEntries (JournalNumber, JournalDate, JournalType, Status, Description, Reference, TotalDebit, TotalCredit, CreatedBy, CreatedDate, Notes)
    VALUES ('1', GETDATE(), 1, 1, N'قيد افتتاحي للأرصدة', N'REF-001', 50000.00, 50000.00, '1', GETDATE(), N'قيد افتتاحي لبداية السنة المالية')

    DECLARE @JournalEntryId INT = SCOPE_IDENTITY()

    -- تفاصيل القيد الافتتاحي
    INSERT INTO JournalEntryDetails (JournalEntryId, AccountId, LineNumber, DebitAmount, CreditAmount, Description, Reference, CreatedDate, CreatedBy)
    VALUES 
    (@JournalEntryId, 1, 1, 30000.00, 0.00, N'رصيد النقدية في الصندوق', N'REF-001-1', GETDATE(), '1'),
    (@JournalEntryId, 2, 2, 20000.00, 0.00, N'رصيد البنك', N'REF-001-2', GETDATE(), '1'),
    (@JournalEntryId, 3, 3, 0.00, 50000.00, N'رأس المال', N'REF-001-3', GETDATE(), '1')
END
GO

PRINT 'تم إنشاء جداول القيود اليومية بنجاح'
PRINT 'Journal Entry tables created successfully'
