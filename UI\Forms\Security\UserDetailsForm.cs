using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using Awqaf_Managment.Common.Helpers;
using Awqaf_Managment.DataAccess.Security;
using Awqaf_Managment.Models.Security;

namespace Awqaf_Managment.UI.Forms.Security
{
    public partial class UserDetailsForm : Form
    {
        private User _currentUser;
        private List<Role> _userRoles;

        public UserDetailsForm(User user)
        {
            InitializeComponent();
            _currentUser = user;
            InitializeForm();
            LoadUserData();
        }

        private void InitializeForm()
        {
            // تطبيق دعم RTL
            UIHelper.ApplyRTLSupport(this);
            
            // تطبيق الخط العربي
            UIHelper.ApplyArabicFont(this);
            
            // إعداد أحداث الأزرار
            btnEdit.Click += BtnEdit_Click;
            btnClose.Click += BtnClose_Click;
            
            // إعداد DataGridView
            SetupLoginHistoryGrid();
        }

        private void SetupLoginHistoryGrid()
        {
            dgvLoginHistory.AutoGenerateColumns = false;
            dgvLoginHistory.Columns.Clear();

            // إضافة الأعمدة
            dgvLoginHistory.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "LoginDate",
                HeaderText = "تاريخ الدخول",
                DataPropertyName = "LoginDate",
                Width = 150,
                DefaultCellStyle = new DataGridViewCellStyle { Format = "dd/MM/yyyy HH:mm" }
            });

            dgvLoginHistory.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "IPAddress",
                HeaderText = "عنوان IP",
                DataPropertyName = "IPAddress",
                Width = 120
            });

            dgvLoginHistory.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "UserAgent",
                HeaderText = "المتصفح/النظام",
                DataPropertyName = "UserAgent",
                AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill
            });

            dgvLoginHistory.Columns.Add(new DataGridViewTextBoxColumn
            {
                Name = "IsSuccessful",
                HeaderText = "النتيجة",
                DataPropertyName = "IsSuccessful",
                Width = 80
            });

            // تخصيص مظهر الجدول
            dgvLoginHistory.EnableHeadersVisualStyles = false;
            dgvLoginHistory.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(33, 150, 243);
            dgvLoginHistory.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dgvLoginHistory.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 11F, FontStyle.Bold);
            dgvLoginHistory.ColumnHeadersHeight = 35;

            dgvLoginHistory.DefaultCellStyle.Font = new Font("Tahoma", 10F);
            dgvLoginHistory.DefaultCellStyle.SelectionBackColor = Color.FromArgb(33, 150, 243, 50);
            dgvLoginHistory.DefaultCellStyle.SelectionForeColor = Color.Black;
            dgvLoginHistory.RowTemplate.Height = 30;
        }

        private void LoadUserData()
        {
            try
            {
                // تحميل بيانات المستخدم الأساسية
                LoadBasicUserInfo();
                
                // تحميل أدوار المستخدم
                LoadUserRoles();
                
                // تحميل سجل تسجيل الدخول
                LoadLoginHistory();
            }
            catch (Exception ex)
            {
                UIHelper.ShowError($"خطأ في تحميل بيانات المستخدم: {ex.Message}");
            }
        }

        private void LoadBasicUserInfo()
        {
            lblUsernameValue.Text = _currentUser.Username;
            lblFullNameValue.Text = _currentUser.FullName;
            lblEmailValue.Text = _currentUser.Email;
            
            // تحديد حالة المستخدم
            string status = "";
            Color statusColor = Color.Black;
            
            if (!_currentUser.IsActive)
            {
                status = "غير نشط";
                statusColor = Color.Red;
            }
            else if (_currentUser.IsLocked)
            {
                status = "مقفل";
                statusColor = Color.Orange;
            }
            else
            {
                status = "نشط";
                statusColor = Color.Green;
            }
            
            lblStatusValue.Text = status;
            lblStatusValue.ForeColor = statusColor;
            lblStatusValue.Font = new Font(lblStatusValue.Font, FontStyle.Bold);
            
            // تاريخ الإنشاء
            lblCreatedDateValue.Text = _currentUser.CreatedDate.ToString("dd/MM/yyyy HH:mm");
            
            // آخر تسجيل دخول
            if (_currentUser.LastLoginDate.HasValue)
            {
                lblLastLoginValue.Text = _currentUser.LastLoginDate.Value.ToString("dd/MM/yyyy HH:mm");
            }
            else
            {
                lblLastLoginValue.Text = "لم يسجل دخول من قبل";
                lblLastLoginValue.ForeColor = Color.Gray;
            }
        }

        private void LoadUserRoles()
        {
            try
            {
                _userRoles = RoleDataAccess.GetUserRoles(_currentUser.UserId);
                lstRoles.Items.Clear();
                
                if (_userRoles.Count > 0)
                {
                    foreach (var role in _userRoles)
                    {
                        lstRoles.Items.Add($"• {role.RoleNameAr} ({role.RoleName})");
                    }
                }
                else
                {
                    lstRoles.Items.Add("لا توجد أدوار مخصصة لهذا المستخدم");
                    lstRoles.ForeColor = Color.Gray;
                }
            }
            catch (Exception ex)
            {
                UIHelper.ShowError($"خطأ في تحميل أدوار المستخدم: {ex.Message}");
            }
        }

        private void LoadLoginHistory()
        {
            try
            {
                // هنا يمكن إضافة استدعاء لجلب سجل تسجيل الدخول من قاعدة البيانات
                // var loginHistory = LoginHistoryDataAccess.GetUserLoginHistory(_currentUser.UserId, 10);
                
                // مؤقتاً سنعرض رسالة
                var emptyTable = new DataTable();
                emptyTable.Columns.Add("LoginDate", typeof(DateTime));
                emptyTable.Columns.Add("IPAddress", typeof(string));
                emptyTable.Columns.Add("UserAgent", typeof(string));
                emptyTable.Columns.Add("IsSuccessful", typeof(string));
                
                // إضافة بيانات وهمية للعرض
                if (_currentUser.LastLoginDate.HasValue)
                {
                    var row = emptyTable.NewRow();
                    row["LoginDate"] = _currentUser.LastLoginDate.Value;
                    row["IPAddress"] = "*************";
                    row["UserAgent"] = "Windows 10 - Chrome";
                    row["IsSuccessful"] = "نجح";
                    emptyTable.Rows.Add(row);
                }
                
                dgvLoginHistory.DataSource = emptyTable;
                
                // تخصيص عرض النتيجة
                if (dgvLoginHistory.Rows.Count > 0)
                {
                    foreach (DataGridViewRow row in dgvLoginHistory.Rows)
                    {
                        if (row.Cells["IsSuccessful"].Value?.ToString() == "نجح")
                        {
                            row.Cells["IsSuccessful"].Style.ForeColor = Color.Green;
                            row.Cells["IsSuccessful"].Style.Font = new Font(dgvLoginHistory.Font, FontStyle.Bold);
                        }
                        else
                        {
                            row.Cells["IsSuccessful"].Style.ForeColor = Color.Red;
                            row.Cells["IsSuccessful"].Style.Font = new Font(dgvLoginHistory.Font, FontStyle.Bold);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                UIHelper.ShowError($"خطأ في تحميل سجل تسجيل الدخول: {ex.Message}");
            }
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            try
            {
                var editForm = new AddEditUserForm(_currentUser);
                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    // إعادة تحميل البيانات بعد التعديل
                    LoadUserData();
                    UIHelper.ShowSuccess("تم تحديث بيانات المستخدم بنجاح");
                }
            }
            catch (Exception ex)
            {
                UIHelper.ShowError($"خطأ في فتح نافذة التعديل: {ex.Message}");
            }
        }

        private void BtnClose_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        /// <summary>
        /// إضافة زر لإعادة تعيين كلمة المرور
        /// </summary>
        private void ResetPassword()
        {
            try
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من إعادة تعيين كلمة المرور للمستخدم '{_currentUser.Username}'؟\n\nسيتم تعيين كلمة المرور إلى: 123456",
                    "تأكيد إعادة تعيين كلمة المرور",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question,
                    MessageBoxDefaultButton.Button2,
                    MessageBoxOptions.RtlReading);

                if (result == DialogResult.Yes)
                {
                    UserDataAccess.UpdateUserPassword(_currentUser.UserId, "123456");
                    UIHelper.ShowSuccess("تم إعادة تعيين كلمة المرور بنجاح\nكلمة المرور الجديدة: 123456");
                }
            }
            catch (Exception ex)
            {
                UIHelper.ShowError($"خطأ في إعادة تعيين كلمة المرور: {ex.Message}");
            }
        }

        /// <summary>
        /// تبديل حالة المستخدم (تفعيل/إلغاء تفعيل)
        /// </summary>
        private void ToggleUserStatus()
        {
            try
            {
                string action = _currentUser.IsActive ? "إلغاء تفعيل" : "تفعيل";
                var result = MessageBox.Show(
                    $"هل أنت متأكد من {action} المستخدم '{_currentUser.Username}'؟",
                    $"تأكيد {action} المستخدم",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question,
                    MessageBoxDefaultButton.Button2,
                    MessageBoxOptions.RtlReading);

                if (result == DialogResult.Yes)
                {
                    _currentUser.IsActive = !_currentUser.IsActive;
                    UserDataAccess.UpdateUser(_currentUser);
                    LoadBasicUserInfo(); // إعادة تحميل البيانات لتحديث الحالة
                    UIHelper.ShowSuccess($"تم {action} المستخدم بنجاح");
                }
            }
            catch (Exception ex)
            {
                UIHelper.ShowError($"خطأ في تغيير حالة المستخدم: {ex.Message}");
            }
        }
    }
}
