using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Awqaf_Managment.Models.Accounting;
using Awqaf_Managment.Services.Accounting;
using Awqaf_Managment.Common.Helpers;
using UIHelper = Awqaf_Managment.UI.Helpers.UIHelper;

namespace Awqaf_Managment.UI.Forms.Accounting
{
    public partial class ChartOfAccountsManagementForm : Form
    {
        #region Fields
        private List<ChartOfAccount> _accounts;
        private List<ChartOfAccount> _filteredAccounts;
        private List<AccountType> _accountTypes;
        private List<AccountGroup> _accountGroups;
        private List<Currency> _currencies;
        private ChartOfAccount _selectedAccount;
        private bool _isEditMode = false;
        private bool _isLoadingData = false;
        private bool _isDarkMode = false;

        // حقول البحث المتقدم
        private string _currentSearchTerm = string.Empty;
        private int _currentAccountTypeFilter = -1;
        private int _currentAccountGroupFilter = -1;
        private bool _showActiveOnly = true;
        private bool _showParentAccountsOnly = false;
        #endregion

        #region Constructor
        public ChartOfAccountsManagementForm()
        {
            InitializeComponent();
            InitializeForm();
        }
        #endregion

        #region Initialization
        private void InitializeForm()
        {
            // إعداد النموذج للغة العربية
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            
            // تطبيق الخطوط العربية
            UIHelper.ApplyArabicFont(this);
            
            // تطبيق التصميم العصري
            ApplyModernDesign();
            
            // إعداد الأحداث
            SetupEvents();
            
            // تحميل البيانات
            LoadData();
            
            // إعداد شجرة الحسابات
            SetupAccountsTree();
            
            // إعداد النموذج
            SetupForm();
            
            // تطبيق الوضع الافتراضي
            SetAddMode();
        }

        private void ApplyModernDesign()
        {
            try
            {
                // تطبيق التصميم العصري الجديد
                ModernDesignHelper.ApplyModernDesign(this);

                // تطبيق التصميم المتجاوب
                ModernDesignHelper.ApplyResponsiveDesign(this);

                // تطبيق تحسينات إضافية
                ApplyCustomStyling();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق التصميم العصري: {ex.Message}");

                // تطبيق التصميم الأساسي في حالة الخطأ
                ApplyBasicDesign();
            }
        }

        private void ApplyCustomStyling()
        {
            try
            {
                // تخصيصات إضافية للنموذج
                this.BackColor = ModernDesignHelper.Colors.Background;

                // تحسين شجرة الحسابات
                if (treeViewAccounts != null)
                {
                    treeViewAccounts.BackColor = ModernDesignHelper.Colors.Surface;
                    treeViewAccounts.ForeColor = ModernDesignHelper.Colors.TextPrimary;
                    treeViewAccounts.BorderStyle = BorderStyle.None;
                    treeViewAccounts.Font = ModernDesignHelper.Fonts.GetDefaultArabicFont(9f);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في التخصيصات الإضافية: {ex.Message}");
            }
        }

        private void ApplyBasicDesign()
        {
            // التصميم الأساسي كبديل
            this.BackColor = Color.FromArgb(248, 249, 250);
            this.Font = new Font("Tahoma", 9F);
        }

        private void AddShadowEffect(Panel panel)
        {
            // محاكاة تأثير الظل بحدود ملونة
            panel.Paint += (s, e) =>
            {
                var rect = panel.ClientRectangle;
                rect.Width -= 1;
                rect.Height -= 1;
                
                using (var pen = new Pen(Color.FromArgb(30, 0, 0, 0), 1))
                {
                    e.Graphics.DrawRectangle(pen, rect);
                }
            };
        }

        private void ApplyButtonStyles()
        {
            // أزرار ملونة حسب الوظيفة
            if (btnAdd != null)
            {
                StyleButton(btnAdd, Color.FromArgb(40, 167, 69), Color.White, "➕");
            }
            
            if (btnEdit != null)
            {
                StyleButton(btnEdit, Color.FromArgb(0, 123, 255), Color.White, "✏️");
            }
            
            if (btnDelete != null)
            {
                StyleButton(btnDelete, Color.FromArgb(220, 53, 69), Color.White, "🗑️");
            }
            
            if (btnSave != null)
            {
                StyleButton(btnSave, Color.FromArgb(23, 162, 184), Color.White, "💾");
            }
            
            if (btnCancel != null)
            {
                StyleButton(btnCancel, Color.FromArgb(108, 117, 125), Color.White, "❌");
            }
            
            if (btnRefresh != null)
            {
                StyleButton(btnRefresh, Color.FromArgb(111, 66, 193), Color.White, "🔄");
            }
        }

        private void StyleButton(Button button, Color backColor, Color foreColor, string icon)
        {
            button.BackColor = backColor;
            button.ForeColor = foreColor;
            button.FlatStyle = FlatStyle.Flat;
            button.FlatAppearance.BorderSize = 0;
            button.FlatAppearance.MouseOverBackColor = ControlPaint.Light(backColor, 0.1f);
            button.FlatAppearance.MouseDownBackColor = ControlPaint.Dark(backColor, 0.1f);
            button.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            button.Text = $"{icon} {button.Text}";
            button.Cursor = Cursors.Hand;
            
            // تأثير الحواف المدورة
            button.Region = System.Drawing.Region.FromHrgn(CreateRoundRectRgn(0, 0, button.Width, button.Height, 8, 8));
        }

        [System.Runtime.InteropServices.DllImport("Gdi32.dll", EntryPoint = "CreateRoundRectRgn")]
        private static extern IntPtr CreateRoundRectRgn(int nLeftRect, int nTopRect, int nRightRect, int nBottomRect, int nWidthEllipse, int nHeightEllipse);

        private void SetupEvents()
        {
            // أحداث الأزرار
            btnAdd.Click += BtnAdd_Click;
            btnEdit.Click += BtnEdit_Click;
            btnDelete.Click += BtnDelete_Click;
            btnSave.Click += BtnSave_Click;
            btnCancel.Click += BtnCancel_Click;
            btnRefresh.Click += BtnRefresh_Click;
            btnToggleDarkMode.Click += BtnToggleDarkMode_Click;
            
            // أحداث شجرة الحسابات
            treeAccounts.AfterSelect += TreeAccounts_AfterSelect;
            treeAccounts.NodeMouseDoubleClick += TreeAccounts_NodeMouseDoubleClick;
            
            // أحداث البحث
            txtSearch.TextChanged += TxtSearch_TextChanged;
            
            // أحداث ComboBox
            cmbAccountType.SelectedIndexChanged += CmbAccountType_SelectedIndexChanged;
            cmbParentAccount.SelectedIndexChanged += CmbParentAccount_SelectedIndexChanged;
            
            // أحداث النموذج
            this.FormClosing += ChartOfAccountsManagementForm_FormClosing;
            
            // أحداث التحقق
            txtAccountCode.Leave += TxtAccountCode_Leave;
            txtAccountNameAr.Leave += TxtAccountNameAr_Leave;
        }
        #endregion

        #region Data Loading
        private void LoadData()
        {
            try
            {
                // تحميل الحسابات
                System.Diagnostics.Debug.WriteLine("ChartOfAccountsManagementForm: بدء تحميل البيانات");
                _accounts = ChartOfAccountsService.GetAllAccounts() ?? new List<ChartOfAccount>();
                _filteredAccounts = new List<ChartOfAccount>(_accounts);
                System.Diagnostics.Debug.WriteLine($"ChartOfAccountsManagementForm: تم تحميل {_accounts.Count} حساب");

                // تحميل أنواع الحسابات
                LoadAccountTypes();
                System.Diagnostics.Debug.WriteLine("ChartOfAccountsManagementForm: تم تحميل أنواع الحسابات");

                // تحميل مجموعات الحسابات
                LoadAccountGroups();
                System.Diagnostics.Debug.WriteLine("ChartOfAccountsManagementForm: تم تحميل مجموعات الحسابات");

                // تحميل العملات
                LoadCurrencies();
                System.Diagnostics.Debug.WriteLine("ChartOfAccountsManagementForm: تم تحميل العملات");

                // تحديث شجرة الحسابات
                RefreshAccountsTree();
                System.Diagnostics.Debug.WriteLine("ChartOfAccountsManagementForm: تم تحديث شجرة الحسابات");

                // تحديث القوائم المنسدلة
                LoadComboBoxes();
                System.Diagnostics.Debug.WriteLine("ChartOfAccountsManagementForm: تم تحديث القوائم المنسدلة");

                System.Diagnostics.Debug.WriteLine("ChartOfAccountsManagementForm: تم إنجاز تحميل البيانات بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"ChartOfAccountsManagementForm: خطأ في تحميل البيانات: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"ChartOfAccountsManagementForm: Stack trace: {ex.StackTrace}");
                UIHelper.ShowErrorMessage($"خطأ في تحميل البيانات: {ex.Message}");

                // في حالة الخطأ، تهيئة القوائم الفارغة
                _accounts = new List<ChartOfAccount>();
                LoadAccountTypes();
                LoadAccountGroups();
                LoadCurrencies();
            }
        }

        private void LoadAccountTypes()
        {
            _accountTypes = new List<AccountType>
            {
                new AccountType { AccountTypeId = 1, TypeNameAr = "أصول", TypeNameEn = "Assets", DefaultNature = AccountNature.Debit, AccountTypeName = "أصول" },
                new AccountType { AccountTypeId = 2, TypeNameAr = "خصوم", TypeNameEn = "Liabilities", DefaultNature = AccountNature.Credit, AccountTypeName = "خصوم" },
                new AccountType { AccountTypeId = 3, TypeNameAr = "حقوق ملكية", TypeNameEn = "Equity", DefaultNature = AccountNature.Credit, AccountTypeName = "حقوق ملكية" },
                new AccountType { AccountTypeId = 4, TypeNameAr = "إيرادات", TypeNameEn = "Revenue", DefaultNature = AccountNature.Credit, AccountTypeName = "إيرادات" },
                new AccountType { AccountTypeId = 5, TypeNameAr = "مصروفات", TypeNameEn = "Expenses", DefaultNature = AccountNature.Debit, AccountTypeName = "مصروفات" },
                new AccountType { AccountTypeId = 6, TypeNameAr = "حسابات نظامية", TypeNameEn = "System Accounts", DefaultNature = AccountNature.Debit, AccountTypeName = "حسابات نظامية" }
            };

            cmbAccountType.DataSource = _accountTypes;
            cmbAccountType.DisplayMember = "AccountTypeName";
            cmbAccountType.ValueMember = "AccountTypeId";
            cmbAccountType.SelectedIndex = -1;
        }

        private void LoadAccountGroups()
        {
            _accountGroups = new List<AccountGroup>
            {
                // مجموعات الأصول
                new AccountGroup { AccountGroupId = 1, GroupNameAr = "أصول متداولة", AccountTypeId = 1, GroupName = "أصول متداولة" },
                new AccountGroup { AccountGroupId = 2, GroupNameAr = "أصول ثابتة", AccountTypeId = 1, GroupName = "أصول ثابتة" },
                new AccountGroup { AccountGroupId = 3, GroupNameAr = "أصول غير ملموسة", AccountTypeId = 1, GroupName = "أصول غير ملموسة" },

                // مجموعات الخصوم
                new AccountGroup { AccountGroupId = 4, GroupNameAr = "خصوم متداولة", AccountTypeId = 2, GroupName = "خصوم متداولة" },
                new AccountGroup { AccountGroupId = 5, GroupNameAr = "خصوم طويلة الأجل", AccountTypeId = 2, GroupName = "خصوم طويلة الأجل" },

                // مجموعات حقوق الملكية
                new AccountGroup { AccountGroupId = 6, GroupNameAr = "رأس المال", AccountTypeId = 3, GroupName = "رأس المال" },
                new AccountGroup { AccountGroupId = 7, GroupNameAr = "الأرباح المحتجزة", AccountTypeId = 3, GroupName = "الأرباح المحتجزة" },

                // مجموعات الإيرادات
                new AccountGroup { AccountGroupId = 8, GroupNameAr = "إيرادات تشغيلية", AccountTypeId = 4, GroupName = "إيرادات تشغيلية" },
                new AccountGroup { AccountGroupId = 9, GroupNameAr = "إيرادات أخرى", AccountTypeId = 4, GroupName = "إيرادات أخرى" },

                // مجموعات المصروفات
                new AccountGroup { AccountGroupId = 10, GroupNameAr = "مصروفات تشغيلية", AccountTypeId = 5, GroupName = "مصروفات تشغيلية" },
                new AccountGroup { AccountGroupId = 11, GroupNameAr = "مصروفات إدارية", AccountTypeId = 5, GroupName = "مصروفات إدارية" },
                new AccountGroup { AccountGroupId = 12, GroupNameAr = "مصروفات أخرى", AccountTypeId = 5, GroupName = "مصروفات أخرى" }
            };
        }

        private void LoadCurrencies()
        {
            _currencies = new List<Currency>
            {
                new Currency { CurrencyId = 1, CurrencyCode = "SAR", CurrencyNameAr = "ريال سعودي", Symbol = "ر.س" },
                new Currency { CurrencyId = 2, CurrencyCode = "USD", CurrencyNameAr = "دولار أمريكي", Symbol = "$" },
                new Currency { CurrencyId = 3, CurrencyCode = "EUR", CurrencyNameAr = "يورو", Symbol = "€" }
            };

            cmbCurrency.DataSource = _currencies;
            cmbCurrency.DisplayMember = "CurrencyNameAr";
            cmbCurrency.ValueMember = "CurrencyId";
            cmbCurrency.SelectedIndex = 0; // افتراضي: ريال سعودي
        }

        private void LoadComboBoxes()
        {
            // تحديث قائمة الحسابات الأب
            LoadParentAccounts();

            // تحديث قائمة مجموعات الحسابات حسب النوع المحدد
            if (cmbAccountType.SelectedValue != null)
            {
                LoadAccountGroupsByType(GetComboBoxIntValue(cmbAccountType));
            }
        }

        private void LoadParentAccounts()
        {
            var parentAccounts = _accounts.Where(a => a.LevelType == AccountLevelType.Main || a.LevelType == AccountLevelType.Sub).ToList();
            parentAccounts.Insert(0, new ChartOfAccount { AccountId = 0, AccountNameAr = "-- لا يوجد حساب أب --" });

            cmbParentAccount.DataSource = parentAccounts;
            cmbParentAccount.DisplayMember = "AccountNameAr";
            cmbParentAccount.ValueMember = "AccountId";
            cmbParentAccount.SelectedIndex = 0;
        }

        private void LoadAccountGroupsByType(int accountTypeId)
        {
            // إذا كان accountTypeId صفر أو غير صحيح، عرض جميع المجموعات
            List<AccountGroup> groups;
            if (accountTypeId > 0)
            {
                
                groups = _accountGroups.Where(g => g.AccountTypeId == accountTypeId).ToList();

                // إضافة خيار فارغ في البداية
                groups.Insert(0, new AccountGroup { AccountGroupId = 0, GroupNameAr = "-- اختر مجموعة الحساب --" });

                cmbAccountGroup.DataSource = groups;
                cmbAccountGroup.DisplayMember = "GroupNameAr";
                cmbAccountGroup.ValueMember = "AccountGroupId";
                cmbAccountGroup.SelectedIndex = 0; // تحديد الخيار الفارغ

            }

          
        }
        #endregion

        #region Tree Management
        private void SetupAccountsTree()
        {
            treeAccounts.ImageList = CreateTreeImageList();
            treeAccounts.ShowLines = true;
            treeAccounts.ShowPlusMinus = true;
            treeAccounts.ShowRootLines = true;
            treeAccounts.HideSelection = false;
            treeAccounts.FullRowSelect = true;
            treeAccounts.BackColor = Color.White;
            treeAccounts.ForeColor = Color.FromArgb(52, 58, 64);
            treeAccounts.Font = new Font("Segoe UI", 10F);
        }

        private ImageList CreateTreeImageList()
        {
            var imageList = new ImageList();
            imageList.ImageSize = new Size(16, 16);

            // إضافة أيقونات للحسابات
            var folderIcon = SystemIcons.Application.ToBitmap();
            var accountIcon = SystemIcons.Information.ToBitmap();

            imageList.Images.Add("folder", folderIcon);
            imageList.Images.Add("account", accountIcon);

            return imageList;
        }

        private void RefreshAccountsTree()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("RefreshAccountsTree: بدء تحديث شجرة الحسابات");

                if (treeAccounts == null)
                {
                    System.Diagnostics.Debug.WriteLine("RefreshAccountsTree: treeAccounts is null");
                    return;
                }

                treeAccounts.Nodes.Clear();

                if (_accounts == null || _accounts.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine("RefreshAccountsTree: لا توجد حسابات للعرض");
                    return;
                }

                // إنشاء العقد الجذرية (الحسابات الرئيسية)
                var rootAccounts = _accounts.Where(a => a.ParentAccountId == null || a.ParentAccountId == 0).OrderBy(a => a.AccountCode);
                System.Diagnostics.Debug.WriteLine($"RefreshAccountsTree: عدد الحسابات الجذرية: {rootAccounts.Count()}");

                foreach (var account in rootAccounts)
                {
                    try
                    {
                        var node = CreateAccountNode(account);
                        treeAccounts.Nodes.Add(node);
                        AddChildNodes(node, account.AccountId);
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"RefreshAccountsTree: خطأ في إنشاء عقدة للحساب {account.AccountCode}: {ex.Message}");
                    }
                }

                treeAccounts.ExpandAll();
                System.Diagnostics.Debug.WriteLine("RefreshAccountsTree: تم إنجاز تحديث شجرة الحسابات بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"RefreshAccountsTree: خطأ عام: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"RefreshAccountsTree: Stack trace: {ex.StackTrace}");
            }
        }

        private TreeNode CreateAccountNode(ChartOfAccount account)
        {
            try
            {
                if (account == null)
                {
                    System.Diagnostics.Debug.WriteLine("CreateAccountNode: account is null");
                    return new TreeNode("حساب غير صحيح");
                }

                string accountCode = account.AccountCode ?? "غير محدد";
                string accountName = account.AccountNameAr ?? account.AccountNameAr ?? "غير محدد";

                var node = new TreeNode($"{accountCode} - {accountName}")
                {
                    Tag = account,
                    ImageKey = account.IsParentAccount ? "folder" : "account",
                    SelectedImageKey = account.IsParentAccount ? "folder" : "account"
                };

                // تلوين العقد حسب نوع الحساب
                switch (account.AccountTypeId)
                {
                    case 1003: // أصول
                        node.ForeColor = Color.FromArgb(40, 167, 69);
                        break;
                    case 1004: // خصوم
                        node.ForeColor = Color.FromArgb(220, 53, 69);
                        break;
                    case 1005: // حقوق ملكية
                        node.ForeColor = Color.FromArgb(111, 66, 193);
                        break;
                    case 1006: // إيرادات
                        node.ForeColor = Color.FromArgb(0, 123, 255);
                        break;
                    case 1007: // مصروفات
                        node.ForeColor = Color.FromArgb(255, 193, 7);
                        break;
                    default:
                        node.ForeColor = Color.FromArgb(108, 117, 125);
                        break;
                }

                return node;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"CreateAccountNode: خطأ في إنشاء العقدة: {ex.Message}");
                return new TreeNode("خطأ في تحميل الحساب");
            }
        }

        private void AddChildNodes(TreeNode parentNode, int parentAccountId)
        {
            try
            {
                if (parentNode == null || _accounts == null)
                {
                    System.Diagnostics.Debug.WriteLine("AddChildNodes: parentNode or _accounts is null");
                    return;
                }

                var childAccounts = _accounts.Where(a => a.ParentAccountId == parentAccountId).OrderBy(a => a.AccountCode);

                foreach (var account in childAccounts)
                {
                    try
                    {
                        var childNode = CreateAccountNode(account);
                        parentNode.Nodes.Add(childNode);
                        AddChildNodes(childNode, account.AccountId);
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"AddChildNodes: خطأ في إضافة العقدة الفرعية للحساب {account.AccountCode}: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"AddChildNodes: خطأ عام: {ex.Message}");
            }
        }
        #endregion

        #region Form Management
        private void SetupForm()
        {
            // إعداد الحقول
            txtAccountCode.ReadOnly = true; // يُولّد تلقائياً
            txtAccountLevel.ReadOnly = true; // محسوب تلقائياً

            // إعداد القيم الافتراضية
            chkIsActive.Checked = true;
            chkAllowPosting.Checked = true;
            chkIsParentAccount.Checked = false;

            // إعداد نوع الرصيد
            cmbBalanceType.Items.AddRange(new string[] { "مدين", "دائن" });
            cmbBalanceType.SelectedIndex = 0;
        }

        private void SetAddMode()
        {
            _isEditMode = false;
            var parentAccount = _selectedAccount; // حفظ الحساب المحدد كحساب أب محتمل
            _selectedAccount = null;

            ClearForm();
            EnableFormControls(true);

            // إذا كان هناك حساب محدد، اجعله الحساب الأب للحساب الجديد
            if (parentAccount != null)
            {
                cmbParentAccount.SelectedValue = parentAccount.AccountId;

                // تحديد نوع الحساب ومجموعة الحساب تلقائياً بناءً على الحساب الأب
                cmbAccountType.SelectedValue = parentAccount.AccountTypeId;
                if (parentAccount.AccountGroupId > 0)
                {
                    LoadAccountGroupsByType(parentAccount.AccountTypeId);
                    cmbAccountGroup.SelectedValue = parentAccount.AccountGroupId;
                }

                // تحديد مستوى الحساب (مستوى الأب + 1)
                txtAccountLevel.Text = (parentAccount.AccountLevel + 1).ToString();

                // إنشاء رمز الحساب التلقائي
                GenerateAccountCode();
            }

            btnAdd.Enabled = false;
            btnEdit.Enabled = false;
            btnDelete.Enabled = false;
            btnSave.Enabled = true;
            btnCancel.Enabled = true;

            txtAccountNameAr.Focus();
        }

        private void SetEditMode(ChartOfAccount account)
        {
            _isEditMode = true;
            _selectedAccount = account;

            LoadAccountToForm(account);
            EnableFormControls(true);

            btnAdd.Enabled = false;
            btnEdit.Enabled = false;
            btnDelete.Enabled = false;
            btnSave.Enabled = true;
            btnCancel.Enabled = true;
        }

        private void SetViewMode()
        {
            _isEditMode = false;

            EnableFormControls(false);

            btnAdd.Enabled = true;
            btnEdit.Enabled = _selectedAccount != null;
            btnDelete.Enabled = _selectedAccount != null && !_selectedAccount.IsParent;
            btnSave.Enabled = false;
            btnCancel.Enabled = false;
        }

        private void EnableFormControls(bool enabled)
        {
            foreach (Control control in pnlAccountData.Controls)
            {
                if (control is TextBox || control is ComboBox || control is CheckBox)
                {
                    control.Enabled = enabled;
                }
            }

            foreach (Control control in pnlSettings.Controls)
            {
                if (control is CheckBox || control is ComboBox)
                {
                    control.Enabled = enabled;
                }
            }

            foreach (Control control in pnlOpeningBalance.Controls)
            {
                if (control is TextBox || control is ComboBox)
                {
                    control.Enabled = enabled;
                }
            }

            // الحقول التي تبقى للقراءة فقط
            txtAccountCode.ReadOnly = true;
            txtAccountLevel.ReadOnly = true;
        }

        private void ClearForm()
        {
            txtAccountCode.Clear();
            txtAccountNameAr.Clear();
            txtAccountNameEn.Clear();
            txtAccountLevel.Clear();
            txtOpeningBalance.Clear();
            txtDescription.Clear();

            cmbParentAccount.SelectedIndex = -1;
            cmbAccountType.SelectedIndex = -1;
            cmbAccountGroup.SelectedIndex = -1;
            cmbCurrency.SelectedIndex = 0;
            cmbBalanceType.SelectedIndex = 0;

            chkIsActive.Checked = true;
            chkAllowPosting.Checked = true;
            chkIsParentAccount.Checked = false;
        }



        private void LoadAccountToForm(ChartOfAccount account)
        {
            _isLoadingData = true; // منع توليد كود جديد أثناء التحميل

            txtAccountCode.Text = account.AccountCode;
            txtAccountNameAr.Text = account.AccountNameAr;
            txtAccountNameEn.Text = account.AccountNameEn ?? "";
            txtAccountLevel.Text = account.AccountLevel.ToString();
            txtOpeningBalance.Text = account.OpeningBalance.ToString("F2");
            txtDescription.Text = account.Description ?? "";

            // تحديد الحساب الأب
            if (account.ParentAccountId.HasValue)
            {
                var parentAccount = _accounts.FirstOrDefault(a => a.AccountId == account.ParentAccountId.Value);
                if (parentAccount != null)
                {
                    cmbParentAccount.SelectedValue = parentAccount.AccountId;
                }
            }

            cmbAccountType.SelectedValue = account.AccountTypeId;
            cmbAccountGroup.SelectedValue = account.AccountGroupId;
            // تعيين العملة بناءً على رمز العملة
            if (!string.IsNullOrEmpty(account.CurrencyCode))
            {
                var currency = _currencies.FirstOrDefault(c => c.CurrencyCode == account.CurrencyCode);
                if (currency != null)
                {
                    cmbCurrency.SelectedValue = currency.CurrencyId;
                }
                else
                {
                    cmbCurrency.SelectedIndex = 0; // افتراضي: ريال سعودي
                }
            }
            else
            {
                cmbCurrency.SelectedIndex = 0; // افتراضي: ريال سعودي
            }
            SetAccountNatureInComboBox(account.Nature);

            chkIsActive.Checked = account.IsActive;
            chkAllowPosting.Checked = account.AllowPosting;
            chkIsParentAccount.Checked = account.IsParent;

            _isLoadingData = false; // إعادة تفعيل توليد الكود
        }
        #endregion

        #region Event Handlers
        private void BtnAdd_Click(object sender, EventArgs e)
        {
            SetAddMode();
        }

        private void BtnEdit_Click(object sender, EventArgs e)
        {
            if (_selectedAccount != null)
            {
                SetEditMode(_selectedAccount);
            }
        }

        private void BtnDelete_Click(object sender, EventArgs e)
        {
            if (_selectedAccount == null) return;

            if (_selectedAccount.IsParent)
            {
                UIHelper.ShowWarningMessage("لا يمكن حذف حساب أب يحتوي على حسابات فرعية");
                return;
            }

            var result = UIHelper.ShowConfirmMessage($"هل تريد حذف الحساب '{_selectedAccount.AccountNameAr}'؟", "تأكيد الحذف");

            if (result == DialogResult.Yes)
            {
                try
                {
                    ChartOfAccountsService.DeleteAccount(_selectedAccount.AccountId);
                    LoadData();
                    SetViewMode();

                    UIHelper.ShowInfoMessage("تم حذف الحساب بنجاح");
                }
                catch (Exception ex)
                {
                    UIHelper.ShowErrorMessage($"خطأ في حذف الحساب: {ex.Message}");
                }
            }
        }

        private void BtnSave_Click(object sender, EventArgs e)
        {
            if (!ValidateForm()) return;

            try
            {
                var account = CreateAccountFromForm();

                if (_isEditMode)
                {
                    account.AccountId = _selectedAccount.AccountId;
                    ChartOfAccountsService.UpdateAccount(account);
                    MessageBox.Show("تم تحديث الحساب بنجاح", "نجح التحديث",
                        MessageBoxButtons.OK, MessageBoxIcon.Information,
                        MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                }
                else
                {
                    ChartOfAccountsService.AddAccount(account);
                    MessageBox.Show("تم إضافة الحساب بنجاح", "نجحت الإضافة",
                        MessageBoxButtons.OK, MessageBoxIcon.Information,
                        MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                }

                LoadData();
                SetViewMode();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الحساب: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
        }

        private void BtnCancel_Click(object sender, EventArgs e)
        {
            SetViewMode();
            if (_selectedAccount != null)
            {
                LoadAccountToForm(_selectedAccount);
            }
            else
            {
                ClearForm();
            }
        }

        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            LoadData();
        }

        private void BtnToggleDarkMode_Click(object sender, EventArgs e)
        {
            _isDarkMode = !_isDarkMode;
            ApplyTheme();
        }

        private void TreeAccounts_AfterSelect(object sender, TreeViewEventArgs e)
        {
            if (e.Node?.Tag is ChartOfAccount account)
            {
                _selectedAccount = account;
                LoadAccountToForm(account);
                SetViewMode();
            }
        }

        private void TreeAccounts_NodeMouseDoubleClick(object sender, TreeNodeMouseClickEventArgs e)
        {
            if (e.Node?.Tag is ChartOfAccount account)
            {
                _selectedAccount = account;
                SetEditMode(account);
            }
        }

        private void TxtSearch_TextChanged(object sender, EventArgs e)
        {
            FilterAccountsTree(txtSearch.Text);
        }

        private void CmbAccountType_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbAccountType.SelectedValue != null)
            {
                var accountTypeId = GetComboBoxIntValue(cmbAccountType);
             
                LoadAccountGroupsByType(accountTypeId);

                // تحديد طبيعة الحساب تلقائياً
                var accountType = _accountTypes.FirstOrDefault(at => at.AccountTypeId == accountTypeId);
                if (accountType != null)
                {
                    SetAccountNatureInComboBox(accountType.Nature);
                }
            }
        }

        private void CmbParentAccount_SelectedIndexChanged(object sender, EventArgs e)
        {
            // تجاهل الحدث أثناء تحميل البيانات
            if (_isLoadingData) return;

            // تحديث نوع الحساب ومجموعة الحساب بناءً على الحساب الأب
            var parentAccountId = GetComboBoxNullableIntValue(cmbParentAccount);
            if (parentAccountId.HasValue && parentAccountId.Value > 0)
            {
                var parentAccount = _accounts.FirstOrDefault(a => a.AccountId == parentAccountId.Value);
                if (parentAccount != null)
                {
                    // تحديد نوع الحساب بناءً على الحساب الأب
                    cmbAccountType.SelectedValue = parentAccount.AccountTypeId;

                    // تحديد مجموعة الحساب بناءً على الحساب الأب
                    if (parentAccount.AccountGroupId > 0)
                    {
                        cmbAccountGroup.SelectedValue = parentAccount.AccountGroupId;
                    }
                }
            }

            GenerateAccountCode();
            CalculateAccountLevel();
        }

        private void TxtAccountCode_Leave(object sender, EventArgs e)
        {
            CheckAccountCodeDuplication();
            CalculateAccountLevel();
        }

        private void TxtAccountNameAr_Leave(object sender, EventArgs e)
        {
            CheckAccountNameDuplication();
        }

        private void ChartOfAccountsManagementForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (btnSave.Enabled)
            {
                var result = MessageBox.Show("هناك تغييرات غير محفوظة. هل تريد الخروج بدون حفظ؟", "تأكيد الخروج",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question,
                    MessageBoxDefaultButton.Button2, MessageBoxOptions.RtlReading);

                if (result == DialogResult.No)
                {
                    e.Cancel = true;
                }
            }
        }
        #endregion

        #region Helper Methods


        private void FilterAccountsTree(string searchText)
        {
            if (string.IsNullOrWhiteSpace(searchText))
            {
                RefreshAccountsTree();
                return;
            }

            treeAccounts.Nodes.Clear();

            var filteredAccounts = _accounts.Where(a =>
                a.AccountCode.Contains(searchText) ||
                a.AccountNameAr.Contains(searchText) ||
                (a.AccountNameEn != null && a.AccountNameEn.Contains(searchText))
            ).ToList();

            foreach (var account in filteredAccounts)
            {
                var node = CreateAccountNode(account);
                treeAccounts.Nodes.Add(node);
            }

            treeAccounts.ExpandAll();
        }

        private void GenerateAccountCode()
        {
            try
            {
                // تحقق من أننا في وضع الإضافة وليس التعديل أو تحميل البيانات
                if (_isEditMode || _isLoadingData) return;

                var parentAccountId = GetComboBoxNullableIntValue(cmbParentAccount);

                if (!parentAccountId.HasValue || parentAccountId.Value <= 0)
                {
                    // حساب رئيسي
                    var existingCodes = _accounts.Select(a => a.AccountCode).ToList();
                    var newCode = AccountCodeGenerator.GenerateNextCode("", existingCodes);
                    txtAccountCode.Text = newCode;
                }
                else
                {
                    // حساب فرعي
                    var parentAccount = _accounts.FirstOrDefault(a => a.AccountId == parentAccountId.Value);

                    if (parentAccount != null)
                    {
                        var existingCodes = _accounts.Select(a => a.AccountCode).ToList();
                        var newCode = AccountCodeGenerator.GenerateNextCode(parentAccount.AccountCode, existingCodes);
                        txtAccountCode.Text = newCode;
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في توليد كود الحساب: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
            }
        }

        private void CalculateAccountLevel()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(txtAccountCode.Text))
                {
                    txtAccountLevel.Text = "1";
                    return;
                }

                var level = AccountCodeGenerator.GetAccountLevel(txtAccountCode.Text);
                txtAccountLevel.Text = level.ToString();
            }
            catch (Exception)
            {
                txtAccountLevel.Text = "1";
            }
        }

        private void CheckAccountCodeDuplication()
        {
            if (string.IsNullOrWhiteSpace(txtAccountCode.Text)) return;

            // التحقق من صحة تنسيق الكود
            if (!AccountCodeGenerator.ValidateAccountCode(txtAccountCode.Text))
            {
                MessageBox.Show("تنسيق رمز الحساب غير صحيح. يجب أن يكون بالشكل: X.XX.XXX", "تحذير",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                txtAccountCode.Focus();
                return;
            }

            // التحقق من عدم التكرار
            var existingAccount = _accounts.FirstOrDefault(a =>
                a.AccountCode == txtAccountCode.Text &&
                (_selectedAccount == null || a.AccountId != _selectedAccount.AccountId));

            if (existingAccount != null)
            {
                MessageBox.Show("رمز الحساب موجود مسبقاً. يرجى اختيار رمز آخر.", "تحذير",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                txtAccountCode.Focus();
            }
        }

        private void CheckAccountNameDuplication()
        {
            if (string.IsNullOrWhiteSpace(txtAccountNameAr.Text)) return;

            var existingAccount = _accounts.FirstOrDefault(a =>
                a.AccountNameAr == txtAccountNameAr.Text &&
                (_selectedAccount == null || a.AccountId != _selectedAccount.AccountId));

            if (existingAccount != null)
            {
                MessageBox.Show("اسم الحساب موجود مسبقاً. يرجى اختيار اسم آخر.", "تحذير",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                txtAccountNameAr.Focus();
            }
        }

        private void ApplyTheme()
        {
            if (_isDarkMode)
            {
                // الوضع الليلي
                this.BackColor = Color.FromArgb(33, 37, 41);

                foreach (Control control in this.Controls)
                {
                    ApplyDarkThemeToControl(control);
                }
            }
            else
            {
                // الوضع النهاري
                this.BackColor = Color.FromArgb(248, 249, 250);
                ApplyModernDesign();
            }
        }

        private void ApplyDarkThemeToControl(Control control)
        {
            if (control is Panel || control is GroupBox)
            {
                control.BackColor = Color.FromArgb(52, 58, 64);
                control.ForeColor = Color.White;
            }
            else if (control is TextBox || control is ComboBox)
            {
                control.BackColor = Color.FromArgb(73, 80, 87);
                control.ForeColor = Color.White;
            }
            else if (control is TreeView)
            {
                control.BackColor = Color.FromArgb(52, 58, 64);
                control.ForeColor = Color.White;
            }

            foreach (Control child in control.Controls)
            {
                ApplyDarkThemeToControl(child);
            }
        }

        private bool ValidateForm()
        {
            // التحقق من الحقول المطلوبة
            if (string.IsNullOrWhiteSpace(txtAccountNameAr.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الحساب بالعربية", "حقل مطلوب",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                txtAccountNameAr.Focus();
                return false;
            }

            if (cmbAccountType.SelectedIndex == -1)
            {
                MessageBox.Show("يرجى اختيار نوع الحساب", "حقل مطلوب",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                cmbAccountType.Focus();
                return false;
            }

            if (cmbAccountGroup.SelectedIndex <= 0 || GetComboBoxIntValue(cmbAccountGroup) <= 0)
            {
                MessageBox.Show("يرجى اختيار مجموعة الحساب", "حقل مطلوب",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                cmbAccountGroup.Focus();
                return false;
            }

            // التحقق من رمز الحساب
            if (string.IsNullOrWhiteSpace(txtAccountCode.Text))
            {
                MessageBox.Show("يرجى إدخال رمز الحساب", "حقل مطلوب",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                txtAccountCode.Focus();
                return false;
            }

            // التحقق من مستوى الحساب
            if (string.IsNullOrWhiteSpace(txtAccountLevel.Text) || !int.TryParse(txtAccountLevel.Text, out int level) || level < 1)
            {
                MessageBox.Show("مستوى الحساب غير صحيح", "قيمة غير صحيحة",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning,
                    MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                txtAccountLevel.Focus();
                return false;
            }

            // التحقق من الرصيد الافتتاحي
            if (!string.IsNullOrWhiteSpace(txtOpeningBalance.Text))
            {
                if (!decimal.TryParse(txtOpeningBalance.Text, out decimal balance))
                {
                    MessageBox.Show("يرجى إدخال رصيد افتتاحي صحيح", "قيمة غير صحيحة",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning,
                        MessageBoxDefaultButton.Button1, MessageBoxOptions.RtlReading);
                    txtOpeningBalance.Focus();
                    return false;
                }
            }

            return true;
        }

        private ChartOfAccount CreateAccountFromForm()
        {
            var account = new ChartOfAccount
            {
                AccountCode = txtAccountCode.Text.Trim(),
                AccountName = string.IsNullOrWhiteSpace(txtAccountNameEn.Text) ? txtAccountNameAr.Text.Trim() : txtAccountNameEn.Text.Trim(),
                AccountNameAr = txtAccountNameAr.Text.Trim(),
                AccountNameEn = string.IsNullOrWhiteSpace(txtAccountNameEn.Text) ? null : txtAccountNameEn.Text.Trim(),
                AccountTypeId = GetComboBoxIntValue(cmbAccountType),
                AccountGroupId = GetComboBoxIntValue(cmbAccountGroup),
                ParentAccountId = GetComboBoxNullableIntValue(cmbParentAccount),
                AccountLevel = string.IsNullOrWhiteSpace(txtAccountLevel.Text) ? 1 : int.Parse(txtAccountLevel.Text),
                CurrencyCode = GetSelectedCurrencyCode(),
                OpeningBalance = string.IsNullOrWhiteSpace(txtOpeningBalance.Text) ? 0 : decimal.Parse(txtOpeningBalance.Text),
                IsActive = chkIsActive.Checked,
                AllowPosting = chkAllowPosting.Checked,
                IsParent = chkIsParentAccount.Checked,
                Description = string.IsNullOrWhiteSpace(txtDescription.Text) ? null : txtDescription.Text.Trim()
            };

            return account;
        }

        /// <summary>
        /// الحصول على قيمة int من ComboBox
        /// Get int value from ComboBox
        /// </summary>
        private int GetComboBoxIntValue(ComboBox comboBox)
        {
            if (comboBox.SelectedValue != null && int.TryParse(comboBox.SelectedValue.ToString(), out int value))
            {
                return value;
            }
            return 0;
        }

        /// <summary>
        /// الحصول على رمز العملة المحددة
        /// Get selected currency code
        /// </summary>
        private string GetSelectedCurrencyCode()
        {
            if (cmbCurrency.SelectedItem is Currency selectedCurrency)
            {
                return selectedCurrency.CurrencyCode;
            }
            return "SAR"; // افتراضي
        }

        /// <summary>
        /// الحصول على قيمة int? من ComboBox
        /// Get nullable int value from ComboBox
        /// </summary>
        private int? GetComboBoxNullableIntValue(ComboBox comboBox)
        {
            if (comboBox.SelectedValue != null && int.TryParse(comboBox.SelectedValue.ToString(), out int value) && value > 0)
            {
                return value;
            }
            return null;
        }

        /// <summary>
        /// الحصول على طبيعة الحساب من ComboBox
        /// Get account nature from ComboBox
        /// </summary>
        private AccountNature GetAccountNatureFromComboBox()
        {
            if (cmbBalanceType.SelectedItem != null)
            {
                string selectedText = cmbBalanceType.SelectedItem.ToString();
                return selectedText == "دائن" ? AccountNature.Credit : AccountNature.Debit;
            }
            return AccountNature.Debit;
        }

        /// <summary>
        /// تعيين طبيعة الحساب في ComboBox
        /// Set account nature in ComboBox
        /// </summary>
        private void SetAccountNatureInComboBox(AccountNature nature)
        {
            string natureText = nature == AccountNature.Credit ? "دائن" : "مدين";
            cmbBalanceType.SelectedItem = natureText;
        }

        #endregion

        #region Advanced Search Functions
        /// <summary>
        /// تطبيق البحث والفلترة المتقدمة
        /// Apply advanced search and filtering
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        /// <param name="accountTypeId">معرف نوع الحساب</param>
        /// <param name="accountGroupId">معرف مجموعة الحساب</param>
        /// <param name="activeOnly">الحسابات النشطة فقط</param>
        /// <param name="parentAccountsOnly">الحسابات الأب فقط</param>
        public void ApplyAdvancedSearch(string searchTerm = "", int accountTypeId = -1,
            int accountGroupId = -1, bool activeOnly = true, bool parentAccountsOnly = false)
        {
            try
            {
                _currentSearchTerm = searchTerm?.Trim() ?? "";
                _currentAccountTypeFilter = accountTypeId;
                _currentAccountGroupFilter = accountGroupId;
                _showActiveOnly = activeOnly;
                _showParentAccountsOnly = parentAccountsOnly;

                // تطبيق الفلترة
                _filteredAccounts = FilterAccounts(_accounts);

                // تحديث شجرة الحسابات
                RefreshAccountsTreeWithFilter();

                // عرض إحصائيات البحث
                ShowSearchStatistics();
            }
            catch (Exception ex)
            {
                UIHelper.ShowErrorMessage($"خطأ في البحث المتقدم: {ex.Message}");
            }
        }

        /// <summary>
        /// فلترة الحسابات حسب المعايير المحددة
        /// Filter accounts based on specified criteria
        /// </summary>
        /// <param name="accounts">قائمة الحسابات</param>
        /// <returns>قائمة الحسابات المفلترة</returns>
        private List<ChartOfAccount> FilterAccounts(List<ChartOfAccount> accounts)
        {
            var filtered = accounts.AsEnumerable();

            // فلترة حسب النشاط
            if (_showActiveOnly)
            {
                filtered = filtered.Where(a => a.IsActive);
            }

            // فلترة حسب الحسابات الأب فقط
            if (_showParentAccountsOnly)
            {
                filtered = filtered.Where(a => a.IsParent || a.IsParentAccount);
            }

            // فلترة حسب نوع الحساب
            if (_currentAccountTypeFilter > 0)
            {
                filtered = filtered.Where(a => a.AccountTypeId == _currentAccountTypeFilter);
            }

            // فلترة حسب مجموعة الحساب
            if (_currentAccountGroupFilter > 0)
            {
                filtered = filtered.Where(a => a.AccountGroupId == _currentAccountGroupFilter);
            }

            // فلترة حسب مصطلح البحث
            if (!string.IsNullOrWhiteSpace(_currentSearchTerm))
            {
                var searchLower = _currentSearchTerm.ToLower();
                filtered = filtered.Where(a =>
                    (a.AccountCode?.ToLower().Contains(searchLower) ?? false) ||
                    (a.AccountNameAr?.ToLower().Contains(searchLower) ?? false) ||
                    (a.AccountNameEn?.ToLower().Contains(searchLower) ?? false) ||
                    (a.Description?.ToLower().Contains(searchLower) ?? false)
                );
            }

            return filtered.ToList();
        }

        /// <summary>
        /// تحديث شجرة الحسابات مع الفلترة
        /// Refresh accounts tree with filtering
        /// </summary>
        private void RefreshAccountsTreeWithFilter()
        {
            if (treeViewAccounts == null) return;

            try
            {
                treeViewAccounts.BeginUpdate();
                treeViewAccounts.Nodes.Clear();

                // إنشاء عقد الشجرة للحسابات المفلترة
                var rootAccounts = _filteredAccounts.Where(a => a.ParentAccountId == null || a.ParentAccountId == 0).ToList();

                foreach (var account in rootAccounts.OrderBy(a => a.AccountCode))
                {
                    var node = CreateAccountNode(account);
                    treeViewAccounts.Nodes.Add(node);

                    // إضافة الحسابات الفرعية
                    AddChildAccountNodes(node, account.AccountId);
                }

                // توسيع العقد إذا كان هناك بحث نشط
                if (!string.IsNullOrWhiteSpace(_currentSearchTerm))
                {
                    treeViewAccounts.ExpandAll();
                }
                else
                {
                    // توسيع المستوى الأول فقط
                    foreach (TreeNode node in treeViewAccounts.Nodes)
                    {
                        node.Expand();
                    }
                }
            }
            catch (Exception ex)
            {
                UIHelper.ShowErrorMessage($"خطأ في تحديث شجرة الحسابات: {ex.Message}");
            }
            finally
            {
                treeViewAccounts.EndUpdate();
            }
        }

        /// <summary>
        /// إضافة عقد الحسابات الفرعية
        /// Add child account nodes
        /// </summary>
        /// <param name="parentNode">العقدة الأب</param>
        /// <param name="parentAccountId">معرف الحساب الأب</param>
        private void AddChildAccountNodes(TreeNode parentNode, int parentAccountId)
        {
            var childAccounts = _filteredAccounts.Where(a => a.ParentAccountId == parentAccountId).ToList();

            foreach (var account in childAccounts.OrderBy(a => a.AccountCode))
            {
                var node = CreateAccountNode(account);
                parentNode.Nodes.Add(node);

                // إضافة الحسابات الفرعية للمستوى التالي
                AddChildAccountNodes(node, account.AccountId);
            }
        }

        /// <summary>
        /// إنشاء عقدة حساب
        /// Create account node
        /// </summary>
        /// <param name="account">الحساب</param>
        /// <returns>عقدة الشجرة</returns>
        private TreeNode CreateAccountNode(ChartOfAccount account)
        {
            var nodeText = $"{account.AccountCode} - {account.AccountNameAr}";
            var node = new TreeNode(nodeText)
            {
                Tag = account,
                ImageIndex = GetAccountImageIndex(account),
                SelectedImageIndex = GetAccountImageIndex(account)
            };

            // تلوين العقدة حسب حالة الحساب
            if (!account.IsActive)
            {
                node.ForeColor = Color.Gray;
            }
            else if (account.IsParent || account.IsParentAccount)
            {
                node.ForeColor = Color.Blue;
                node.NodeFont = new Font(treeViewAccounts.Font, FontStyle.Bold);
            }

            return node;
        }

        /// <summary>
        /// الحصول على فهرس الصورة للحساب
        /// Get image index for account
        /// </summary>
        /// <param name="account">الحساب</param>
        /// <returns>فهرس الصورة</returns>
        private int GetAccountImageIndex(ChartOfAccount account)
        {
            if (account.IsParent || account.IsParentAccount)
                return 0; // أيقونة مجلد
            else
                return 1; // أيقونة ملف
        }

        /// <summary>
        /// عرض إحصائيات البحث
        /// Show search statistics
        /// </summary>
        private void ShowSearchStatistics()
        {
            try
            {
                var totalAccounts = _accounts.Count;
                var filteredCount = _filteredAccounts.Count;
                var activeCount = _filteredAccounts.Count(a => a.IsActive);
                var parentCount = _filteredAccounts.Count(a => a.IsParent || a.IsParentAccount);

                var statsMessage = $"إجمالي الحسابات: {totalAccounts} | المعروضة: {filteredCount} | النشطة: {activeCount} | الحسابات الأب: {parentCount}";

                // يمكن عرض الإحصائيات في شريط الحالة أو label
                // lblSearchStats.Text = statsMessage;

                System.Diagnostics.Debug.WriteLine($"إحصائيات البحث: {statsMessage}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في عرض إحصائيات البحث: {ex.Message}");
            }
        }

        /// <summary>
        /// إعادة تعيين البحث والفلترة
        /// Reset search and filtering
        /// </summary>
        public void ResetSearch()
        {
            _currentSearchTerm = string.Empty;
            _currentAccountTypeFilter = -1;
            _currentAccountGroupFilter = -1;
            _showActiveOnly = true;
            _showParentAccountsOnly = false;

            _filteredAccounts = new List<ChartOfAccount>(_accounts);
            RefreshAccountsTree();
        }

        /// <summary>
        /// البحث السريع في الحسابات
        /// Quick search in accounts
        /// </summary>
        /// <param name="searchTerm">مصطلح البحث</param>
        public void QuickSearch(string searchTerm)
        {
            ApplyAdvancedSearch(searchTerm, -1, -1, _showActiveOnly, _showParentAccountsOnly);
        }

        /// <summary>
        /// تصدير نتائج البحث
        /// Export search results
        /// </summary>
        public void ExportSearchResults()
        {
            try
            {
                if (_filteredAccounts == null || _filteredAccounts.Count == 0)
                {
                    UIHelper.ShowWarningMessage("لا توجد بيانات للتصدير");
                    return;
                }

                // تصدير البيانات المفلترة
                bool success = ExcelExportHelper.ShowExportDialog(_filteredAccounts);

                if (success)
                {
                    UIHelper.ShowSuccessMessage($"تم تصدير {_filteredAccounts.Count} حساب بنجاح");
                }
            }
            catch (Exception ex)
            {
                UIHelper.ShowErrorMessage($"خطأ في تصدير البيانات: {ex.Message}");
            }
        }

        /// <summary>
        /// تصدير جميع الحسابات
        /// Export all accounts
        /// </summary>
        public void ExportAllAccounts()
        {
            try
            {
                if (_accounts == null || _accounts.Count == 0)
                {
                    UIHelper.ShowWarningMessage("لا توجد بيانات للتصدير");
                    return;
                }

                // تصدير جميع الحسابات
                bool success = ExcelExportHelper.ShowExportDialog(_accounts);

                if (success)
                {
                    UIHelper.ShowSuccessMessage($"تم تصدير {_accounts.Count} حساب بنجاح");
                }
            }
            catch (Exception ex)
            {
                UIHelper.ShowErrorMessage($"خطأ في تصدير البيانات: {ex.Message}");
            }
        }

        /// <summary>
        /// تصدير الحسابات المحددة
        /// Export selected accounts
        /// </summary>
        public void ExportSelectedAccounts()
        {
            try
            {
                var selectedAccounts = new List<ChartOfAccount>();

                // الحصول على الحسابات المحددة من الشجرة
                if (treeViewAccounts.SelectedNode?.Tag is ChartOfAccount selectedAccount)
                {
                    selectedAccounts.Add(selectedAccount);

                    // إضافة الحسابات الفرعية إذا كان الحساب أب
                    if (selectedAccount.IsParent || selectedAccount.IsParentAccount)
                    {
                        var childAccounts = GetChildAccounts(selectedAccount.AccountId);
                        selectedAccounts.AddRange(childAccounts);
                    }
                }

                if (selectedAccounts.Count == 0)
                {
                    UIHelper.ShowWarningMessage("يرجى تحديد حساب للتصدير");
                    return;
                }

                // تصدير الحسابات المحددة
                bool success = ExcelExportHelper.ShowExportDialog(selectedAccounts);

                if (success)
                {
                    UIHelper.ShowSuccessMessage($"تم تصدير {selectedAccounts.Count} حساب بنجاح");
                }
            }
            catch (Exception ex)
            {
                UIHelper.ShowErrorMessage($"خطأ في تصدير البيانات: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على الحسابات الفرعية
        /// Get child accounts
        /// </summary>
        /// <param name="parentAccountId">معرف الحساب الأب</param>
        /// <returns>قائمة الحسابات الفرعية</returns>
        private List<ChartOfAccount> GetChildAccounts(int parentAccountId)
        {
            var childAccounts = new List<ChartOfAccount>();

            try
            {
                var directChildren = _accounts.Where(a => a.ParentAccountId == parentAccountId).ToList();
                childAccounts.AddRange(directChildren);

                // الحصول على الحسابات الفرعية للمستوى التالي
                foreach (var child in directChildren)
                {
                    var grandChildren = GetChildAccounts(child.AccountId);
                    childAccounts.AddRange(grandChildren);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في الحصول على الحسابات الفرعية: {ex.Message}");
            }

            return childAccounts;
        }
        #endregion
    }
}
