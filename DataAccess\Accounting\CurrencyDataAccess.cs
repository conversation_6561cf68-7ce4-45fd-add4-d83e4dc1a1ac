using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using Awqaf_Managment.Models.Accounting;

namespace Awqaf_Managment.DataAccess.Accounting
{
    /// <summary>
    /// طبقة الوصول لبيانات العملات
    /// Currency Data Access Layer
    /// </summary>
    public static class CurrencyDataAccess
    {
        #region Get Operations
        /// <summary>
        /// الحصول على جميع العملات
        /// Get All Currencies
        /// </summary>
        /// <returns>قائمة العملات</returns>
        public static List<Currency> GetAllCurrencies()
        {
            var currencies = new List<Currency>();

            try
            {
                using (var connection = new SqlConnection(DatabaseConnection.ConnectionString))
                {
                    connection.Open();
                    using (var command = new SqlCommand("SP_GetAllCurrencies", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                currencies.Add(MapReaderToCurrency(reader));
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع العملات: {ex.Message}", ex);
            }

            return currencies;
        }

        /// <summary>
        /// الحصول على العملات النشطة فقط
        /// Get Active Currencies Only
        /// </summary>
        /// <returns>قائمة العملات النشطة</returns>
        public static List<Currency> GetActiveCurrencies()
        {
            var currencies = new List<Currency>();

            try
            {
                using (var connection = new SqlConnection(DatabaseConnection.ConnectionString))
                {
                    connection.Open();
                    using (var command = new SqlCommand("SP_GetActiveCurrencies", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                currencies.Add(MapReaderToCurrency(reader));
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع العملات النشطة: {ex.Message}", ex);
            }

            return currencies;
        }

        /// <summary>
        /// الحصول على عملة بالمعرف
        /// Get Currency By ID
        /// </summary>
        /// <param name="currencyId">معرف العملة</param>
        /// <returns>بيانات العملة</returns>
        public static Currency GetCurrencyById(int currencyId)
        {
            try
            {
                using (var connection = new SqlConnection(DatabaseConnection.ConnectionString))
                {
                    connection.Open();
                    using (var command = new SqlCommand("SP_GetCurrencyById", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@CurrencyId", currencyId);

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return MapReaderToCurrency(reader);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع العملة: {ex.Message}", ex);
            }

            return null;
        }

        /// <summary>
        /// الحصول على العملة الأساسية
        /// Get Base Currency
        /// </summary>
        /// <returns>العملة الأساسية</returns>
        public static Currency GetBaseCurrency()
        {
            try
            {
                using (var connection = new SqlConnection(DatabaseConnection.ConnectionString))
                {
                    connection.Open();
                    using (var command = new SqlCommand("SP_GetBaseCurrency", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return MapReaderToCurrency(reader);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع العملة الأساسية: {ex.Message}", ex);
            }

            return null;
        }

        /// <summary>
        /// البحث عن عملة برمز العملة
        /// Get Currency By Code
        /// </summary>
        /// <param name="currencyCode">رمز العملة</param>
        /// <returns>بيانات العملة</returns>
        public static Currency GetCurrencyByCode(string currencyCode)
        {
            try
            {
                using (var connection = new SqlConnection(DatabaseConnection.ConnectionString))
                {
                    connection.Open();
                    using (var command = new SqlCommand("SP_GetCurrencyByCode", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@CurrencyCode", currencyCode);

                        using (var reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                return MapReaderToCurrency(reader);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في البحث عن العملة: {ex.Message}", ex);
            }

            return null;
        }
        #endregion

        #region Add/Update/Delete Operations
        /// <summary>
        /// إضافة عملة جديدة
        /// Add New Currency
        /// </summary>
        /// <param name="currency">بيانات العملة</param>
        /// <returns>معرف العملة الجديد</returns>
        public static int AddCurrency(Currency currency)
        {
            try
            {
                using (var connection = new SqlConnection(DatabaseConnection.ConnectionString))
                {
                    connection.Open();
                    using (var command = new SqlCommand("SP_AddCurrency", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        
                        AddCurrencyParameters(command, currency);
                        
                        var result = command.ExecuteScalar();
                        return Convert.ToInt32(result);
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إضافة العملة: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// تحديث بيانات العملة
        /// Update Currency
        /// </summary>
        /// <param name="currency">بيانات العملة</param>
        /// <returns>true إذا تم التحديث بنجاح</returns>
        public static bool UpdateCurrency(Currency currency)
        {
            try
            {
                using (var connection = new SqlConnection(DatabaseConnection.ConnectionString))
                {
                    connection.Open();
                    using (var command = new SqlCommand("SP_UpdateCurrency", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        
                        command.Parameters.AddWithValue("@CurrencyId", currency.CurrencyId);
                        AddCurrencyParameters(command, currency);
                        
                        int rowsAffected = command.ExecuteNonQuery();
                        return rowsAffected > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث العملة: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// حذف عملة
        /// Delete Currency
        /// </summary>
        /// <param name="currencyId">معرف العملة</param>
        /// <returns>true إذا تم الحذف بنجاح</returns>
        public static bool DeleteCurrency(int currencyId)
        {
            try
            {
                using (var connection = new SqlConnection(DatabaseConnection.ConnectionString))
                {
                    connection.Open();
                    using (var command = new SqlCommand("SP_DeleteCurrency", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@CurrencyId", currencyId);
                        
                        int rowsAffected = command.ExecuteNonQuery();
                        return rowsAffected > 0;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حذف العملة: {ex.Message}", ex);
            }
        }
        #endregion

        #region Helper Methods
        /// <summary>
        /// تحويل قارئ البيانات إلى كائن عملة
        /// Map Data Reader to Currency Object
        /// </summary>
        /// <param name="reader">قارئ البيانات</param>
        /// <returns>كائن العملة</returns>
        private static Currency MapReaderToCurrency(SqlDataReader reader)
        {
            return new Currency
            {
                CurrencyId = Convert.ToInt32(reader["CurrencyId"]),
                CurrencyCode = reader["CurrencyCode"].ToString(),
                CurrencyNameAr = reader["CurrencyNameAr"].ToString(),
                CurrencyNameEn = reader["CurrencyNameEn"]?.ToString() ?? string.Empty,
                Symbol = reader["Symbol"].ToString(),
                ExchangeRate = Convert.ToDecimal(reader["ExchangeRate"]),
                DecimalPlaces = Convert.ToInt32(reader["DecimalPlaces"]),
                IsBaseCurrency = Convert.ToBoolean(reader["IsBaseCurrency"]),
                IsActive = Convert.ToBoolean(reader["IsActive"]),
                CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                ModifiedDate = reader["ModifiedDate"] as DateTime?,
                CreatedBy = reader["CreatedBy"]?.ToString() ?? string.Empty,
                ModifiedBy = reader["ModifiedBy"]?.ToString() ?? string.Empty
            };
        }

        /// <summary>
        /// إضافة معاملات العملة للأمر
        /// Add Currency Parameters to Command
        /// </summary>
        /// <param name="command">أمر SQL</param>
        /// <param name="currency">بيانات العملة</param>
        private static void AddCurrencyParameters(SqlCommand command, Currency currency)
        {
            command.Parameters.AddWithValue("@CurrencyCode", currency.CurrencyCode);
            command.Parameters.AddWithValue("@CurrencyNameAr", currency.CurrencyNameAr);
            command.Parameters.AddWithValue("@CurrencyNameEn", currency.CurrencyNameEn ?? string.Empty);
            command.Parameters.AddWithValue("@Symbol", currency.Symbol);
            command.Parameters.AddWithValue("@ExchangeRate", currency.ExchangeRate);
            command.Parameters.AddWithValue("@DecimalPlaces", currency.DecimalPlaces);
            command.Parameters.AddWithValue("@IsBaseCurrency", currency.IsBaseCurrency);
            command.Parameters.AddWithValue("@IsActive", currency.IsActive);
            command.Parameters.AddWithValue("@CreatedBy", currency.CreatedBy);
            command.Parameters.AddWithValue("@ModifiedBy", currency.ModifiedBy ?? (object)DBNull.Value);
        }
        #endregion
    }
}
