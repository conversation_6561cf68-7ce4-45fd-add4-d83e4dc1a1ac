-- تحديث حقول المستخدم في جداول القيود اليومية
-- Update User Fields in Journal Entry Tables
-- تاريخ الإنشاء: 2025-01-03
-- Creation Date: 2025-01-03

USE AwqafManagement
GO

PRINT 'بدء تحديث حقول المستخدم في جداول القيود اليومية...'
PRINT 'Starting update of user fields in journal entry tables...'

-- تحديث جدول القيود اليومية الرئيسي
-- Update main journal entries table
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[JournalEntries]') AND name = 'CreatedBy' AND system_type_id = 56) -- int type
BEGIN
    PRINT 'تحديث حقول المستخدم في جدول JournalEntries...'
    
    -- إضافة أعمدة مؤقتة
    ALTER TABLE JournalEntries ADD CreatedBy_New NVARCHAR(50) NULL
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[JournalEntries]') AND name = 'ModifiedBy')
        ALTER TABLE JournalEntries ADD ModifiedBy_New NVARCHAR(50) NULL
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[JournalEntries]') AND name = 'ApprovedBy')
        ALTER TABLE JournalEntries ADD ApprovedBy_New NVARCHAR(50) NULL
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[JournalEntries]') AND name = 'PostedBy')
        ALTER TABLE JournalEntries ADD PostedBy_New NVARCHAR(50) NULL

    -- نسخ البيانات مع التحويل
    UPDATE JournalEntries SET CreatedBy_New = CAST(CreatedBy AS NVARCHAR(50))

    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[JournalEntries]') AND name = 'ModifiedBy_New')
        UPDATE JournalEntries SET ModifiedBy_New = CASE WHEN ModifiedBy IS NOT NULL THEN CAST(ModifiedBy AS NVARCHAR(50)) ELSE NULL END

    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[JournalEntries]') AND name = 'ApprovedBy_New')
        UPDATE JournalEntries SET ApprovedBy_New = CASE WHEN ApprovedBy IS NOT NULL THEN CAST(ApprovedBy AS NVARCHAR(50)) ELSE NULL END

    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[JournalEntries]') AND name = 'PostedBy_New')
        UPDATE JournalEntries SET PostedBy_New = CASE WHEN PostedBy IS NOT NULL THEN CAST(PostedBy AS NVARCHAR(50)) ELSE NULL END
    
    -- حذف الأعمدة القديمة
    ALTER TABLE JournalEntries DROP COLUMN CreatedBy
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[JournalEntries]') AND name = 'ModifiedBy')
        ALTER TABLE JournalEntries DROP COLUMN ModifiedBy
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[JournalEntries]') AND name = 'ApprovedBy')
        ALTER TABLE JournalEntries DROP COLUMN ApprovedBy
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[JournalEntries]') AND name = 'PostedBy')
        ALTER TABLE JournalEntries DROP COLUMN PostedBy

    -- إعادة تسمية الأعمدة الجديدة
    EXEC sp_rename 'JournalEntries.CreatedBy_New', 'CreatedBy', 'COLUMN'
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[JournalEntries]') AND name = 'ModifiedBy_New')
        EXEC sp_rename 'JournalEntries.ModifiedBy_New', 'ModifiedBy', 'COLUMN'
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[JournalEntries]') AND name = 'ApprovedBy_New')
        EXEC sp_rename 'JournalEntries.ApprovedBy_New', 'ApprovedBy', 'COLUMN'
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[JournalEntries]') AND name = 'PostedBy_New')
        EXEC sp_rename 'JournalEntries.PostedBy_New', 'PostedBy', 'COLUMN'
    
    -- تعيين القيود
    ALTER TABLE JournalEntries ALTER COLUMN CreatedBy NVARCHAR(50) NOT NULL
    
    PRINT 'تم تحديث جدول JournalEntries بنجاح'
END
ELSE
BEGIN
    PRINT 'جدول JournalEntries محدث مسبقاً'
END

-- تحديث جدول تفاصيل القيود اليومية
-- Update journal entry details table
IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[JournalEntryDetails]') AND name = 'CreatedBy' AND system_type_id = 56) -- int type
BEGIN
    PRINT 'تحديث حقل المستخدم في جدول JournalEntryDetails...'
    
    -- إضافة عمود مؤقت
    ALTER TABLE JournalEntryDetails ADD CreatedBy_New NVARCHAR(50) NULL
    
    -- نسخ البيانات مع التحويل
    UPDATE JournalEntryDetails SET CreatedBy_New = CAST(CreatedBy AS NVARCHAR(50))
    
    -- حذف العمود القديم
    ALTER TABLE JournalEntryDetails DROP COLUMN CreatedBy
    
    -- إعادة تسمية العمود الجديد
    EXEC sp_rename 'JournalEntryDetails.CreatedBy_New', 'CreatedBy', 'COLUMN'
    
    -- تعيين القيود
    ALTER TABLE JournalEntryDetails ALTER COLUMN CreatedBy NVARCHAR(50) NOT NULL
    
    PRINT 'تم تحديث جدول JournalEntryDetails بنجاح'
END
ELSE
BEGIN
    PRINT 'جدول JournalEntryDetails محدث مسبقاً'
END

PRINT 'تم الانتهاء من تحديث حقول المستخدم بنجاح!'
PRINT 'User fields update completed successfully!'
GO
