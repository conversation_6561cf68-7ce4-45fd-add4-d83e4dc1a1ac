using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace Awqaf_Managment.Models.Accounting
{
    /// <summary>
    /// نموذج القيد اليومي
    /// Journal Entry Model
    /// </summary>
    public class JournalEntry
    {
        #region Properties

        /// <summary>
        /// معرف القيد اليومي
        /// Journal Entry ID
        /// </summary>
        public int JournalEntryId { get; set; }

        /// <summary>
        /// رقم القيد
        /// Journal Number
        /// </summary>
        [Required]
        [StringLength(50)]
        public string JournalNumber { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ القيد
        /// Journal Date
        /// </summary>
        [Required]
        public DateTime JournalDate { get; set; } = DateTime.Now;

        /// <summary>
        /// نوع القيد
        /// Journal Type
        /// </summary>
        [Required]
        public JournalType JournalType { get; set; } = JournalType.Normal;

        /// <summary>
        /// حالة القيد
        /// Journal Status
        /// </summary>
        [Required]
        public JournalStatus Status { get; set; } = JournalStatus.Draft;

        /// <summary>
        /// البيان العام
        /// General Description
        /// </summary>
        [StringLength(500)]
        public string GeneralDescription { get; set; } = string.Empty;

        /// <summary>
        /// معرف مركز التكلفة
        /// Cost Center ID
        /// </summary>
        public int? CostCenterId { get; set; }

        /// <summary>
        /// إجمالي المدين
        /// Total Debit
        /// </summary>
        public decimal TotalDebit { get; set; } = 0;

        /// <summary>
        /// إجمالي الدائن
        /// Total Credit
        /// </summary>
        public decimal TotalCredit { get; set; } = 0;

        /// <summary>
        /// اسم المستخدم المنشئ
        /// Created By User Name
        /// </summary>
        public string CreatedBy { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ الإنشاء
        /// Created Date
        /// </summary>
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        /// <summary>
        /// اسم المستخدم المعدل
        /// Modified By User Name
        /// </summary>
        public string ModifiedBy { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ التعديل
        /// Modified Date
        /// </summary>
        public DateTime? ModifiedDate { get; set; }

        /// <summary>
        /// اسم المستخدم المعتمد
        /// Approved By User Name
        /// </summary>
        public string ApprovedBy { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ الاعتماد
        /// Approved Date
        /// </summary>
        public DateTime? ApprovedDate { get; set; }

        /// <summary>
        /// اسم المستخدم المرحل
        /// Posted By User Name
        /// </summary>
        public string PostedBy { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ الترحيل
        /// Posted Date
        /// </summary>
        public DateTime? PostedDate { get; set; }

        /// <summary>
        /// ملاحظات
        /// Notes
        /// </summary>
        [StringLength(1000)]
        public string Notes { get; set; } = string.Empty;

        /// <summary>
        /// تفاصيل القيد
        /// Journal Entry Details
        /// </summary>
        public List<JournalEntryDetail> JournalEntryDetails { get; set; } = new List<JournalEntryDetail>();

        #endregion

        #region Constructors

        /// <summary>
        /// منشئ افتراضي
        /// Default Constructor
        /// </summary>
        public JournalEntry()
        {
            JournalDate = DateTime.Now;
            CreatedDate = DateTime.Now;
            JournalType = JournalType.Normal;
            Status = JournalStatus.Draft;
            JournalEntryDetails = new List<JournalEntryDetail>();
        }

        #endregion

        #region Methods

        /// <summary>
        /// التحقق من توازن القيد
        /// Check if journal entry is balanced
        /// </summary>
        /// <returns>true إذا كان القيد متوازن</returns>
        public bool IsBalanced()
        {
            return Math.Abs(TotalDebit - TotalCredit) < 0.01m;
        }

        /// <summary>
        /// حساب إجمالي المدين والدائن
        /// Calculate total debit and credit
        /// </summary>
        public void CalculateTotals()
        {
            TotalDebit = JournalEntryDetails?.Sum(d => d.DebitAmount) ?? 0;
            TotalCredit = JournalEntryDetails?.Sum(d => d.CreditAmount) ?? 0;
        }

        /// <summary>
        /// التحقق من صحة القيد
        /// Validate journal entry
        /// </summary>
        /// <returns>قائمة برسائل الخطأ</returns>
        public List<string> Validate()
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(JournalNumber))
                errors.Add("رقم القيد مطلوب");

            if (JournalDate == default(DateTime))
                errors.Add("تاريخ القيد مطلوب");

            if (JournalEntryDetails == null || !JournalEntryDetails.Any())
                errors.Add("يجب إدخال تفاصيل القيد");

            if (JournalEntryDetails?.Count < 2)
                errors.Add("يجب أن يحتوي القيد على حسابين على الأقل");

            CalculateTotals();
            if (!IsBalanced())
                errors.Add("القيد غير متوازن - إجمالي المدين يجب أن يساوي إجمالي الدائن");

            return errors;
        }

        /// <summary>
        /// تمثيل نصي للقيد
        /// String representation
        /// </summary>
        /// <returns>النص الممثل للقيد</returns>
        public override string ToString()
        {
             return $"{JournalNumber} - {JournalDate:yyyy/MM/dd}  ";
        }

        #endregion
    }
}
