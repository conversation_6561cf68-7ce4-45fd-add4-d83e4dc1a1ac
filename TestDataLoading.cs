using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using Awqaf_Managment.Services.Accounting;
using Awqaf_Managment.Models.Accounting;

namespace Awqaf_Managment
{
    /// <summary>
    /// اختبار تحميل البيانات
    /// Test Data Loading
    /// </summary>
    public partial class TestDataLoadingForm : Form
    {
        private List<ChartOfAccount> _accounts;
        private TreeView treeTest;
        private TextBox txtResults;
        private Button btnTest;

        public TestDataLoadingForm()
        {
            InitializeComponent();
            SetupForm();
        }

        private void InitializeComponent()
        {
            this.treeTest = new TreeView();
            this.txtResults = new TextBox();
            this.btnTest = new Button();
            this.SuspendLayout();

            // treeTest
            this.treeTest.Location = new System.Drawing.Point(12, 12);
            this.treeTest.Name = "treeTest";
            this.treeTest.Size = new System.Drawing.Size(300, 400);
            this.treeTest.TabIndex = 0;
            this.treeTest.RightToLeft = RightToLeft.Yes;
            this.treeTest.RightToLeftLayout = true;

            // txtResults
            this.txtResults.Location = new System.Drawing.Point(330, 12);
            this.txtResults.Multiline = true;
            this.txtResults.Name = "txtResults";
            this.txtResults.ScrollBars = ScrollBars.Vertical;
            this.txtResults.Size = new System.Drawing.Size(400, 350);
            this.txtResults.TabIndex = 1;
            this.txtResults.Font = new System.Drawing.Font("Tahoma", 9F);

            // btnTest
            this.btnTest.Location = new System.Drawing.Point(330, 380);
            this.btnTest.Name = "btnTest";
            this.btnTest.Size = new System.Drawing.Size(100, 30);
            this.btnTest.TabIndex = 2;
            this.btnTest.Text = "اختبار البيانات";
            this.btnTest.UseVisualStyleBackColor = true;
            this.btnTest.Click += new EventHandler(this.btnTest_Click);

            // TestDataLoadingForm
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(750, 430);
            this.Controls.Add(this.btnTest);
            this.Controls.Add(this.txtResults);
            this.Controls.Add(this.treeTest);
            this.Name = "TestDataLoadingForm";
            this.Text = "اختبار تحميل البيانات - Test Data Loading";
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void SetupForm()
        {
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Normal;
        }

        private void btnTest_Click(object sender, EventArgs e)
        {
            TestDataLoading();
        }

        private void TestDataLoading()
        {
            try
            {
                txtResults.Clear();
                treeTest.Nodes.Clear();

                txtResults.AppendText("=== بدء اختبار تحميل البيانات ===\r\n");
                txtResults.AppendText("=== Starting Data Loading Test ===\r\n\r\n");

                // تحميل البيانات
                txtResults.AppendText("1. تحميل الحسابات من قاعدة البيانات...\r\n");
                _accounts = ChartOfAccountsService.GetAllAccounts();

                txtResults.AppendText($"   عدد الحسابات المحملة: {_accounts?.Count ?? 0}\r\n\r\n");

                if (_accounts == null || _accounts.Count == 0)
                {
                    txtResults.AppendText("❌ لا توجد حسابات محملة!\r\n");
                    return;
                }

                // عرض تفاصيل الحسابات
                txtResults.AppendText("2. تفاصيل الحسابات المحملة:\r\n");
                foreach (var account in _accounts.OrderBy(a => a.AccountCode))
                {
                    txtResults.AppendText($"   {account.AccountCode} - {account.AccountNameAr} (المستوى: {account.AccountLevel})\r\n");
                }
                txtResults.AppendText("\r\n");

                // تحميل الشجرة
                txtResults.AppendText("3. تحميل شجرة الحسابات...\r\n");
                LoadAccountsTree();

                txtResults.AppendText($"   عدد العقد في الشجرة: {treeTest.Nodes.Count}\r\n");
                txtResults.AppendText("✓ تم تحميل الشجرة بنجاح\r\n\r\n");

                // إحصائيات
                var mainAccounts = _accounts.Where(a => a.ParentAccountId == null || a.ParentAccountId == 0).Count();
                var subAccounts = _accounts.Where(a => a.ParentAccountId != null && a.ParentAccountId > 0).Count();

                txtResults.AppendText("4. الإحصائيات:\r\n");
                txtResults.AppendText($"   الحسابات الرئيسية: {mainAccounts}\r\n");
                txtResults.AppendText($"   الحسابات الفرعية: {subAccounts}\r\n");
                txtResults.AppendText($"   إجمالي الحسابات: {_accounts.Count}\r\n\r\n");

                txtResults.AppendText("=== انتهى الاختبار بنجاح ===\r\n");
                txtResults.AppendText("=== Test Completed Successfully ===\r\n");
            }
            catch (Exception ex)
            {
                txtResults.AppendText($"❌ خطأ في الاختبار: {ex.Message}\r\n");
                txtResults.AppendText($"تفاصيل الخطأ: {ex.StackTrace}\r\n");
            }
        }

        private void LoadAccountsTree()
        {
            try
            {
                treeTest.Nodes.Clear();

                // إضافة الحسابات الرئيسية
                var mainAccounts = _accounts.Where(a => a.ParentAccountId == null || a.ParentAccountId == 0)
                                          .OrderBy(a => a.AccountCode)
                                          .ToList();

                foreach (var account in mainAccounts)
                {
                    var node = CreateAccountNode(account);
                    treeTest.Nodes.Add(node);
                    LoadChildAccounts(node, account.AccountId);
                }

                treeTest.ExpandAll();
            }
            catch (Exception ex)
            {
                txtResults.AppendText($"خطأ في تحميل الشجرة: {ex.Message}\r\n");
            }
        }

        private TreeNode CreateAccountNode(ChartOfAccount account)
        {
            var displayText = $"{account.AccountCode} - {account.AccountNameAr}";
            var node = new TreeNode(displayText)
            {
                Tag = account,
                ForeColor = account.IsParent ? System.Drawing.Color.Blue : System.Drawing.Color.Black
            };
            return node;
        }

        private void LoadChildAccounts(TreeNode parentNode, int parentAccountId)
        {
            var childAccounts = _accounts.Where(a => a.ParentAccountId == parentAccountId)
                                       .OrderBy(a => a.AccountCode)
                                       .ToList();

            foreach (var childAccount in childAccounts)
            {
                var childNode = CreateAccountNode(childAccount);
                parentNode.Nodes.Add(childNode);
                LoadChildAccounts(childNode, childAccount.AccountId);
            }
        }
    }

    // برنامج اختبار مستقل
    public static class TestProgram
    {
        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new TestDataLoadingForm());
        }
    }
}
